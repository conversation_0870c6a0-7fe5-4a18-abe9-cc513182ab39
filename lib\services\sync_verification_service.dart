import 'package:shared_preferences/shared_preferences.dart';
import 'firebase_service.dart';
import 'logging_service.dart';

/// Service pour vérifier que vos données mobiles sont bien synchronisées
class SyncVerificationService {
  static final SyncVerificationService _instance =
      SyncVerificationService._internal();
  factory SyncVerificationService() => _instance;
  SyncVerificationService._internal();

  /// Vérifier que toutes vos données mobiles sont bien synchronisées
  Future<SyncVerificationReport> verifyDataIntegrity() async {
    LoggingService.info(
      '🔍 Vérification de l\'intégrité des données...',
      'VERIFICATION',
    );

    final report = SyncVerificationReport();

    try {
      // 1. Vérifier les données locales (SharedPreferences)
      await _verifyLocalData(report);

      // 2. Vérifier les données Firebase
      await _verifyFirebaseData(report);

      // 4. Comparer et générer le rapport
      _generateComparisonReport(report);

      LoggingService.success('✅ Vérification terminée', 'VERIFICATION');
    } catch (e) {
      LoggingService.error(
        '❌ Erreur lors de la vérification',
        'VERIFICATION',
        e,
      );
      report.hasErrors = true;
      report.errors.add('Erreur générale: $e');
    }

    return report;
  }

  /// Vérifier les données locales (vos données mobiles existantes)
  Future<void> _verifyLocalData(SyncVerificationReport report) async {
    LoggingService.info(
      '📱 Vérification des données locales...',
      'VERIFICATION',
    );

    try {
      final prefs = await SharedPreferences.getInstance();

      // Compter les factures locales
      final invoicesJson = prefs.getStringList('invoices') ?? [];
      report.localInvoicesCount = invoicesJson.length;

      // Compter les produits locaux
      final productsJson = prefs.getStringList('products') ?? [];
      report.localProductsCount = productsJson.length;

      // Compter les colis locaux
      final colisJson = prefs.getStringList('colis') ?? [];
      report.localColisCount = colisJson.length;

      // Compter les tâches locales
      final tasksJson = prefs.getStringList('tasks') ?? [];
      report.localTasksCount = tasksJson.length;

      // Compter les catégories locales
      final categoriesJson = prefs.getStringList('categories') ?? [];
      report.localCategoriesCount = categoriesJson.length;

      LoggingService.info(
        '📊 Données locales: ${report.localInvoicesCount} factures, '
            '${report.localProductsCount} produits, ${report.localColisCount} colis, '
            '${report.localTasksCount} tâches, ${report.localCategoriesCount} catégories',
        'VERIFICATION',
      );
    } catch (e) {
      LoggingService.error('❌ Erreur vérification locale', 'VERIFICATION', e);
      report.errors.add('Erreur données locales: $e');
    }
  }



  /// Vérifier les données Firebase
  Future<void> _verifyFirebaseData(SyncVerificationReport report) async {
    LoggingService.info(
      '🔥 Vérification des données Firebase...',
      'VERIFICATION',
    );

    try {
      final firebaseService = FirebaseService.instance;
      await firebaseService.initialize();

      if (firebaseService.isOnline()) {
        // Compter les factures Firebase
        final firebaseInvoices = await firebaseService.getAllInvoices();
        report.firebaseInvoicesCount = firebaseInvoices.length;

        // Compter les tâches Firebase
        final firebaseTasks = await firebaseService.getAllTasks();
        report.firebaseTasksCount = firebaseTasks.length;

        // Compter les colis Firebase
        final firebaseColis = await firebaseService.getAllColis();
        report.firebaseColisCount = firebaseColis.length;

        LoggingService.info(
          '📊 Données Firebase: ${report.firebaseInvoicesCount} factures, '
              '${report.firebaseTasksCount} tâches, ${report.firebaseColisCount} colis',
          'VERIFICATION',
        );
      } else {
        LoggingService.warning('⚠️ Firebase hors ligne', 'VERIFICATION');
        report.warnings.add('Firebase non disponible pour vérification');
      }
    } catch (e) {
      LoggingService.warning(
        '⚠️ Erreur vérification Firebase (ignorée)',
        'VERIFICATION',
      );
      report.warnings.add('Firebase non accessible: $e');
    }
  }

  /// Générer le rapport de comparaison
  void _generateComparisonReport(SyncVerificationReport report) {
    LoggingService.info(
      '📋 Génération du rapport de comparaison...',
      'VERIFICATION',
    );

    // Vérifier si les données locales sont synchronisées avec Firebase
    if (report.localInvoicesCount > 0 && report.firebaseInvoicesCount == 0) {
      report.warnings.add('Factures locales non synchronisées avec Firebase');
    }

    if (report.localColisCount > 0 && report.firebaseColisCount == 0) {
      report.warnings.add('Colis locaux non synchronisés avec Firebase');
    }

    if (report.localTasksCount > 0 && report.firebaseTasksCount == 0) {
      report.warnings.add('Tâches locales non synchronisées avec Firebase');
    }

    // Calculer le pourcentage de synchronisation
    final totalLocal =
        report.localInvoicesCount +
        report.localProductsCount +
        report.localColisCount +
        report.localTasksCount +
        report.localCategoriesCount;

    final totalFirebase =
        report.firebaseInvoicesCount +
        report.firebaseTasksCount +
        report.firebaseColisCount;

    if (totalLocal > 0) {
      // Pour Firebase, on ne synchronise que certains types de données
      final syncableLocal = report.localInvoicesCount + report.localColisCount + report.localTasksCount;
      if (syncableLocal > 0) {
        report.syncPercentage = (totalFirebase / syncableLocal * 100).clamp(0, 100);
      } else {
        report.syncPercentage = 100;
      }
    } else {
      report.syncPercentage = 100; // Pas de données locales = 100% synchronisé
    }

    // Déterminer le statut global
    if (report.errors.isNotEmpty) {
      report.status = SyncStatus.error;
    } else if (report.syncPercentage < 50) {
      report.status = SyncStatus.critical;
    } else if (report.syncPercentage < 90) {
      report.status = SyncStatus.warning;
    } else {
      report.status = SyncStatus.success;
    }

    LoggingService.info(
      '📊 Synchronisation: ${report.syncPercentage.toStringAsFixed(1)}% - Statut: ${report.status}',
      'VERIFICATION',
    );
  }

  /// Afficher un rapport détaillé dans la console
  void printDetailedReport(SyncVerificationReport report) {
    print('\n${'=' * 60}');
    print('📋 RAPPORT DE SYNCHRONISATION DES DONNÉES');
    print('=' * 60);
    print('📱 DONNÉES LOCALES (Mobile):');
    print('   • Factures: ${report.localInvoicesCount}');
    print('   • Produits: ${report.localProductsCount}');
    print('   • Colis: ${report.localColisCount}');
    print('   • Tâches: ${report.localTasksCount}');
    print('   • Catégories: ${report.localCategoriesCount}');
    print('');
    print('🔥 DONNÉES FIREBASE (Cloud):');
    print('   • Factures: ${report.firebaseInvoicesCount}');
    print('   • Tâches: ${report.firebaseTasksCount}');
    print('   • Colis: ${report.firebaseColisCount}');
    print('');
    print('📊 RÉSUMÉ:');
    print('   • Synchronisation: ${report.syncPercentage.toStringAsFixed(1)}%');
    print('   • Statut: ${report.status}');
    print('');
    if (report.warnings.isNotEmpty) {
      print('⚠️ AVERTISSEMENTS:');
      for (final warning in report.warnings) {
        print('   • $warning');
      }
      print('');
    }
    if (report.errors.isNotEmpty) {
      print('❌ ERREURS:');
      for (final error in report.errors) {
        print('   • $error');
      }
      print('');
    }
    print('=' * 60);
  }
}

/// Rapport de vérification de synchronisation
class SyncVerificationReport {
  // Données locales
  int localInvoicesCount = 0;
  int localProductsCount = 0;
  int localColisCount = 0;
  int localTasksCount = 0;
  int localCategoriesCount = 0;

  // Données Firebase
  int firebaseInvoicesCount = 0;
  int firebaseTasksCount = 0;
  int firebaseColisCount = 0;

  // Statut
  SyncStatus status = SyncStatus.unknown;
  double syncPercentage = 0.0;
  bool hasErrors = false;

  // Messages
  List<String> warnings = [];
  List<String> errors = [];
}

/// Statut de synchronisation
enum SyncStatus { unknown, success, warning, critical, error }

extension SyncStatusExtension on SyncStatus {
  String get displayName {
    switch (this) {
      case SyncStatus.success:
        return '✅ Succès';
      case SyncStatus.warning:
        return '⚠️ Avertissement';
      case SyncStatus.critical:
        return '🚨 Critique';
      case SyncStatus.error:
        return '❌ Erreur';
      case SyncStatus.unknown:
        return '❓ Inconnu';
    }
  }
}
