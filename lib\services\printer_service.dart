import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'audio_notification_service.dart';

/// Modèle pour représenter une imprimante découverte
class DiscoveredPrinter {
  final String id;
  final String name;
  final String address;
  final PrinterType type;
  final bool isConnected;
  final int? rssi; // Signal strength for Bluetooth

  DiscoveredPrinter({
    String? id,
    required this.name,
    required this.address,
    required this.type,
    this.isConnected = false,
    this.rssi,
  }) : id = id ?? address; // Use address as default id

  // Factory constructor from BluetoothInfo
  factory DiscoveredPrinter.fromBluetoothInfo(BluetoothInfo info) {
    return DiscoveredPrinter(
      id: info.macAdress,
      name: info.name,
      address: info.macAdress,
      type: PrinterType.bluetooth,
    );
  }

  /// Crée une copie de cette instance avec les valeurs modifiées
  DiscoveredPrinter copyWith({
    String? id,
    String? name,
    String? address,
    PrinterType? type,
    bool? isConnected,
    int? rssi,
  }) {
    return DiscoveredPrinter(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      type: type ?? this.type,
      isConnected: isConnected ?? this.isConnected,
      rssi: rssi ?? this.rssi,
    );
  }

  @override
  String toString() {
    return 'DiscoveredPrinter(id: $id, name: $name, address: $address, type: $type, connected: $isConnected)';
  }
}

/// Types d'imprimantes supportées
enum PrinterType { bluetooth, wifi, usb }

/// États de connexion de l'imprimante
enum PrinterConnectionStatus { disconnected, connecting, connected, error }

/// Service principal pour la gestion des imprimantes thermiques ESC/POS
class PrinterService {
  static final PrinterService _instance = PrinterService._internal();
  factory PrinterService() => _instance;
  PrinterService._internal();

  // Services
  final AudioNotificationService _audioService = AudioNotificationService();

  // État du service
  PrinterConnectionStatus _connectionStatus =
      PrinterConnectionStatus.disconnected;
  DiscoveredPrinter? _connectedPrinter;
  final List<DiscoveredPrinter> _discoveredPrinters = [];

  // Streams pour notifier les changements d'état
  final StreamController<PrinterConnectionStatus> _statusController =
      StreamController<PrinterConnectionStatus>.broadcast();
  final StreamController<List<DiscoveredPrinter>> _printersController =
      StreamController<List<DiscoveredPrinter>>.broadcast();

  // Getters publics
  PrinterConnectionStatus get connectionStatus => _connectionStatus;
  DiscoveredPrinter? get connectedPrinter => _connectedPrinter;
  List<DiscoveredPrinter> get discoveredPrinters =>
      List.unmodifiable(_discoveredPrinters);

  // Streams publics
  Stream<PrinterConnectionStatus> get statusStream => _statusController.stream;
  Stream<List<DiscoveredPrinter>> get printersStream =>
      _printersController.stream;

  /// Initialise le service et vérifie les permissions
  Future<bool> initialize() async {
    try {
      debugPrint('🖨️ Initialisation du PrinterService...');

      // Initialiser le service audio
      await _audioService.initialize();

      // Vérifier et demander les permissions Bluetooth
      final hasPermissions = await _checkAndRequestPermissions();
      if (!hasPermissions) {
        debugPrint('❌ Permissions Bluetooth refusées');
        return false;
      }

      // Initialiser Bluetooth
      await _initializeBluetooth();

      debugPrint('✅ PrinterService initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'initialisation du PrinterService: $e');
      return false;
    }
  }

  /// Vérifie et demande les permissions nécessaires
  Future<bool> _checkAndRequestPermissions() async {
    try {
      // Permissions pour Android 12+
      final permissions = [
        Permission.bluetooth,
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.bluetoothAdvertise,
        Permission.location,
      ];

      Map<Permission, PermissionStatus> statuses = await permissions.request();

      // Vérifier si toutes les permissions sont accordées
      bool allGranted = true;
      for (var permission in permissions) {
        final status = statuses[permission];
        if (status != PermissionStatus.granted) {
          debugPrint('❌ Permission refusée: $permission - Status: $status');
          allGranted = false;
        }
      }

      return allGranted;
    } catch (e) {
      debugPrint('❌ Erreur lors de la vérification des permissions: $e');
      return false;
    }
  }

  /// Initialise le Bluetooth
  Future<void> _initializeBluetooth() async {
    try {
      // Vérifier si Bluetooth est activé
      bool isEnabled = await PrintBluetoothThermal.bluetoothEnabled;
      if (!isEnabled) {
        debugPrint('❌ Bluetooth non activé');
        throw Exception('Bluetooth non activé');
      }

      debugPrint('✅ Bluetooth initialisé');
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'initialisation Bluetooth: $e');
      rethrow;
    }
  }

  /// Scanne les imprimantes disponibles
  Future<List<DiscoveredPrinter>> scan({PrinterType? type}) async {
    try {
      debugPrint('🔍 Début du scan des imprimantes...');
      _updateConnectionStatus(PrinterConnectionStatus.connecting);

      _discoveredPrinters.clear();

      // Scanner les imprimantes Bluetooth si demandé ou par défaut
      if (type == null || type == PrinterType.bluetooth) {
        await _scanBluetoothPrinters();
      }

      // Scanner les imprimantes WiFi si demandé
      if (type == null || type == PrinterType.wifi) {
        await _scanWifiPrinters();
      }

      _updateConnectionStatus(PrinterConnectionStatus.disconnected);
      _printersController.add(List.from(_discoveredPrinters));

      debugPrint(
        '✅ Scan terminé: ${_discoveredPrinters.length} imprimantes trouvées',
      );
      return List.from(_discoveredPrinters);
    } catch (e) {
      debugPrint('❌ Erreur lors du scan: $e');
      _updateConnectionStatus(PrinterConnectionStatus.error);
      return [];
    }
  }

  /// Scanne les imprimantes Bluetooth
  Future<void> _scanBluetoothPrinters() async {
    try {
      debugPrint('🔍 Scan Bluetooth en cours...');

      // Vérifier si Bluetooth est activé
      bool isBluetoothEnabled = await PrintBluetoothThermal.bluetoothEnabled;
      if (!isBluetoothEnabled) {
        debugPrint('❌ Bluetooth non activé');
        return;
      }

      // Obtenir les appareils Bluetooth appairés
      List<BluetoothInfo> pairedDevices =
          await PrintBluetoothThermal.pairedBluetooths;

      for (BluetoothInfo device in pairedDevices) {
        final printer = DiscoveredPrinter.fromBluetoothInfo(device);

        // Éviter les doublons
        if (!_discoveredPrinters.any((p) => p.address == printer.address)) {
          _discoveredPrinters.add(printer);
          debugPrint(
            '📱 Imprimante Bluetooth trouvée: ${printer.name} (${printer.address})',
          );
        }
      }

      debugPrint('✅ Scan Bluetooth terminé');
    } catch (e) {
      debugPrint('❌ Erreur scan Bluetooth: $e');
    }
  }

  /// Scanne les imprimantes WiFi/TCP
  Future<void> _scanWifiPrinters() async {
    try {
      debugPrint('🔍 Scan WiFi en cours...');

      // Pour les imprimantes WiFi, on peut scanner le réseau local
      // ou permettre à l'utilisateur d'entrer manuellement l'IP
      // Ici, on ajoute quelques exemples d'IPs communes
      final commonIPs = [
        '*************',
        '*************',
        '*************',
        '*************',
      ];

      for (String ip in commonIPs) {
        // Test de connexion rapide pour voir si une imprimante répond
        try {
          // Simuler un test de connexion (à implémenter selon les besoins)
          final printer = DiscoveredPrinter(
            name: 'Imprimante WiFi ($ip)',
            address: '$ip:9100', // Port standard ESC/POS
            type: PrinterType.wifi,
            isConnected: false,
          );

          // Pour l'instant, on ajoute toutes les IPs comme potentielles
          // Dans une vraie implémentation, on testerait la connexion
          _discoveredPrinters.add(printer);
          debugPrint('🌐 Imprimante WiFi potentielle: $ip');
        } catch (e) {
          // Ignorer les erreurs de connexion pour le scan
        }
      }

      debugPrint('✅ Scan WiFi terminé');
    } catch (e) {
      debugPrint('❌ Erreur scan WiFi: $e');
    }
  }

  /// Se connecte à une imprimante
  Future<bool> connect(DiscoveredPrinter printer) async {
    try {
      debugPrint('🔗 Connexion à l\'imprimante: ${printer.name}');
      _updateConnectionStatus(PrinterConnectionStatus.connecting);

      bool success = false;

      switch (printer.type) {
        case PrinterType.bluetooth:
          success = await _connectBluetooth(printer);
          break;
        case PrinterType.wifi:
          success = await _connectWifi(printer);
          break;
        case PrinterType.usb:
          success = await _connectUsb(printer);
          break;
      }

      if (success) {
        _connectedPrinter = printer;
        _updateConnectionStatus(PrinterConnectionStatus.connected);
        debugPrint('✅ Connexion réussie à ${printer.name}');

        // Jouer le son de connexion réussie
        await _audioService.playConnectionSuccess();
      } else {
        _updateConnectionStatus(PrinterConnectionStatus.error);
        debugPrint('❌ Échec de connexion à ${printer.name}');

        // Jouer le son d'erreur
        await _audioService.playError();
      }

      return success;
    } catch (e) {
      debugPrint('❌ Erreur lors de la connexion: $e');
      _updateConnectionStatus(PrinterConnectionStatus.error);
      return false;
    }
  }

  /// Connexion Bluetooth
  Future<bool> _connectBluetooth(DiscoveredPrinter printer) async {
    try {
      debugPrint(
        '🔗 Tentative de connexion Bluetooth à: ${printer.name} (${printer.address})',
      );

      // Vérifier d'abord si Bluetooth est activé
      bool isBluetoothEnabled = await PrintBluetoothThermal.bluetoothEnabled;
      if (!isBluetoothEnabled) {
        debugPrint('❌ Bluetooth désactivé');
        throw Exception('Bluetooth désactivé. Veuillez l\'activer.');
      }

      // Déconnecter toute connexion existante
      try {
        await PrintBluetoothThermal.disconnect;
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        debugPrint('⚠️ Erreur lors de la déconnexion: $e');
      }

      // Tentative de connexion avec timeout
      bool connected = false;
      int attempts = 0;
      const maxAttempts = 3;

      while (!connected && attempts < maxAttempts) {
        attempts++;
        debugPrint('🔄 Tentative $attempts/$maxAttempts pour ${printer.name}');

        try {
          connected = await PrintBluetoothThermal.connect(
            macPrinterAddress: printer.address,
          ).timeout(
            const Duration(seconds: 15), // Augmenter le timeout
            onTimeout: () {
              debugPrint('⏰ Timeout de connexion pour ${printer.name}');
              return false;
            },
          );

          if (connected) {
            debugPrint(
              '✅ Connexion réussie à ${printer.name} (tentative $attempts)',
            );

            // Test de communication
            await Future.delayed(const Duration(milliseconds: 500));
            bool isReady = await _testPrinterConnection();

            if (isReady) {
              debugPrint('✅ Imprimante prête: ${printer.name}');
              return true;
            } else {
              debugPrint(
                '⚠️ Imprimante connectée mais non prête: ${printer.name}',
              );
              await PrintBluetoothThermal.disconnect;
              connected = false;
            }
          } else {
            debugPrint(
              '❌ Échec connexion tentative $attempts: ${printer.name}',
            );
            if (attempts < maxAttempts) {
              await Future.delayed(const Duration(seconds: 2));
            }
          }
        } catch (e) {
          debugPrint('❌ Erreur tentative $attempts: $e');
          if (attempts < maxAttempts) {
            await Future.delayed(const Duration(seconds: 2));
          }
        }
      }

      if (!connected) {
        throw Exception(
          'Impossible de se connecter à ${printer.name} après $maxAttempts tentatives',
        );
      }

      return false;
    } catch (e) {
      debugPrint('❌ Erreur connexion Bluetooth: $e');
      return false;
    }
  }

  /// Test la connexion avec l'imprimante
  Future<bool> _testPrinterConnection() async {
    try {
      // Envoyer une commande de test simple (initialisation ESC/POS)
      final testCommand = Uint8List.fromList([
        0x1B,
        0x40,
      ]); // ESC @ (Initialize)
      await PrintBluetoothThermal.writeBytes(testCommand);

      // Attendre un peu pour la réponse
      await Future.delayed(const Duration(milliseconds: 500));

      return true;
    } catch (e) {
      debugPrint('❌ Test de connexion échoué: $e');
      return false;
    }
  }

  /// Test rapide de connectivité sans établir une connexion complète
  Future<bool> testPrinterConnectivity(DiscoveredPrinter printer) async {
    try {
      if (printer.type == PrinterType.bluetooth) {
        // Pour Bluetooth, vérifier si l'appareil est appairé et disponible
        List<BluetoothInfo> pairedDevices =
            await PrintBluetoothThermal.pairedBluetooths;
        bool isPaired = pairedDevices.any(
          (device) =>
              device.macAdress == printer.address ||
              device.name == printer.name,
        );
        return isPaired;
      } else if (printer.type == PrinterType.wifi) {
        // Pour WiFi, faire un ping rapide
        return await _pingWifiPrinter(printer.address);
      }
      return false;
    } catch (e) {
      debugPrint('❌ Test de connectivité échoué pour ${printer.name}: $e');
      return false;
    }
  }

  Future<bool> _pingWifiPrinter(String address) async {
    try {
      // Simuler un test de connectivité WiFi
      // Dans une vraie implémentation, on pourrait utiliser un socket ou HTTP request
      await Future.delayed(const Duration(milliseconds: 100));
      return true; // Simuler que l'imprimante répond
    } catch (e) {
      return false;
    }
  }

  /// Connexion WiFi/TCP
  Future<bool> _connectWifi(DiscoveredPrinter printer) async {
    try {
      // WiFi connection logic can be implemented later
      // For now, return false as WiFi is not implemented
      debugPrint('⚠️ WiFi connection not implemented yet');
      return false;
    } catch (e) {
      debugPrint('❌ Erreur connexion WiFi: $e');
      return false;
    }
  }

  /// Connexion USB (pour les appareils supportés)
  Future<bool> _connectUsb(DiscoveredPrinter printer) async {
    try {
      // Implémentation USB si nécessaire
      debugPrint('⚠️ Connexion USB non implémentée');
      return false;
    } catch (e) {
      debugPrint('❌ Erreur connexion USB: $e');
      return false;
    }
  }

  /// Se déconnecte de l'imprimante actuelle
  Future<bool> disconnect() async {
    try {
      if (_connectedPrinter == null) {
        debugPrint('⚠️ Aucune imprimante connectée');
        return true;
      }

      debugPrint('🔌 Déconnexion de ${_connectedPrinter!.name}');

      bool success = false;

      switch (_connectedPrinter!.type) {
        case PrinterType.bluetooth:
          await PrintBluetoothThermal.disconnect;
          success = true;
          break;
        case PrinterType.wifi:
          // WiFi disconnect logic can be implemented later
          success = true;
          break;
        case PrinterType.usb:
          success = true;
          break;
      }

      if (success) {
        _connectedPrinter = null;
        _updateConnectionStatus(PrinterConnectionStatus.disconnected);
        debugPrint('✅ Déconnexion réussie');

        // Jouer le son de déconnexion
        await _audioService.playDisconnection();
      }

      return success;
    } catch (e) {
      debugPrint('❌ Erreur lors de la déconnexion: $e');
      return false;
    }
  }

  /// Imprime des données brutes (bytes)
  Future<bool> printBytes(Uint8List bytes) async {
    try {
      if (_connectedPrinter == null) {
        debugPrint('❌ Aucune imprimante connectée');
        throw Exception('Aucune imprimante connectée');
      }

      if (_connectionStatus != PrinterConnectionStatus.connected) {
        debugPrint('❌ Imprimante non connectée');
        throw Exception('Imprimante non connectée');
      }

      debugPrint('🖨️ === DÉBUT IMPRESSION ===');
      debugPrint('🖨️ Imprimante: ${_connectedPrinter!.name}');
      debugPrint('🖨️ Données: ${bytes.length} bytes');
      debugPrint('🖨️ Type: ${_connectedPrinter!.type}');

      bool success = false;

      switch (_connectedPrinter!.type) {
        case PrinterType.bluetooth:
          // VÉRIFICATIONS PRÉLIMINAIRES RENFORCÉES
          debugPrint('🔍 Vérifications préliminaires...');

          // 1. Vérifier la connexion Bluetooth
          bool isConnected = await PrintBluetoothThermal.connectionStatus;
          debugPrint('📡 Statut Bluetooth: $isConnected');

          if (!isConnected) {
            debugPrint('❌ Connexion Bluetooth perdue avant envoi');
            _updateConnectionStatus(PrinterConnectionStatus.error);
            throw Exception(
              'Connexion Bluetooth perdue. Reconnectez-vous à l\'imprimante.',
            );
          }

          // 2. Test de communication rapide
          debugPrint('🧪 Test de communication rapide...');
          try {
            final testBytes = [0x1B, 0x40]; // ESC @ - Reset command
            await PrintBluetoothThermal.writeBytes(
              testBytes,
            ).timeout(const Duration(seconds: 3));
            debugPrint('✅ Test de communication réussi');
            await Future.delayed(const Duration(milliseconds: 500));
          } catch (e) {
            debugPrint('⚠️ Test de communication échoué: $e');
            // Continuer quand même, mais noter l'avertissement
          }

          // 3. Vérifier la taille des données
          if (bytes.isEmpty) {
            throw Exception('Données vides - rien à imprimer');
          }

          if (bytes.length > 1024 * 1024) {
            // 1MB
            debugPrint('⚠️ Données très volumineuses: ${bytes.length} bytes');
          }

          debugPrint('📤 === ENVOI DES DONNÉES ===');
          debugPrint('📤 Taille: ${bytes.length} bytes');

          // ENVOI AVEC STRATÉGIE DE RETRY AMÉLIORÉE
          bool writeSuccess = false;
          int attempts = 0;
          const maxAttempts = 3;
          Duration timeoutDuration = const Duration(seconds: 15);

          while (!writeSuccess && attempts < maxAttempts) {
            attempts++;
            debugPrint('🔄 === TENTATIVE $attempts/$maxAttempts ===');

            try {
              // Vérifier la connexion avant chaque tentative
              final preAttemptConnected =
                  await PrintBluetoothThermal.connectionStatus;
              if (!preAttemptConnected) {
                debugPrint('❌ Connexion perdue avant tentative $attempts');
                throw Exception('Connexion Bluetooth perdue avant envoi');
              }

              // Augmenter le timeout pour les tentatives suivantes
              if (attempts > 1) {
                timeoutDuration = Duration(seconds: 15 + (attempts * 5));
                debugPrint('⏰ Timeout augmenté: ${timeoutDuration.inSeconds}s');
              }

              debugPrint(
                '📤 Envoi des données (timeout: ${timeoutDuration.inSeconds}s)...',
              );

              writeSuccess = await PrintBluetoothThermal.writeBytes(
                bytes,
              ).timeout(
                timeoutDuration,
                onTimeout: () {
                  debugPrint(
                    '⏰ Timeout tentative $attempts (${timeoutDuration.inSeconds}s)',
                  );
                  return false;
                },
              );

              debugPrint(
                '📤 Résultat writeBytes tentative $attempts: $writeSuccess',
              );

              if (writeSuccess) {
                debugPrint('✅ Envoi réussi tentative $attempts');

                // Attendre un peu pour s'assurer que les données sont traitées
                debugPrint('⏳ Attente traitement des données...');
                await Future.delayed(const Duration(milliseconds: 1500));

                // Vérifier que la connexion est toujours active
                final postSendConnected =
                    await PrintBluetoothThermal.connectionStatus;
                debugPrint('📡 Connexion après envoi: $postSendConnected');

                if (!postSendConnected) {
                  debugPrint('⚠️ Connexion perdue après envoi réussi');
                  // Ne pas considérer comme un échec si l'envoi a réussi
                }
              } else {
                debugPrint('❌ writeBytes a retourné false tentative $attempts');

                if (attempts < maxAttempts) {
                  debugPrint('⏳ Pause avant nouvelle tentative...');
                  await Future.delayed(Duration(seconds: 1 + attempts));

                  // Vérifier si la connexion est toujours là
                  final stillConnected =
                      await PrintBluetoothThermal.connectionStatus;
                  if (!stillConnected) {
                    debugPrint('❌ Connexion perdue pendant les tentatives');
                    throw Exception(
                      'Connexion Bluetooth perdue pendant l\'envoi',
                    );
                  }
                }
              }
            } catch (e) {
              debugPrint('❌ Erreur tentative $attempts: $e');

              if (e.toString().contains('Bluetooth') ||
                  e.toString().contains('connexion') ||
                  e.toString().contains('connection')) {
                debugPrint('❌ Erreur de connexion détectée');
                throw Exception('Erreur de connexion Bluetooth: $e');
              }

              if (attempts < maxAttempts) {
                debugPrint('⏳ Pause après erreur...');
                await Future.delayed(Duration(seconds: 2 + attempts));
              } else {
                debugPrint('❌ Échec définitif après $maxAttempts tentatives');
                rethrow;
              }
            }
          }

          // ÉVALUATION DU RÉSULTAT
          if (writeSuccess) {
            debugPrint('🎉 === ENVOI RÉUSSI ===');
            debugPrint(
              '✅ Données envoyées avec succès (tentatives: $attempts)',
            );

            // Attendre que l'impression physique se termine
            debugPrint('⏳ Attente impression physique...');
            await Future.delayed(const Duration(seconds: 2));

            // Vérification finale de la connexion
            try {
              final finalConnected =
                  await PrintBluetoothThermal.connectionStatus;
              debugPrint('📡 Connexion finale: $finalConnected');

              if (!finalConnected) {
                debugPrint(
                  '⚠️ Connexion fermée après impression (normal pour certaines imprimantes)',
                );
              }
            } catch (e) {
              debugPrint('⚠️ Impossible de vérifier la connexion finale: $e');
            }

            success = true;
          } else {
            debugPrint('❌ === ÉCHEC ENVOI ===');
            debugPrint('❌ Échec après $maxAttempts tentatives');
            throw Exception(
              'Impossible d\'envoyer les données à l\'imprimante après $maxAttempts tentatives',
            );
          }
          break;

        case PrinterType.wifi:
          debugPrint('⚠️ Impression WiFi non implémentée');
          throw Exception('Impression WiFi non implémentée');

        case PrinterType.usb:
          debugPrint('⚠️ Impression USB non implémentée');
          throw Exception('Impression USB non implémentée');
      }

      if (success) {
        debugPrint('🎉 === IMPRESSION TERMINÉE AVEC SUCCÈS ===');
        // Jouer le son de succès d'impression
        try {
          await _audioService.playPrintSuccess();
        } catch (e) {
          debugPrint('⚠️ Erreur son de succès: $e');
        }
      }

      return success;
    } catch (e) {
      debugPrint('❌ === ERREUR IMPRESSION ===');
      debugPrint('❌ Erreur: $e');
      debugPrint('❌ Type: ${e.runtimeType}');
      debugPrint('❌ Stack trace: ${StackTrace.current}');

      // Mettre à jour le statut de connexion si nécessaire
      if (e.toString().contains('Connexion Bluetooth perdue') ||
          e.toString().contains('Aucune imprimante connectée') ||
          e.toString().contains('Erreur de connexion Bluetooth')) {
        debugPrint('🔄 Mise à jour statut connexion vers erreur');
        _updateConnectionStatus(PrinterConnectionStatus.error);
      }

      rethrow; // Relancer l'exception pour que l'UI puisse la gérer
    }
  }

  /// Met à jour le statut de connexion
  void _updateConnectionStatus(PrinterConnectionStatus status) {
    _connectionStatus = status;
    _statusController.add(status);
  }

  /// Teste la connexion avec l'imprimante
  Future<bool> testConnection() async {
    if (_connectedPrinter == null) {
      debugPrint('❌ Test connexion: Aucune imprimante connectée');
      return false;
    }

    try {
      debugPrint('🔍 Test de connexion avec ${_connectedPrinter!.name}');

      switch (_connectedPrinter!.type) {
        case PrinterType.bluetooth:
          // Vérifier le statut Bluetooth sans envoyer de données
          bool isConnected = await PrintBluetoothThermal.connectionStatus;
          debugPrint(
            '🔍 Statut Bluetooth: ${isConnected ? "Connecté" : "Déconnecté"}',
          );

          if (!isConnected) {
            _updateConnectionStatus(PrinterConnectionStatus.disconnected);
            return false;
          }

          // Test très léger - juste vérifier que l'imprimante répond
          return await _testPrinterConnection();

        case PrinterType.wifi:
          // Pour WiFi, on assume que la connexion est OK si on a une imprimante connectée
          return true;

        case PrinterType.usb:
          // Pour USB, on assume que la connexion est OK si on a une imprimante connectée
          return true;
      }
    } catch (e) {
      debugPrint('❌ Erreur test connexion: $e');
      _updateConnectionStatus(PrinterConnectionStatus.error);
      return false;
    }
  }

  /// Scanner les imprimantes Bluetooth disponibles
  Future<List<DiscoveredPrinter>> scanBluetoothPrinters() async {
    await _scanBluetoothPrinters();
    _printersController.add(List.from(_discoveredPrinters));
    return _discoveredPrinters
        .where((p) => p.type == PrinterType.bluetooth)
        .toList();
  }

  /// Scanner les imprimantes WiFi disponibles
  Future<List<DiscoveredPrinter>> scanWifiPrinters() async {
    await _scanWifiPrinters();
    _printersController.add(List.from(_discoveredPrinters));
    return _discoveredPrinters
        .where((p) => p.type == PrinterType.wifi)
        .toList();
  }

  /// Méthode pour scanner toutes les imprimantes (alias pour compatibilité)
  Future<List<DiscoveredPrinter>> scanForPrinters() async {
    _discoveredPrinters.clear();
    await _scanBluetoothPrinters();
    await _scanWifiPrinters();
    _printersController.add(List.from(_discoveredPrinters));
    return _discoveredPrinters;
  }

  /// Convertit un PDF en bytes d'impression thermique
  Future<Uint8List> convertPdfToThermalBytes(String pdfPath) async {
    try {
      debugPrint('🔄 Conversion PDF vers bytes thermiques: $pdfPath');

      // Vérifier que le fichier existe
      final file = File(pdfPath);
      if (!await file.exists()) {
        throw Exception('Fichier PDF non trouvé: $pdfPath');
      }

      // Pour l'instant, on va créer un reçu simple avec les informations du PDF
      // Dans une version future, on pourrait utiliser pdf_render pour convertir en image

      // Charger le profil de capacité pour la taille de papier
      final profile = await CapabilityProfile.load();
      final generator = Generator(PaperSize.mm58, profile);

      List<int> bytes = [];

      // Initialisation
      bytes += generator.reset();

      // En-tête
      bytes += generator.text(
        'HCP DESIGN',
        styles: const PosStyles(
          align: PosAlign.center,
          height: PosTextSize.size2,
          width: PosTextSize.size2,
          bold: true,
        ),
      );

      bytes += generator.text(
        'Abidjan, Côte d\'Ivoire',
        styles: const PosStyles(align: PosAlign.center),
      );

      bytes += generator.text(
        '+225 07 09 49 58 48',
        styles: const PosStyles(align: PosAlign.center),
      );

      bytes += generator.hr();

      // Titre
      bytes += generator.text(
        'MINI FACTURE PDF',
        styles: const PosStyles(
          align: PosAlign.center,
          bold: true,
          height: PosTextSize.size1,
        ),
      );

      bytes += generator.emptyLines(1);

      // Informations du PDF
      final fileName = pdfPath.split('/').last;
      bytes += generator.text('Fichier: $fileName');
      bytes += generator.text(
        'Date: ${DateTime.now().toString().substring(0, 16)}',
      );

      bytes += generator.emptyLines(1);

      // Message
      bytes += generator.text(
        'Document PDF imprimé via',
        styles: const PosStyles(align: PosAlign.center),
      );
      bytes += generator.text(
        'imprimante thermique HCP',
        styles: const PosStyles(align: PosAlign.center),
      );

      bytes += generator.emptyLines(2);

      // QR Code (simulé avec du texte)
      bytes += generator.text(
        '[QR: www.hcp-designci.com]',
        styles: const PosStyles(align: PosAlign.center),
      );

      bytes += generator.emptyLines(1);

      // Footer
      bytes += generator.text(
        'Merci pour votre confiance',
        styles: const PosStyles(align: PosAlign.center, bold: true),
      );

      bytes += generator.text(
        'HCP DESIGN',
        styles: const PosStyles(align: PosAlign.center),
      );

      // Couper le papier
      bytes += generator.cut();

      debugPrint('✅ Conversion PDF réussie (${bytes.length} bytes)');
      return Uint8List.fromList(bytes);
    } catch (e) {
      debugPrint('❌ Erreur conversion PDF: $e');
      rethrow;
    }
  }

  /// Effectue un diagnostic complet de l'imprimante
  Future<Map<String, dynamic>> runDiagnostic() async {
    final diagnostic = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'bluetooth_enabled': false,
      'bluetooth_connected': false,
      'printer_in_memory': false,
      'communication_test': false,
      'errors': <String>[],
    };

    try {
      // 1. Vérifier si une imprimante est en mémoire
      diagnostic['printer_in_memory'] = _connectedPrinter != null;
      if (_connectedPrinter != null) {
        diagnostic['printer_name'] = _connectedPrinter!.name;
        diagnostic['printer_address'] = _connectedPrinter!.address;
        diagnostic['printer_type'] = _connectedPrinter!.type.toString();
      }

      // 2. Vérifier Bluetooth
      try {
        diagnostic['bluetooth_enabled'] =
            await PrintBluetoothThermal.bluetoothEnabled;
        if (diagnostic['bluetooth_enabled']) {
          diagnostic['bluetooth_connected'] =
              await PrintBluetoothThermal.connectionStatus;
        }
      } catch (e) {
        diagnostic['errors'].add('Erreur Bluetooth: $e');
      }

      // 3. Test de communication si connecté
      if (diagnostic['bluetooth_connected'] && _connectedPrinter != null) {
        try {
          final testBytes = [0x1B, 0x40]; // ESC @ - Reset command
          await PrintBluetoothThermal.writeBytes(
            testBytes,
          ).timeout(const Duration(seconds: 5));
          diagnostic['communication_test'] = true;
        } catch (e) {
          diagnostic['errors'].add('Test communication échoué: $e');
        }
      }

      debugPrint('🔍 Diagnostic complet: $diagnostic');
      return diagnostic;
    } catch (e) {
      diagnostic['errors'].add('Erreur diagnostic: $e');
      debugPrint('❌ Erreur diagnostic: $e');
      return diagnostic;
    }
  }

  /// Vérifie si l'imprimante est toujours connectée
  Future<bool> isConnected() async {
    try {
      if (_connectedPrinter == null) {
        debugPrint('❌ Aucune imprimante connectée en mémoire');
        return false;
      }

      switch (_connectedPrinter!.type) {
        case PrinterType.bluetooth:
          // Test de connexion Bluetooth plus robuste
          final isBluetoothConnected =
              await PrintBluetoothThermal.connectionStatus;
          debugPrint('📡 Statut Bluetooth: $isBluetoothConnected');

          if (!isBluetoothConnected) {
            debugPrint('❌ Bluetooth déconnecté');
            _connectedPrinter = null;
            _updateConnectionStatus(PrinterConnectionStatus.disconnected);
            return false;
          }

          // Test supplémentaire : essayer d'envoyer une commande simple
          try {
            final testBytes = [0x1B, 0x40]; // ESC @ - Reset command
            final testResult = await PrintBluetoothThermal.writeBytes(
              testBytes,
            ).timeout(const Duration(seconds: 3));
            debugPrint('🧪 Test de communication: $testResult');
            return true; // Même si le test échoue, on considère connecté si Bluetooth OK
          } catch (e) {
            debugPrint('⚠️ Test de communication échoué: $e');
            return isBluetoothConnected; // Retourner le statut Bluetooth de base
          }

        case PrinterType.wifi:
          // Pour WiFi, on assume connecté si on a une imprimante en mémoire
          return true;

        case PrinterType.usb:
          // Pour USB, on assume connecté si on a une imprimante en mémoire
          return true;
      }
    } catch (e) {
      debugPrint('❌ Erreur vérification connexion: $e');
      return false;
    }
  }

  /// Nettoie les ressources
  void dispose() {
    _statusController.close();
    _printersController.close();
    _audioService.dispose();
    disconnect();
  }
}
