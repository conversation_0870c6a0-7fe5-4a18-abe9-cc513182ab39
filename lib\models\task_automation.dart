import 'package:flutter/material.dart';

/// Mod<PERSON>le de règle d'automatisation
class AutomationRule {
  final String id;
  final String name;
  final String description;
  final bool isActive;
  final AutomationTrigger trigger;
  final List<AutomationCondition> conditions;
  final List<AutomationAction> actions;
  final DateTime createdAt;
  final DateTime? lastExecuted;
  final int executionCount;

  AutomationRule({
    required this.id,
    required this.name,
    required this.description,
    required this.isActive,
    required this.trigger,
    required this.conditions,
    required this.actions,
    required this.createdAt,
    this.lastExecuted,
    this.executionCount = 0,
  });

  AutomationRule copyWith({
    String? id,
    String? name,
    String? description,
    bool? isActive,
    AutomationTrigger? trigger,
    List<AutomationCondition>? conditions,
    List<AutomationAction>? actions,
    DateTime? createdAt,
    DateTime? lastExecuted,
    int? executionCount,
  }) {
    return AutomationRule(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      trigger: trigger ?? this.trigger,
      conditions: conditions ?? this.conditions,
      actions: actions ?? this.actions,
      createdAt: createdAt ?? this.createdAt,
      lastExecuted: lastExecuted ?? this.lastExecuted,
      executionCount: executionCount ?? this.executionCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isActive': isActive,
      'trigger': trigger.toJson(),
      'conditions': conditions.map((c) => c.toJson()).toList(),
      'actions': actions.map((a) => a.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'lastExecuted': lastExecuted?.toIso8601String(),
      'executionCount': executionCount,
    };
  }

  factory AutomationRule.fromJson(Map<String, dynamic> json) {
    return AutomationRule(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      isActive: json['isActive'] ?? true,
      trigger: AutomationTrigger.fromJson(json['trigger']),
      conditions:
          (json['conditions'] as List)
              .map((c) => AutomationCondition.fromJson(c))
              .toList(),
      actions:
          (json['actions'] as List)
              .map((a) => AutomationAction.fromJson(a))
              .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      lastExecuted:
          json['lastExecuted'] != null
              ? DateTime.parse(json['lastExecuted'])
              : null,
      executionCount: json['executionCount'] ?? 0,
    );
  }
}

/// Déclencheur d'automatisation
class AutomationTrigger {
  final TriggerType type;
  final Map<String, dynamic> parameters;

  AutomationTrigger({required this.type, required this.parameters});

  Map<String, dynamic> toJson() {
    return {'type': type.name, 'parameters': parameters};
  }

  factory AutomationTrigger.fromJson(Map<String, dynamic> json) {
    return AutomationTrigger(
      type: TriggerType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => TriggerType.taskCreated,
      ),
      parameters: Map<String, dynamic>.from(json['parameters'] ?? {}),
    );
  }
}

/// Condition d'automatisation
class AutomationCondition {
  final ConditionType type;
  final String field;
  final ConditionOperator operator;
  final dynamic value;

  AutomationCondition({
    required this.type,
    required this.field,
    required this.operator,
    required this.value,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'field': field,
      'operator': operator.name,
      'value': value,
    };
  }

  factory AutomationCondition.fromJson(Map<String, dynamic> json) {
    return AutomationCondition(
      type: ConditionType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => ConditionType.field,
      ),
      field: json['field'],
      operator: ConditionOperator.values.firstWhere(
        (o) => o.name == json['operator'],
        orElse: () => ConditionOperator.equals,
      ),
      value: json['value'],
    );
  }
}

/// Action d'automatisation
class AutomationAction {
  final ActionType type;
  final Map<String, dynamic> parameters;

  AutomationAction({required this.type, required this.parameters});

  Map<String, dynamic> toJson() {
    return {'type': type.name, 'parameters': parameters};
  }

  factory AutomationAction.fromJson(Map<String, dynamic> json) {
    return AutomationAction(
      type: ActionType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => ActionType.updateField,
      ),
      parameters: Map<String, dynamic>.from(json['parameters'] ?? {}),
    );
  }
}

/// Types de déclencheur
enum TriggerType {
  taskCreated('Tâche créée'),
  taskUpdated('Tâche modifiée'),
  taskCompleted('Tâche terminée'),
  dueDateApproaching('Échéance approche'),
  dueDatePassed('Échéance dépassée'),
  statusChanged('Statut changé'),
  assigneeChanged('Responsable changé'),
  priorityChanged('Priorité changée'),
  commentAdded('Commentaire ajouté'),
  attachmentAdded('Pièce jointe ajoutée'),
  scheduled('Programmé');

  const TriggerType(this.displayName);
  final String displayName;
}

/// Types de condition
enum ConditionType {
  field('Champ'),
  time('Temps'),
  user('Utilisateur'),
  project('Projet');

  const ConditionType(this.displayName);
  final String displayName;
}

/// Opérateurs de condition
enum ConditionOperator {
  equals('Égal à'),
  notEquals('Différent de'),
  contains('Contient'),
  notContains('Ne contient pas'),
  greaterThan('Supérieur à'),
  lessThan('Inférieur à'),
  greaterThanOrEqual('Supérieur ou égal à'),
  lessThanOrEqual('Inférieur ou égal à'),
  isEmpty('Est vide'),
  isNotEmpty('N\'est pas vide'),
  isTrue('Est vrai'),
  isFalse('Est faux'),
  inList('Dans la liste'),
  notInList('Pas dans la liste');

  const ConditionOperator(this.displayName);
  final String displayName;
}

/// Types d'action
enum ActionType {
  updateField('Modifier un champ'),
  changeStatus('Changer le statut'),
  changePriority('Changer la priorité'),
  assignTo('Assigner à'),
  addComment('Ajouter un commentaire'),
  sendNotification('Envoyer une notification'),
  createSubtask('Créer une sous-tâche'),
  moveToSection('Déplacer vers une section'),
  addTag('Ajouter un tag'),
  removeTag('Supprimer un tag'),
  setDueDate('Définir une échéance'),
  addFollower('Ajouter un observateur'),
  removeFollower('Supprimer un observateur');

  const ActionType(this.displayName);
  final String displayName;
}

/// Modèle de notification d'automatisation
class AutomationNotification {
  final String id;
  final String ruleId;
  final String taskId;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime createdAt;
  final bool isRead;
  final List<String> recipientIds;

  AutomationNotification({
    required this.id,
    required this.ruleId,
    required this.taskId,
    required this.title,
    required this.message,
    required this.type,
    required this.createdAt,
    required this.isRead,
    required this.recipientIds,
  });

  AutomationNotification copyWith({
    String? id,
    String? ruleId,
    String? taskId,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? createdAt,
    bool? isRead,
    List<String>? recipientIds,
  }) {
    return AutomationNotification(
      id: id ?? this.id,
      ruleId: ruleId ?? this.ruleId,
      taskId: taskId ?? this.taskId,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      recipientIds: recipientIds ?? this.recipientIds,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'ruleId': ruleId,
      'taskId': taskId,
      'title': title,
      'message': message,
      'type': type.name,
      'createdAt': createdAt.toIso8601String(),
      'isRead': isRead,
      'recipientIds': recipientIds,
    };
  }

  factory AutomationNotification.fromJson(Map<String, dynamic> json) {
    return AutomationNotification(
      id: json['id'],
      ruleId: json['ruleId'],
      taskId: json['taskId'],
      title: json['title'],
      message: json['message'],
      type: NotificationType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => NotificationType.info,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      isRead: json['isRead'] ?? false,
      recipientIds: List<String>.from(json['recipientIds'] ?? []),
    );
  }
}

/// Types de notification
enum NotificationType {
  info('Information'),
  warning('Avertissement'),
  error('Erreur'),
  success('Succès'),
  reminder('Rappel');

  const NotificationType(this.displayName);
  final String displayName;

  Color get color {
    switch (this) {
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.warning:
        return Colors.orange;
      case NotificationType.error:
        return Colors.red;
      case NotificationType.success:
        return Colors.green;
      case NotificationType.reminder:
        return Colors.purple;
    }
  }

  IconData get icon {
    switch (this) {
      case NotificationType.info:
        return Icons.info;
      case NotificationType.warning:
        return Icons.warning;
      case NotificationType.error:
        return Icons.error;
      case NotificationType.success:
        return Icons.check_circle;
      case NotificationType.reminder:
        return Icons.notifications;
    }
  }
}
