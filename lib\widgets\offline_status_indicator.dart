import 'package:flutter/material.dart';
import '../services/offline_config_service.dart';

class OfflineStatusIndicator extends StatefulWidget {
  const OfflineStatusIndicator({super.key});

  @override
  State<OfflineStatusIndicator> createState() => _OfflineStatusIndicatorState();
}

class _OfflineStatusIndicatorState extends State<OfflineStatusIndicator> {
  bool _isOfflineMode = true;

  @override
  void initState() {
    super.initState();
    _loadStatus();
  }

  Future<void> _loadStatus() async {
    final isOffline = await OfflineConfigService.instance.isOfflineModeEnabled();
    if (mounted) {
      setState(() {
        _isOfflineMode = isOffline;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _isOfflineMode ? Colors.orange[100] : Colors.green[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isOfflineMode ? Colors.orange[300]! : Colors.green[300]!,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _isOfflineMode ? Icons.wifi_off : Icons.wifi,
            size: 16,
            color: _isOfflineMode ? Colors.orange[700] : Colors.green[700],
          ),
          const SizedBox(width: 4),
          Text(
            _isOfflineMode ? 'Offline' : 'Online',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: _isOfflineMode ? Colors.orange[700] : Colors.green[700],
            ),
          ),
        ],
      ),
    );
  }
}
