import 'package:flutter/material.dart';
import '../models/invoice.dart';
import '../services/invoice_service.dart';
import '../services/data_change_notifier.dart';
import 'dart:async';

class SimpleInvoiceTestPage extends StatefulWidget {
  const SimpleInvoiceTestPage({super.key});

  @override
  State<SimpleInvoiceTestPage> createState() => _SimpleInvoiceTestPageState();
}

class _SimpleInvoiceTestPageState extends State<SimpleInvoiceTestPage> {
  List<Invoice> _invoices = [];
  bool _isLoading = false;
  String _status = 'Prêt';
  int _notificationCount = 0;
  StreamSubscription? _subscription;

  @override
  void initState() {
    super.initState();
    _setupNotificationListener();
    _loadInvoices();
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  void _setupNotificationListener() {
    _subscription = DataChangeNotifier.instance.invoicesChanged.listen((_) {
      setState(() {
        _notificationCount++;
        _status = 'Notification reçue #$_notificationCount';
      });
      _loadInvoices();
    });
  }

  Future<void> _loadInvoices() async {
    setState(() {
      _isLoading = true;
      _status = 'Chargement...';
    });

    try {
      final stopwatch = Stopwatch()..start();
      final invoices = await InvoiceService.loadInvoices();
      stopwatch.stop();

      setState(() {
        _invoices = invoices;
        _isLoading = false;
        _status = 'Chargé: ${invoices.length} factures en ${stopwatch.elapsedMilliseconds}ms';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _status = 'Erreur: $e';
      });
    }
  }

  Future<void> _createTestInvoice() async {
    setState(() {
      _status = 'Création facture test...';
    });

    try {
      final testInvoice = Invoice(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        clientName: 'Client Test ${DateTime.now().hour}:${DateTime.now().minute}',
        clientNumber: '123456',
        products: 'Produit Test',
        items: [],
        deliveryLocation: 'Test Location',
        deliveryPrice: 10.0,
        advance: 0.0,
        subtotal: 100.0,
        total: 110.0,
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
        type: InvoiceType.normale,
      );

      final stopwatch = Stopwatch()..start();
      await InvoiceService().addInvoice(testInvoice);
      stopwatch.stop();

      setState(() {
        _status = 'Facture créée en ${stopwatch.elapsedMilliseconds}ms';
      });
    } catch (e) {
      setState(() {
        _status = 'Erreur création: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Simple Factures'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Statut: $_status',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Notifications reçues: $_notificationCount'),
                    Text('Factures chargées: ${_invoices.length}'),
                    if (_isLoading) const CircularProgressIndicator(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _loadInvoices,
                  child: const Text('Recharger'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _createTestInvoice,
                  child: const Text('Créer Test'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: _invoices.isEmpty
                  ? const Center(
                      child: Text(
                        'Aucune facture trouvée',
                        style: TextStyle(fontSize: 18),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _invoices.length,
                      itemBuilder: (context, index) {
                        final invoice = _invoices[index];
                        return Card(
                          child: ListTile(
                            title: Text(invoice.clientName),
                            subtitle: Text(
                              'ID: ${invoice.id}\n'
                              'Total: ${invoice.total.toStringAsFixed(2)}€\n'
                              'Créé: ${invoice.createdAt.toString().substring(0, 19)}',
                            ),
                            trailing: Text(invoice.status.toString().split('.').last),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}