import 'package:flutter/material.dart';
import '../services/theme_service.dart';
import '../widgets/dynamic_app_bar.dart';

class ThemeDemoPage extends StatefulWidget {
  const ThemeDemoPage({super.key});

  @override
  State<ThemeDemoPage> createState() => _ThemeDemoPageState();
}

class _ThemeDemoPageState extends State<ThemeDemoPage> {
  String _currentTheme = 'home';

  @override
  Widget build(BuildContext context) {
    return DynamicScaffold(
      pageKey: _currentTheme,
      title: 'Démonstration des Thèmes',
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Sélecteur de thème
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sélectionner un thème',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          ThemeService.instance.getAllThemes().entries.map((
                            entry,
                          ) {
                            final isSelected = _currentTheme == entry.key;
                            final theme = entry.value;

                            return AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              child: FilterChip(
                                label: Text(theme.name),
                                selected: isSelected,
                                onSelected: (selected) {
                                  if (selected) {
                                    setState(() {
                                      _currentTheme = entry.key;
                                    });
                                  }
                                },
                                backgroundColor: theme.primary.withValues(
                                  alpha: 0.1,
                                ),
                                selectedColor: theme.primary.withValues(
                                  alpha: 0.3,
                                ),
                                checkmarkColor: theme.primary,
                                labelStyle: TextStyle(
                                  color:
                                      isSelected
                                          ? theme.primary
                                          : Colors.black87,
                                  fontWeight:
                                      isSelected
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                ),
                                side: BorderSide(
                                  color: theme.primary,
                                  width: isSelected ? 2 : 1,
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Aperçu du thème actuel
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Aperçu du thème: ${ThemeService.instance.getThemeForPage(_currentTheme).name}',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      const SizedBox(height: 16),

                      Expanded(child: _buildThemePreview()),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: DynamicFloatingActionButton(
        pageKey: _currentTheme,
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Thème ${ThemeService.instance.getThemeForPage(_currentTheme).name} appliqué !',
              ),
              backgroundColor:
                  ThemeService.instance.getThemeForPage(_currentTheme).primary,
            ),
          );
        },
        tooltip: 'Tester le thème',
        child: const Icon(Icons.palette),
      ),
    );
  }

  Widget _buildThemePreview() {
    final theme = ThemeService.instance.getThemeForPage(_currentTheme);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Palette de couleurs
          _buildColorPalette(theme),
          const SizedBox(height: 24),

          // Exemples de composants
          _buildComponentExamples(theme),
        ],
      ),
    );
  }

  Widget _buildColorPalette(PageTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Palette de couleurs',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(child: _buildColorCard('Primaire', theme.primary)),
            const SizedBox(width: 8),
            Expanded(child: _buildColorCard('Secondaire', theme.secondary)),
            const SizedBox(width: 8),
            Expanded(child: _buildColorCard('Accent', theme.accent)),
          ],
        ),
      ],
    );
  }

  Widget _buildColorCard(String label, Color color) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _getColorHex(color),
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
                fontFamily: 'monospace',
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComponentExamples(PageTheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Exemples de composants',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // Boutons
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.primary,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Bouton Principal'),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton(
                onPressed: () {},
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: theme.primary),
                  foregroundColor: theme.primary,
                ),
                child: const Text('Bouton Secondaire'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Cartes avec gradient
        Container(
          width: double.infinity,
          height: 100,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [theme.primary, theme.secondary],
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Center(
            child: Text(
              'Carte avec Gradient',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Indicateurs
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildIndicator('Succès', theme.accent, Icons.check_circle),
            _buildIndicator('Info', theme.primary, Icons.info),
            _buildIndicator('Attention', theme.secondary, Icons.warning),
          ],
        ),
        const SizedBox(height: 16),

        // Barre de progression
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Barre de progression'),
            const SizedBox(height: 8),
            LinearProgressIndicator(
              value: 0.7,
              backgroundColor: theme.primary.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(theme.accent),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildIndicator(String label, Color color, IconData icon) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color, width: 2),
          ),
          child: Icon(icon, color: color, size: 24),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: color,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Obtenir la représentation hexadécimale d'une couleur
  String _getColorHex(Color color) {
    // Utiliser les nouvelles propriétés recommandées
    final r = color.r.round().toRadixString(16).padLeft(2, '0');
    final g = color.g.round().toRadixString(16).padLeft(2, '0');
    final b = color.b.round().toRadixString(16).padLeft(2, '0');
    return '#${r.toUpperCase()}${g.toUpperCase()}${b.toUpperCase()}';
  }
}
