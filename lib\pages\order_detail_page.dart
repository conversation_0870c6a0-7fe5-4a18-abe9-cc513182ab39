import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../models/order_photo.dart';
import '../services/order_service.dart';
import '../services/invoice_service.dart';
import '../widgets/photo_gallery_widget.dart';
import 'edit_invoice_page.dart';
import 'create_invoice_page.dart';
import 'invoice_detail_page.dart';
import 'thermal_printer/printer_discovery_page.dart';
import 'thermal_printer/thermal_diagnostic_page.dart';

/// Page de détail d'une commande
class OrderDetailPage extends StatefulWidget {
  final Invoice order;

  const OrderDetailPage({super.key, required this.order});

  @override
  State<OrderDetailPage> createState() => _OrderDetailPageState();
}

class _OrderDetailPageState extends State<OrderDetailPage> {
  final OrderService _orderService = OrderService.instance;
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy HH:mm');
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');

  Invoice? _linkedInvoice;

  List<OrderPhoto> _photos = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPhotos();
    _loadLinkedInvoice();
  }

  /// Charger la facture liée si elle existe
  Future<void> _loadLinkedInvoice() async {
    if (widget.order.linkedInvoiceId != null) {
      try {
        final invoices = await InvoiceService.loadInvoices();
        final linkedInvoice = invoices.firstWhere(
          (invoice) => invoice.id == widget.order.linkedInvoiceId,
          orElse: () => throw Exception('Facture liée non trouvée'),
        );

        if (mounted) {
          setState(() {
            _linkedInvoice = linkedInvoice;
          });
        }
      } catch (e) {
        // Facture liée non trouvée, on continue sans erreur
        print('Facture liée non trouvée: $e');
      }
    }
  }

  /// Charger les photos de la commande
  Future<void> _loadPhotos() async {
    try {
      final photos = await _orderService.getOrderPhotos(widget.order.id);
      if (mounted) {
        setState(() {
          _photos = photos;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Erreur chargement photos: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Commande ${widget.order.clientName}'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _editOrder,
            tooltip: 'Modifier',
          ),
          IconButton(
            icon: const Icon(Icons.print),
            onPressed: _printOrder,
            tooltip: 'Imprimer mini-facture',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'print_thermal',
                    child: Row(
                      children: [
                        Icon(Icons.print),
                        SizedBox(width: 8),
                        Text('Impression thermique'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'diagnostic',
                    child: Row(
                      children: [
                        Icon(Icons.bug_report),
                        SizedBox(width: 8),
                        Text('Diagnostic impression'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'duplicate',
                    child: Row(
                      children: [
                        Icon(Icons.copy),
                        SizedBox(width: 8),
                        Text('Dupliquer'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Supprimer', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statut et informations principales
            _buildStatusCard(),

            const SizedBox(height: 16),

            // Facture liée (si elle existe)
            if (_linkedInvoice != null) ...[
              _buildLinkedInvoiceCard(),
              const SizedBox(height: 16),
            ],

            // Informations client
            _buildClientCard(),

            const SizedBox(height: 16),

            // Détails de la commande
            _buildOrderDetailsCard(),

            const SizedBox(height: 16),

            // Livraison
            _buildDeliveryCard(),

            const SizedBox(height: 16),

            // Photos
            _buildPhotosCard(),

            if (widget.order.notes != null) ...[
              const SizedBox(height: 16),
              _buildNotesCard(),
            ],
          ],
        ),
      ),
    );
  }

  /// Construire la carte de statut
  Widget _buildStatusCard() {
    Color statusColor;
    String statusLabel;
    IconData statusIcon;

    switch (widget.order.status) {
      case InvoiceStatus.enAttente:
        statusColor = Colors.orange;
        statusLabel = 'En attente';
        statusIcon = Icons.schedule;
        break;
      case InvoiceStatus.enCours:
        statusColor = Colors.blue;
        statusLabel = 'En cours';
        statusIcon = Icons.hourglass_empty;
        break;
      case InvoiceStatus.terminee:
        statusColor = Colors.green;
        statusLabel = 'Terminée';
        statusIcon = Icons.check_circle;
        break;
      case InvoiceStatus.enRetard:
        statusColor = Colors.grey;
        statusLabel = 'En retard';
        statusIcon = Icons.warning;
        break;
      case InvoiceStatus.annulee:
        statusColor = Colors.red;
        statusLabel = 'Annulée';
        statusIcon = Icons.cancel_outlined;
        break;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [statusColor.withValues(alpha: 0.1), Colors.white],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(statusIcon, color: statusColor, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusLabel,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                  Text(
                    'Créée le ${_dateFormat.format(widget.order.createdAt)}',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Text(
              '${_currencyFormat.format(widget.order.total)} FCFA',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Construire la carte client
  Widget _buildClientCard() {
    return _buildInfoCard(
      title: 'Informations Client',
      icon: Icons.person,
      children: [
        _buildInfoRow('Nom', widget.order.clientName),
        _buildInfoRow('Téléphone', widget.order.clientNumber),
        if (widget.order.clientAddress != null)
          _buildInfoRow('Adresse', widget.order.clientAddress!),
        if (widget.order.clientEmail != null)
          _buildInfoRow('Email', widget.order.clientEmail!),
      ],
    );
  }

  /// Construire la carte des détails de commande
  Widget _buildOrderDetailsCard() {
    return _buildInfoCard(
      title: 'Détails de la Commande',
      icon: Icons.shopping_cart,
      children: [
        _buildInfoRow('Produits', widget.order.products),
        if (widget.order.items.isNotEmpty) ...[
          const SizedBox(height: 8),
          const Text(
            'Articles détaillés:',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          ...widget.order.items.map(
            (item) => Padding(
              padding: const EdgeInsets.only(left: 16, top: 4),
              child: Text(
                '• ${item.name} (${item.quantity}x ${_currencyFormat.format(item.price)} FCFA)',
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Construire la carte de livraison
  Widget _buildDeliveryCard() {
    return _buildInfoCard(
      title: 'Livraison',
      icon: Icons.local_shipping,
      children: [
        _buildInfoRow('Lieu', widget.order.deliveryLocation),
        if (widget.order.deliveryDetails != null)
          _buildInfoRow('Détails', widget.order.deliveryDetails!),
        _buildInfoRow(
          'Frais de livraison',
          '${_currencyFormat.format(widget.order.deliveryPrice)} FCFA',
        ),
        if (widget.order.advance > 0)
          _buildInfoRow(
            'Avance',
            '${_currencyFormat.format(widget.order.advance)} FCFA',
          ),
      ],
    );
  }

  /// Construire la carte des photos
  Widget _buildPhotosCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.photo_camera, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Photos',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : PhotoGalleryWidget(
                  photos: _photos,
                  onPhotoTap: _viewPhotoDetails,
                  showOcrData: true,
                ),
          ],
        ),
      ),
    );
  }

  /// Construire la carte des notes
  Widget _buildNotesCard() {
    return _buildInfoCard(
      title: 'Notes',
      icon: Icons.note,
      children: [
        Text(
          widget.order.notes!,
          style: TextStyle(fontSize: 14, color: Colors.grey[700]),
        ),
      ],
    );
  }

  /// Construire une carte d'information générique
  Widget _buildInfoCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Colors.blue[700]),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// Construire une ligne d'information
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: Colors.grey[700])),
          ),
        ],
      ),
    );
  }

  /// Construire la carte de la facture liée
  Widget _buildLinkedInvoiceCard() {
    if (_linkedInvoice == null) return const SizedBox.shrink();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.link, color: Colors.purple[700]),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Facture liée',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Cette commande a été créée à partir d\'une facture existante',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => InvoiceDetailPage(
                              invoiceId: _linkedInvoice!.id,
                            ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('Voir'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Informations de la facture
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.purple[200]!),
              ),
              child: Column(
                children: [
                  _buildInfoRow('Numéro', _linkedInvoice!.invoiceNumber),
                  _buildInfoRow(
                    'Date',
                    _dateFormat.format(_linkedInvoice!.createdAt),
                  ),
                  _buildInfoRow('Client', _linkedInvoice!.clientName),
                  _buildInfoRow(
                    'Montant',
                    '${_currencyFormat.format(_linkedInvoice!.total)} FCFA',
                  ),
                  _buildInfoRow(
                    'Statut',
                    _getStatusLabel(_linkedInvoice!.status),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Obtenir le libellé du statut
  String _getStatusLabel(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.enAttente:
        return 'En attente';
      case InvoiceStatus.enCours:
        return 'En cours';
      case InvoiceStatus.terminee:
        return 'Terminée';
      case InvoiceStatus.enRetard:
        return 'En retard';
      case InvoiceStatus.annulee:
        return 'Annulée';
    }
  }

  /// Voir les détails d'une photo
  void _viewPhotoDetails(OrderPhoto photo) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // En-tête
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[700],
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            photo.displayName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close, color: Colors.white),
                        ),
                      ],
                    ),
                  ),

                  // Contenu
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Image
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.file(
                              File(photo.filePath),
                              width: double.infinity,
                              height: 200,
                              fit: BoxFit.cover,
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Données OCR
                          _buildPhotoOcrDetails(photo),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  /// Modifier la commande
  void _editOrder() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditInvoicePage(invoice: widget.order),
      ),
    );

    if (result == true) {
      // Recharger la commande mise à jour
      final updatedOrder = await _orderService.getOrder(widget.order.id);
      if (updatedOrder != null && mounted) {
        setState(() {
          // Mettre à jour l'ordre local avec les nouvelles données
        });
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Commande modifiée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  /// Gérer les actions du menu
  void _handleMenuAction(String action) {
    switch (action) {
      case 'print_thermal':
        _printOrder();
        break;
      case 'diagnostic':
        _openDiagnostic();
        break;
      case 'duplicate':
        _duplicateOrder();
        break;
      case 'delete':
        _deleteOrder();
        break;
    }
  }

  /// Dupliquer la commande
  void _duplicateOrder() async {
    try {
      // Créer une copie de la commande avec un nouvel ID et la date actuelle
      final duplicatedOrder = widget.order.copyWith(
        id: '', // Sera généré automatiquement
        createdAt: DateTime.now(),
        status: InvoiceStatus.enAttente, // Nouvelle commande en attente
        photoIds: [], // Pas de photos dans la duplication
      );

      // Naviguer vers la page de création avec la commande dupliquée
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => CreateInvoicePage(duplicateFrom: duplicatedOrder),
        ),
      );

      if (result == true && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Commande dupliquée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur lors de la duplication: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Supprimer la commande
  void _deleteOrder() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Supprimer la commande'),
            content: const Text(
              'Êtes-vous sûr de vouloir supprimer cette commande ? '
              'Cette action est irréversible.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () async {
                  // Capturer les références avant l'opération async
                  final navigator = Navigator.of(context);
                  final messenger = ScaffoldMessenger.of(context);

                  navigator.pop(); // Fermer le dialog

                  try {
                    await _orderService.deleteOrder(widget.order.id);
                    if (mounted) {
                      navigator.pop(true);
                      messenger.showSnackBar(
                        const SnackBar(
                          content: Text('✅ Commande supprimée'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text('Erreur: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }

  /// Construire les détails OCR d'une photo
  Widget _buildPhotoOcrDetails(OrderPhoto photo) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Données OCR:',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        if (photo.ocrData.isNotEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children:
                  photo.ocrData.entries.map((entry) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 80,
                            child: Text(
                              '${entry.key}:',
                              style: const TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Text(
                              entry.value,
                              style: const TextStyle(fontSize: 12),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
            ),
          )
        else
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: const Text(
              'Aucune donnée OCR disponible',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Colors.orange,
              ),
            ),
          ),
      ],
    );
  }

  /// Imprimer la commande via imprimante thermique
  void _printOrder() async {
    try {
      // Naviguer vers la page de découverte d'imprimante avec la commande
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PrinterDiscoveryPage(invoice: widget.order),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de l\'impression: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Ouvrir la page de diagnostic d'impression
  void _openDiagnostic() async {
    try {
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const ThermalDiagnosticPage()),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
