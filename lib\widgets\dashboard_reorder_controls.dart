import 'package:flutter/material.dart';

/// Widget de contrôles pour la réorganisation du tableau de bord
class DashboardReorderControls extends StatefulWidget {
  final bool isReorderMode;
  final VoidCallback onToggleReorderMode;
  final VoidCallback onResetOrder;

  const DashboardReorderControls({
    super.key,
    required this.isReorderMode,
    required this.onToggleReorderMode,
    required this.onResetOrder,
  });

  @override
  State<DashboardReorderControls> createState() =>
      _DashboardReorderControlsState();
}

class _DashboardReorderControlsState extends State<DashboardReorderControls>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );
  }

  @override
  void didUpdateWidget(DashboardReorderControls oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isReorderMode != oldWidget.isReorderMode) {
      if (widget.isReorderMode) {
        _slideController.forward();
      } else {
        _slideController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Bouton de basculement du mode réorganisation
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: widget.onToggleReorderMode,
                  icon: Icon(
                    widget.isReorderMode ? Icons.check : Icons.edit,
                    size: 20,
                  ),
                  label: Text(
                    widget.isReorderMode
                        ? 'Terminer la réorganisation'
                        : 'Réorganiser les cartes',
                    style: const TextStyle(fontWeight: FontWeight.w600),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        widget.isReorderMode
                            ? Colors.green[600]
                            : Colors.blue[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              if (widget.isReorderMode) ...[
                const SizedBox(width: 12),
                IconButton(
                  onPressed: () => _showResetDialog(context),
                  icon: const Icon(Icons.refresh),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.all(12),
                  ),
                  tooltip: 'Réinitialiser l\'ordre',
                ),
              ],
            ],
          ),
        ),

        // Panneau d'instructions (visible en mode réorganisation)
        SlideTransition(
          position: _slideAnimation,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue[600], size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Mode Réorganisation Activé',
                      style: TextStyle(
                        color: Colors.blue[800],
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  '• Maintenez et faites glisser les cartes pour les réorganiser\n'
                  '• L\'ordre sera sauvegardé automatiquement\n'
                  '• Cliquez sur "Terminer" pour revenir au mode normal',
                  style: TextStyle(color: Colors.blue[700], fontSize: 14),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(
                      Icons.drag_indicator,
                      color: Colors.blue[600],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Glissez les cartes avec l\'icône de déplacement',
                      style: TextStyle(
                        color: Colors.blue[600],
                        fontSize: 12,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showResetDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.refresh, color: Colors.orange),
              SizedBox(width: 8),
              Text('Réinitialiser l\'ordre'),
            ],
          ),
          content: const Text(
            'Voulez-vous vraiment remettre les cartes dans leur ordre par défaut ?\n\n'
            'Cette action ne peut pas être annulée.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                widget.onResetOrder();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Ordre des cartes réinitialisé'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
              ),
              child: const Text('Réinitialiser'),
            ),
          ],
        );
      },
    );
  }
}

/// Widget d'aide pour expliquer la réorganisation
class ReorderHelpWidget extends StatelessWidget {
  const ReorderHelpWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.help_outline, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                'Comment réorganiser les cartes ?',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildHelpStep(
            '1.',
            'Cliquez sur "Réorganiser les cartes"',
            Icons.edit,
          ),
          _buildHelpStep(
            '2.',
            'Maintenez et faites glisser les cartes',
            Icons.drag_indicator,
          ),
          _buildHelpStep('3.', 'Relâchez à la position souhaitée', Icons.place),
          _buildHelpStep(
            '4.',
            'Cliquez sur "Terminer" pour sauvegarder',
            Icons.check,
          ),
        ],
      ),
    );
  }

  Widget _buildHelpStep(String number, String text, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                number,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(fontSize: 14, color: Colors.grey[700]),
            ),
          ),
        ],
      ),
    );
  }
}
