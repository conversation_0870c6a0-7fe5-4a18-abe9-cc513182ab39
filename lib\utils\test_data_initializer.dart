import '../services/task_service.dart';
import '../services/colis_service.dart';
import '../models/task.dart';
import '../models/colis.dart';

/// Utilitaire pour initialiser des données de test
class TestDataInitializer {
  static final TaskService _taskService = TaskService.instance;
  static final ColisService _colisService = ColisService.instance;

  /// Initialise des données de test pour les notifications
  static Future<void> initializeTestData() async {
    await _createTestTasks();
    await _createTestRetours();
  }

  /// Crée des tâches de test
  static Future<void> _createTestTasks() async {
    final now = DateTime.now();
    
    // Tâche urgente pour aujourd'hui
    final urgentTask = Task(
      id: '',
      title: 'Appeler client urgent',
      description: 'Contacter le client pour confirmer la commande importante',
      dueDate: now.add(const Duration(hours: 2)),
      priority: TaskPriority.high,
      isCompleted: false,
      createdAt: now,
    );

    // Tâche pour demain
    final tomorrowTask = Task(
      id: '',
      title: 'Préparer rapport mensuel',
      description: 'Compiler les statistiques de vente du mois',
      dueDate: now.add(const Duration(days: 1)),
      priority: TaskPriority.medium,
      isCompleted: false,
      createdAt: now,
    );

    // Tâche en retard
    final overdueTask = Task(
      id: '',
      title: 'Mise à jour inventaire',
      description: 'Vérifier et mettre à jour les stocks',
      dueDate: now.subtract(const Duration(hours: 6)),
      priority: TaskPriority.high,
      isCompleted: false,
      createdAt: now.subtract(const Duration(days: 2)),
    );

    try {
      await _taskService.addTask(urgentTask);
      await _taskService.addTask(tomorrowTask);
      await _taskService.addTask(overdueTask);
      print('✅ Tâches de test créées avec succès');
    } catch (e) {
      print('❌ Erreur lors de la création des tâches de test: $e');
    }
  }

  /// Crée des colis retours de test
  static Future<void> _createTestRetours() async {
    final now = DateTime.now();

    // Colis retour 1
    final retour1 = Colis(
      id: 'retour_test_1',
      libelle: 'Commande iPhone 15 - Retour client',
      photoPath: '/test/path/photo1.jpg',
      zoneLivraison: 'Cocody',
      numeroClient: '07 12 34 56 78',
      resteAPayer: 0.0,
      fraisLivraison: 2000.0,
      dateAjout: now.subtract(const Duration(days: 1)),
      statut: StatutLivraison.retour,
      nomClient: 'Kouakou Jean',
      adresseLivraison: 'Cocody Angré, Rue des Jardins',
      notes: 'Client absent lors de la livraison',
    );

    // Colis retour 2
    final retour2 = Colis(
      id: 'retour_test_2',
      libelle: 'Samsung Galaxy S24 - Problème adresse',
      photoPath: '/test/path/photo2.jpg',
      zoneLivraison: 'Plateau',
      numeroClient: '05 87 65 43 21',
      resteAPayer: 15000.0,
      fraisLivraison: 1500.0,
      dateAjout: now.subtract(const Duration(hours: 8)),
      statut: StatutLivraison.retour,
      nomClient: 'Aya Marie',
      adresseLivraison: 'Plateau, Immeuble Alpha',
      notes: 'Adresse incorrecte, retour au dépôt',
    );

    try {
      // Charger les colis existants
      final existingColis = await _colisService.loadColis();
      
      // Ajouter les nouveaux retours
      final updatedColis = [...existingColis, retour1, retour2];
      
      // Sauvegarder
      await _colisService.saveColis(updatedColis);
      
      print('✅ Colis retours de test créés avec succès');
    } catch (e) {
      print('❌ Erreur lors de la création des colis retours de test: $e');
    }
  }

  /// Nettoie les données de test
  static Future<void> clearTestData() async {
    try {
      // Supprimer les tâches de test
      final tasks = await _taskService.getTasks();
      final testTasks = tasks.where((task) => 
        task.title.contains('test') || 
        task.title.contains('Test') ||
        task.description.contains('test')
      ).toList();

      for (final task in testTasks) {
        await _taskService.deleteTask(task.id);
      }

      // Supprimer les colis retours de test
      final colis = await _colisService.loadColis();
      final filteredColis = colis.where((c) => 
        !c.id.startsWith('retour_test_')
      ).toList();
      
      await _colisService.saveColis(filteredColis);
      
      print('✅ Données de test nettoyées avec succès');
    } catch (e) {
      print('❌ Erreur lors du nettoyage des données de test: $e');
    }
  }
}
