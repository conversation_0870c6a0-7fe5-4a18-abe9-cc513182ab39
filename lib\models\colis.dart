/// Énumération pour les statuts de livraison
enum StatutLivraison { livree, enRetard, annulee, retour, enCours, brouillon }

/// Extension pour obtenir les libellés et couleurs des statuts
extension StatutLivraisonExtension on StatutLivraison {
  String get libelle {
    switch (this) {
      case StatutLivraison.livree:
        return 'Livrée';
      case StatutLivraison.enRetard:
        return 'En retard';
      case StatutLivraison.annulee:
        return 'Annulée';
      case StatutLivraison.retour:
        return 'Retour';
      case StatutLivraison.enCours:
        return 'Livraison en cours';
      case StatutLivraison.brouillon:
        return 'Brouillon';
    }
  }

  String get emoji {
    switch (this) {
      case StatutLivraison.livree:
        return '🟢';
      case StatutLivraison.enRetard:
        return '🟠';
      case StatutLivraison.annulee:
        return '🔴';
      case StatutLivraison.retour:
        return '🔴';
      case StatutLivraison.enCours:
        return '🚚';
      case StatutLivraison.brouillon:
        return '📝';
    }
  }

  String get color {
    switch (this) {
      case StatutLivraison.livree:
        return '#4CAF50';
      case StatutLivraison.enRetard:
        return '#FF9800';
      case StatutLivraison.annulee:
        return '#F44336';
      case StatutLivraison.retour:
        return '#F44336';
      case StatutLivraison.enCours:
        return '#9C27B0';
      case StatutLivraison.brouillon:
        return '#9E9E9E';
    }
  }

  // Getters pour compatibilité avec l'ancien code
  String get couleur => color;
  int get colorValue {
    switch (this) {
      case StatutLivraison.livree:
        return 0xFF4CAF50;
      case StatutLivraison.enRetard:
        return 0xFFFF9800;
      case StatutLivraison.annulee:
        return 0xFFF44336;
      case StatutLivraison.retour:
        return 0xFFF44336;
      case StatutLivraison.enCours:
        return 0xFF9C27B0;
      case StatutLivraison.brouillon:
        return 0xFF9E9E9E;
    }
  }

  String toLowerCase() {
    return name.toLowerCase();
  }
}

/// Modèle pour représenter un colis
class Colis {
  final String id;
  final String libelle;
  final String photoPath;
  final String zoneLivraison;
  final String numeroClient;
  final double resteAPayer;
  final double fraisLivraison;
  final DateTime dateAjout;
  final StatutLivraison statut;
  final String? nomClient;
  final String? adresseLivraison;
  final String? notes;
  final String? factureId; // ID de la facture associée
  final DateTime? dateLivraison; // Date de livraison effective

  Colis({
    required this.id,
    required this.libelle,
    required this.photoPath,
    required this.zoneLivraison,
    required this.numeroClient,
    required this.resteAPayer,
    required this.fraisLivraison,
    required this.dateAjout,
    required this.statut,
    this.nomClient,
    this.adresseLivraison,
    this.notes,
    this.factureId,
    this.dateLivraison,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'libelle': libelle,
      'photoPath': photoPath,
      'zoneLivraison': zoneLivraison,
      'numeroClient': numeroClient,
      'resteAPayer': resteAPayer,
      'fraisLivraison': fraisLivraison,
      'dateAjout': dateAjout.toIso8601String(),
      'statut': statut.name,
      'nomClient': nomClient,
      'adresseLivraison': adresseLivraison,
      'notes': notes,
      'factureId': factureId,
      'dateLivraison': dateLivraison?.toIso8601String(),
    };
  }

  factory Colis.fromJson(Map<String, dynamic> json) {
    return Colis(
      id: json['id'],
      libelle: json['libelle'],
      photoPath: json['photoPath'],
      zoneLivraison: json['zoneLivraison'],
      numeroClient: json['numeroClient'],
      resteAPayer: (json['resteAPayer'] as num).toDouble(),
      fraisLivraison: (json['fraisLivraison'] as num).toDouble(),
      dateAjout: DateTime.parse(json['dateAjout']),
      statut: StatutLivraison.values.firstWhere(
        (s) => s.name == json['statut'],
        orElse: () => StatutLivraison.enRetard,
      ),
      nomClient: json['nomClient'],
      adresseLivraison: json['adresseLivraison'],
      notes: json['notes'],
      factureId: json['factureId'],
      dateLivraison:
          json['dateLivraison'] != null
              ? DateTime.parse(json['dateLivraison'])
              : null,
    );
  }

  Colis copyWith({
    String? id,
    String? libelle,
    String? photoPath,
    String? zoneLivraison,
    String? numeroClient,
    double? resteAPayer,
    double? fraisLivraison,
    DateTime? dateAjout,
    StatutLivraison? statut,
    String? nomClient,
    String? adresseLivraison,
    String? notes,
    String? factureId,
    DateTime? dateLivraison,
  }) {
    return Colis(
      id: id ?? this.id,
      libelle: libelle ?? this.libelle,
      photoPath: photoPath ?? this.photoPath,
      zoneLivraison: zoneLivraison ?? this.zoneLivraison,
      numeroClient: numeroClient ?? this.numeroClient,
      resteAPayer: resteAPayer ?? this.resteAPayer,
      fraisLivraison: fraisLivraison ?? this.fraisLivraison,
      dateAjout: dateAjout ?? this.dateAjout,
      statut: statut ?? this.statut,
      nomClient: nomClient ?? this.nomClient,
      adresseLivraison: adresseLivraison ?? this.adresseLivraison,
      notes: notes ?? this.notes,
      factureId: factureId ?? this.factureId,
      dateLivraison: dateLivraison ?? this.dateLivraison,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Colis && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Colis(id: $id, libelle: $libelle, statut: $statut)';
  }
}

/// Zones de livraison avec leurs prix
class ZoneLivraison {
  static const Map<String, double> zones = {
    'Cocody': 1000,
    'Plateau': 1000,
    'Marcory': 1500,
    'Treichville': 1500,
    'Adjamé': 1500,
    'Yopougon': 2500,
    'Abobo': 2500,
    'Port Bouet': 2000,
    'Gonzague ville': 2000,
    // Zones supplémentaires
    'Koumassi': 2000,
    'Anyama': 2500,
    'Grand-Bassam': 3000,
    'Ebimpé': 2500,
    'Km-17': 2500,
    'Adjoufou': 2000,
    'Bingerville': 2000,
    'Yango': 2000,
    'Viens chercher': 0,
    'Expédition Nationale': 2000, // Ajouté
  };

  /// Obtient le prix de livraison pour une zone donnée
  static double getPrixLivraison(String zone) {
    return zones[zone] ?? 1000; // Prix par défaut si zone non trouvée
  }

  /// Obtient la liste des zones disponibles
  static List<String> getZonesDisponibles() {
    return zones.keys.toList()..sort();
  }

  /// Vérifie si une zone existe
  static bool zoneExiste(String zone) {
    return zones.containsKey(zone);
  }
}

/// Modèle pour les statistiques de livraison
class StatistiquesLivraison {
  final int totalColis;
  final int colisLivres;
  final int colisEnRetard;
  final int colisAnnules;
  final int colisReportes;
  final int colisEnCours;
  final double totalResteAPayer;
  final double totalFraisLivraison;
  final double pointJournalier;
  final DateTime date;
  final Map<String, int> repartitionParZone;

  StatistiquesLivraison({
    required this.totalColis,
    required this.colisLivres,
    required this.colisEnRetard,
    required this.colisAnnules,
    required this.colisReportes,
    required this.colisEnCours,
    required this.totalResteAPayer,
    required this.totalFraisLivraison,
    required this.pointJournalier,
    required this.date,
    required this.repartitionParZone,
  });

  factory StatistiquesLivraison.fromColis(List<Colis> colis, DateTime date) {
    final colisDuJour =
        colis
            .where(
              (c) =>
                  c.dateAjout.year == date.year &&
                  c.dateAjout.month == date.month &&
                  c.dateAjout.day == date.day,
            )
            .toList();

    int colisLivres = 0;
    int colisEnRetard = 0;
    int colisAnnules = 0;
    int colisReportes = 0;
    int colisEnCours = 0;
    double totalResteAPayer = 0;
    double totalFraisLivraison = 0;
    Map<String, int> repartitionParZone = {};

    for (final coli in colisDuJour) {
      totalResteAPayer += coli.resteAPayer;
      totalFraisLivraison += coli.fraisLivraison;

      repartitionParZone[coli.zoneLivraison] =
          (repartitionParZone[coli.zoneLivraison] ?? 0) + 1;

      switch (coli.statut) {
        case StatutLivraison.livree:
          colisLivres++;
          break;
        case StatutLivraison.enRetard:
          colisEnRetard++;
          break;
        case StatutLivraison.annulee:
          colisAnnules++;
          break;
        case StatutLivraison.retour:
          colisReportes++;
          break;
        case StatutLivraison.enCours:
          colisEnCours++;
          break;
        case StatutLivraison.brouillon:
          // Les brouillons sont comptés dans le total mais pas dans les autres catégories
          break;
      }
    }

    final pointJournalier = totalResteAPayer - totalFraisLivraison;

    return StatistiquesLivraison(
      totalColis: colisDuJour.length,
      colisLivres: colisLivres,
      colisEnRetard: colisEnRetard,
      colisAnnules: colisAnnules,
      colisReportes: colisReportes,
      colisEnCours: colisEnCours,
      totalResteAPayer: totalResteAPayer,
      totalFraisLivraison: totalFraisLivraison,
      pointJournalier: pointJournalier,
      date: date,
      repartitionParZone: repartitionParZone,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalColis': totalColis,
      'colisLivres': colisLivres,
      'colisEnRetard': colisEnRetard,
      'colisAnnules': colisAnnules,
      'colisReportes': colisReportes,
      'colisEnCours': colisEnCours,
      'totalResteAPayer': totalResteAPayer,
      'totalFraisLivraison': totalFraisLivraison,
      'pointJournalier': pointJournalier,
      'date': date.toIso8601String(),
      'repartitionParZone': repartitionParZone,
    };
  }

  factory StatistiquesLivraison.fromJson(Map<String, dynamic> json) {
    return StatistiquesLivraison(
      totalColis: json['totalColis'],
      colisLivres: json['colisLivres'],
      colisEnRetard: json['colisEnRetard'],
      colisAnnules: json['colisAnnules'],
      colisReportes: json['colisReportes'],
      colisEnCours: json['colisEnCours'] ?? 0,
      totalResteAPayer: (json['totalResteAPayer'] as num).toDouble(),
      totalFraisLivraison: (json['totalFraisLivraison'] as num).toDouble(),
      pointJournalier: (json['pointJournalier'] as num).toDouble(),
      date: DateTime.parse(json['date']),
      repartitionParZone: Map<String, int>.from(json['repartitionParZone']),
    );
  }

  // Getters pour compatibilité avec l'ancien code
  int get livres => colisLivres;
  int get enRetard => colisEnRetard;
  int get annules => colisAnnules;
  int get reportes => colisReportes;
  int get enCours => colisEnCours;
  double get totalAEncaisser => totalResteAPayer;
}
