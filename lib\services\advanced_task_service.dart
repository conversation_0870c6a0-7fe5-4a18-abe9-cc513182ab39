import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import '../models/advanced_task.dart';
import '../models/task_user.dart';
import '../models/task_activity.dart';
import '../models/task_automation.dart';

class AdvancedTaskService {
  static const String _projectsKey = 'hcp_projects';
  static const String _sectionsKey = 'hcp_task_sections';
  static const String _tasksKey = 'hcp_advanced_tasks';
  static const String _commentsKey = 'hcp_task_comments';
  static const String _attachmentsKey = 'hcp_task_attachments';
  static const String _activitiesKey = 'hcp_task_activities';
  static const String _automationRulesKey = 'hcp_automation_rules';

  final Uuid _uuid = const Uuid();

  // Cache en mémoire
  List<Project> _projects = [];
  List<TaskSection> _sections = [];
  List<AdvancedTask> _tasks = [];
  List<TaskComment> _comments = [];
  List<TaskAttachment> _attachments = [];
  List<TaskActivity> _activities = [];
  List<AutomationRule> _automationRules = [];

  bool _isInitialized = false;

  static final AdvancedTaskService _instance = AdvancedTaskService._internal();
  factory AdvancedTaskService() => _instance;
  AdvancedTaskService._internal();

  /// Initialise le service
  Future<void> initialize() async {
    if (_isInitialized) return;

    debugPrint('🔄 Initialisation du service de tâches avancées...');
    final stopwatch = Stopwatch()..start();

    await Future.wait([
      _loadProjects(),
      _loadSections(),
      _loadTasks(),
      _loadComments(),
      _loadAttachments(),
      _loadActivities(),
      _loadAutomationRules(),
    ]);

    _isInitialized = true;
    stopwatch.stop();
    debugPrint(
      '✅ Service de tâches avancées initialisé en ${stopwatch.elapsedMilliseconds}ms',
    );
  }

  // ===== GESTION DES PROJETS =====

  Future<List<Project>> getProjects() async {
    await initialize();
    return List.from(_projects);
  }

  Future<Project?> getProjectById(String id) async {
    await initialize();
    try {
      return _projects.firstWhere(
        (project) => project.id == id,
        orElse: () => throw StateError('Projet non trouvé'),
      );
    } catch (e) {
      debugPrint('⚠️ Projet non trouvé: $id');
      return null;
    }
  }

  Future<String> addProject(Project project) async {
    await initialize();

    final id = _uuid.v4();
    final projectWithId = project.copyWith(id: id);

    _projects.add(projectWithId);
    await _saveProjects();

    // Enregistrer l'activité
    await _logActivity(
      taskId: '', // Pas de tâche spécifique
      userId: project.ownerId ?? 'system',
      type: ActivityType.created,
      description: 'Projet "${project.name}" créé',
    );

    debugPrint('✅ Projet ajouté: ${project.name}');
    return id;
  }

  Future<void> updateProject(Project project) async {
    await initialize();

    final index = _projects.indexWhere((p) => p.id == project.id);
    if (index != -1) {
      final oldProject = _projects[index];
      _projects[index] = project;
      await _saveProjects();

      // Enregistrer l'activité
      await _logActivity(
        taskId: '',
        userId: project.ownerId ?? 'system',
        type: ActivityType.updated,
        description: 'Projet "${project.name}" modifié',
        oldValues: oldProject.toJson(),
        newValues: project.toJson(),
      );

      debugPrint('✅ Projet mis à jour: ${project.name}');
    }
  }

  Future<void> deleteProject(String id) async {
    await initialize();

    final project = await getProjectById(id);
    if (project != null) {
      _projects.removeWhere((p) => p.id == id);

      // Supprimer toutes les sections et tâches du projet
      _sections.removeWhere((s) => s.projectId == id);
      _tasks.removeWhere((t) => t.projectId == id);

      await Future.wait([_saveProjects(), _saveSections(), _saveTasks()]);

      debugPrint('✅ Projet supprimé: ${project.name}');
    }
  }

  // ===== GESTION DES SECTIONS =====

  Future<List<TaskSection>> getSectionsByProject(String projectId) async {
    await initialize();
    return _sections.where((s) => s.projectId == projectId).toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  Future<String> addSection(TaskSection section) async {
    await initialize();

    final id = _uuid.v4();
    final sectionWithId = section.copyWith(id: id);

    _sections.add(sectionWithId);
    await _saveSections();

    debugPrint('✅ Section ajoutée: ${section.name}');
    return id;
  }

  Future<void> updateSection(TaskSection section) async {
    await initialize();

    final index = _sections.indexWhere((s) => s.id == section.id);
    if (index != -1) {
      _sections[index] = section;
      await _saveSections();
      debugPrint('✅ Section mise à jour: ${section.name}');
    }
  }

  Future<void> deleteSection(String id) async {
    await initialize();

    final section = _sections.where((s) => s.id == id).firstOrNull;
    if (section != null) {
      _sections.removeWhere((s) => s.id == id);

      // Déplacer les tâches vers "Sans section"
      for (var task in _tasks.where((t) => t.sectionId == id)) {
        final updatedTask = task.copyWith(sectionId: null);
        await updateTask(updatedTask);
      }

      await _saveSections();
      debugPrint('✅ Section supprimée: ${section.name}');
    }
  }

  // ===== GESTION DES TÂCHES =====

  Future<List<AdvancedTask>> getTasks({TaskFilter? filter}) async {
    await initialize();

    var tasks = List<AdvancedTask>.from(_tasks);

    if (filter != null) {
      tasks = _applyFilter(tasks, filter);
    }

    return tasks;
  }

  Future<List<AdvancedTask>> getTasksByProject(String projectId) async {
    await initialize();
    return _tasks.where((t) => t.projectId == projectId).toList();
  }

  Future<List<AdvancedTask>> getSubtasks(String parentTaskId) async {
    await initialize();
    return _tasks.where((t) => t.parentTaskId == parentTaskId).toList()
      ..sort((a, b) => a.order.compareTo(b.order));
  }

  Future<AdvancedTask?> getTaskById(String id) async {
    await initialize();
    try {
      return _tasks.firstWhere(
        (task) => task.id == id,
        orElse: () => throw StateError('Tâche avancée non trouvée'),
      );
    } catch (e) {
      debugPrint('⚠️ Tâche avancée non trouvée: $id');
      return null;
    }
  }

  Future<String> addTask(AdvancedTask task) async {
    await initialize();

    final id = _uuid.v4();
    final taskWithId = task.copyWith(id: id);

    _tasks.add(taskWithId);
    await _saveTasks();

    // Enregistrer l'activité
    await _logActivity(
      taskId: id,
      userId: task.assigneeId ?? 'system',
      type: ActivityType.created,
      description: 'Tâche "${task.title}" créée',
    );

    // Exécuter les règles d'automatisation
    await _executeAutomationRules(TriggerType.taskCreated, taskWithId);

    debugPrint('✅ Tâche ajoutée: ${task.title}');
    return id;
  }

  Future<void> updateTask(AdvancedTask task) async {
    await initialize();

    final index = _tasks.indexWhere((t) => t.id == task.id);
    if (index != -1) {
      final oldTask = _tasks[index];
      _tasks[index] = task;
      await _saveTasks();

      // Enregistrer l'activité
      await _logActivity(
        taskId: task.id,
        userId: task.assigneeId ?? 'system',
        type: ActivityType.updated,
        description: 'Tâche "${task.title}" modifiée',
        oldValues: oldTask.toJson(),
        newValues: task.toJson(),
      );

      // Exécuter les règles d'automatisation
      await _executeAutomationRules(TriggerType.taskUpdated, task, oldTask);

      debugPrint('✅ Tâche mise à jour: ${task.title}');
    }
  }

  Future<void> deleteTask(String id) async {
    await initialize();

    final task = await getTaskById(id);
    if (task != null) {
      _tasks.removeWhere((t) => t.id == id);

      // Supprimer les sous-tâches
      final subtasks = await getSubtasks(id);
      for (var subtask in subtasks) {
        await deleteTask(subtask.id);
      }

      // Supprimer les commentaires et pièces jointes
      _comments.removeWhere((c) => c.taskId == id);
      _attachments.removeWhere((a) => a.taskId == id);
      _activities.removeWhere((a) => a.taskId == id);

      await Future.wait([
        _saveTasks(),
        _saveComments(),
        _saveAttachments(),
        _saveActivities(),
      ]);

      debugPrint('✅ Tâche supprimée: ${task.title}');
    }
  }

  // ===== MÉTHODES PRIVÉES =====

  Future<void> _loadProjects() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_projectsKey);
      if (data != null) {
        final List<dynamic> jsonList = json.decode(data);
        _projects = jsonList.map((json) => Project.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('❌ Erreur lors du chargement des projets: $e');
      _projects = [];
    }
  }

  Future<void> _saveProjects() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(_projects.map((p) => p.toJson()).toList());
      await prefs.setString(_projectsKey, data);
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde des projets: $e');
    }
  }

  Future<void> _loadSections() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_sectionsKey);
      if (data != null) {
        final List<dynamic> jsonList = json.decode(data);
        _sections = jsonList.map((json) => TaskSection.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('❌ Erreur lors du chargement des sections: $e');
      _sections = [];
    }
  }

  Future<void> _saveSections() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(_sections.map((s) => s.toJson()).toList());
      await prefs.setString(_sectionsKey, data);
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde des sections: $e');
    }
  }

  Future<void> _loadTasks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_tasksKey);
      if (data != null) {
        final List<dynamic> jsonList = json.decode(data);
        _tasks = jsonList.map((json) => AdvancedTask.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('❌ Erreur lors du chargement des tâches: $e');
      _tasks = [];
    }
  }

  Future<void> _saveTasks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(_tasks.map((t) => t.toJson()).toList());
      await prefs.setString(_tasksKey, data);
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde des tâches: $e');
    }
  }

  Future<void> _loadComments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_commentsKey);
      if (data != null) {
        final List<dynamic> jsonList = json.decode(data);
        _comments = jsonList.map((json) => TaskComment.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('❌ Erreur lors du chargement des commentaires: $e');
      _comments = [];
    }
  }

  Future<void> _saveComments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(_comments.map((c) => c.toJson()).toList());
      await prefs.setString(_commentsKey, data);
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde des commentaires: $e');
    }
  }

  Future<void> _loadAttachments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_attachmentsKey);
      if (data != null) {
        final List<dynamic> jsonList = json.decode(data);
        _attachments =
            jsonList.map((json) => TaskAttachment.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('❌ Erreur lors du chargement des pièces jointes: $e');
      _attachments = [];
    }
  }

  Future<void> _saveAttachments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(_attachments.map((a) => a.toJson()).toList());
      await prefs.setString(_attachmentsKey, data);
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde des pièces jointes: $e');
    }
  }

  Future<void> _loadActivities() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_activitiesKey);
      if (data != null) {
        final List<dynamic> jsonList = json.decode(data);
        _activities =
            jsonList.map((json) => TaskActivity.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('❌ Erreur lors du chargement des activités: $e');
      _activities = [];
    }
  }

  Future<void> _saveActivities() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(_activities.map((a) => a.toJson()).toList());
      await prefs.setString(_activitiesKey, data);
    } catch (e) {
      debugPrint('❌ Erreur lors de la sauvegarde des activités: $e');
    }
  }

  Future<void> _loadAutomationRules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = prefs.getString(_automationRulesKey);
      if (data != null) {
        final List<dynamic> jsonList = json.decode(data);
        _automationRules =
            jsonList.map((json) => AutomationRule.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint(
        '❌ Erreur lors du chargement des règles d\'automatisation: $e',
      );
      _automationRules = [];
    }
  }

  Future<void> _saveAutomationRules() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final data = json.encode(
        _automationRules.map((r) => r.toJson()).toList(),
      );
      await prefs.setString(_automationRulesKey, data);
    } catch (e) {
      debugPrint(
        '❌ Erreur lors de la sauvegarde des règles d\'automatisation: $e',
      );
    }
  }

  /// Applique un filtre à une liste de tâches
  List<AdvancedTask> _applyFilter(List<AdvancedTask> tasks, TaskFilter filter) {
    return tasks.where((task) {
      // Filtre par projet
      if (filter.projectId != null && task.projectId != filter.projectId) {
        return false;
      }

      // Filtre par section
      if (filter.sectionId != null && task.sectionId != filter.sectionId) {
        return false;
      }

      // Filtre par responsable
      if (filter.assigneeId != null && task.assigneeId != filter.assigneeId) {
        return false;
      }

      // Filtre par statuts
      if (filter.statuses.isNotEmpty &&
          !filter.statuses.contains(task.status)) {
        return false;
      }

      // Filtre par priorités
      if (filter.priorities.isNotEmpty &&
          !filter.priorities.contains(task.priority)) {
        return false;
      }

      // Filtre par tags
      if (filter.tags.isNotEmpty) {
        final hasMatchingTag = filter.tags.any(
          (tag) => task.tags.contains(tag),
        );
        if (!hasMatchingTag) return false;
      }

      // Filtre par dates d'échéance
      if (filter.dueDateFrom != null && task.dueDate != null) {
        if (task.dueDate!.isBefore(filter.dueDateFrom!)) return false;
      }
      if (filter.dueDateTo != null && task.dueDate != null) {
        if (task.dueDate!.isAfter(filter.dueDateTo!)) return false;
      }

      // Filtre par dates de début
      if (filter.startDateFrom != null && task.startDate != null) {
        if (task.startDate!.isBefore(filter.startDateFrom!)) return false;
      }
      if (filter.startDateTo != null && task.startDate != null) {
        if (task.startDate!.isAfter(filter.startDateTo!)) return false;
      }

      // Filtre par tâches en retard
      if (filter.isOverdue != null && filter.isOverdue! != task.isOverdue) {
        return false;
      }

      // Filtre par recherche textuelle
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        final matchesTitle = task.title.toLowerCase().contains(query);
        final matchesDescription = task.description.toLowerCase().contains(
          query,
        );
        if (!matchesTitle && !matchesDescription) return false;
      }

      return true;
    }).toList();
  }

  /// Enregistre une activité
  Future<void> _logActivity({
    required String taskId,
    required String userId,
    required ActivityType type,
    required String description,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
  }) async {
    final activity = TaskActivity(
      id: _uuid.v4(),
      taskId: taskId,
      userId: userId,
      type: type,
      description: description,
      oldValues: oldValues,
      newValues: newValues,
      createdAt: DateTime.now(),
    );

    _activities.add(activity);
    await _saveActivities();
  }

  /// Exécute les règles d'automatisation
  Future<void> _executeAutomationRules(
    TriggerType triggerType,
    AdvancedTask task, [
    AdvancedTask? oldTask,
  ]) async {
    final applicableRules =
        _automationRules
            .where((rule) => rule.isActive && rule.trigger.type == triggerType)
            .toList();

    for (final rule in applicableRules) {
      try {
        final shouldExecute = _evaluateConditions(
          rule.conditions,
          task,
          oldTask,
        );
        if (shouldExecute) {
          await _executeActions(rule.actions, task);

          // Mettre à jour les statistiques de la règle
          final updatedRule = rule.copyWith(
            lastExecuted: DateTime.now(),
            executionCount: rule.executionCount + 1,
          );
          final index = _automationRules.indexWhere((r) => r.id == rule.id);
          if (index != -1) {
            _automationRules[index] = updatedRule;
            await _saveAutomationRules();
          }
        }
      } catch (e) {
        debugPrint(
          '❌ Erreur lors de l\'exécution de la règle ${rule.name}: $e',
        );
      }
    }
  }

  /// Évalue les conditions d'une règle
  bool _evaluateConditions(
    List<AutomationCondition> conditions,
    AdvancedTask task,
    AdvancedTask? oldTask,
  ) {
    if (conditions.isEmpty) return true;

    for (final condition in conditions) {
      if (!_evaluateCondition(condition, task, oldTask)) {
        return false; // Toutes les conditions doivent être vraies (AND)
      }
    }
    return true;
  }

  /// Évalue une condition individuelle
  bool _evaluateCondition(
    AutomationCondition condition,
    AdvancedTask task,
    AdvancedTask? oldTask,
  ) {
    dynamic fieldValue;

    // Récupérer la valeur du champ
    switch (condition.field) {
      case 'status':
        fieldValue = task.status.name;
        break;
      case 'priority':
        fieldValue = task.priority.name;
        break;
      case 'assigneeId':
        fieldValue = task.assigneeId;
        break;
      case 'dueDate':
        fieldValue = task.dueDate;
        break;
      case 'title':
        fieldValue = task.title;
        break;
      default:
        fieldValue = task.customFields[condition.field];
    }

    // Appliquer l'opérateur
    switch (condition.operator) {
      case ConditionOperator.equals:
        return fieldValue == condition.value;
      case ConditionOperator.notEquals:
        return fieldValue != condition.value;
      case ConditionOperator.contains:
        return fieldValue?.toString().contains(condition.value.toString()) ??
            false;
      case ConditionOperator.isEmpty:
        return fieldValue == null || fieldValue.toString().isEmpty;
      case ConditionOperator.isNotEmpty:
        return fieldValue != null && fieldValue.toString().isNotEmpty;
      default:
        return false;
    }
  }

  /// Exécute les actions d'une règle
  Future<void> _executeActions(
    List<AutomationAction> actions,
    AdvancedTask task,
  ) async {
    for (final action in actions) {
      try {
        await _executeAction(action, task);
      } catch (e) {
        debugPrint(
          '❌ Erreur lors de l\'exécution de l\'action ${action.type.name}: $e',
        );
      }
    }
  }

  /// Exécute une action individuelle
  Future<void> _executeAction(
    AutomationAction action,
    AdvancedTask task,
  ) async {
    switch (action.type) {
      case ActionType.changeStatus:
        final newStatus = TaskStatus.values.firstWhere(
          (s) => s.name == action.parameters['status'],
          orElse: () => task.status,
        );
        await updateTask(task.copyWith(status: newStatus));
        break;

      case ActionType.changePriority:
        final newPriority = TaskPriority.values.firstWhere(
          (p) => p.name == action.parameters['priority'],
          orElse: () => task.priority,
        );
        await updateTask(task.copyWith(priority: newPriority));
        break;

      case ActionType.assignTo:
        await updateTask(
          task.copyWith(assigneeId: action.parameters['userId']),
        );
        break;

      case ActionType.addComment:
        await addComment(
          TaskComment(
            id: _uuid.v4(),
            taskId: task.id,
            authorId: 'system',
            content: action.parameters['content'] ?? 'Commentaire automatique',
            createdAt: DateTime.now(),
          ),
        );
        break;

      default:
        debugPrint('Action non implémentée: ${action.type.name}');
    }
  }

  // ===== GESTION DES COMMENTAIRES =====

  Future<List<TaskComment>> getCommentsByTask(String taskId) async {
    await initialize();
    return _comments.where((c) => c.taskId == taskId).toList()
      ..sort((a, b) => a.createdAt.compareTo(b.createdAt));
  }

  Future<String> addComment(TaskComment comment) async {
    await initialize();

    final id = _uuid.v4();
    final commentWithId = comment.copyWith(id: id);

    _comments.add(commentWithId);
    await _saveComments();

    // Enregistrer l'activité
    await _logActivity(
      taskId: comment.taskId,
      userId: comment.authorId,
      type: ActivityType.commentAdded,
      description: 'Commentaire ajouté',
    );

    debugPrint('✅ Commentaire ajouté');
    return id;
  }

  // ===== GESTION DES ACTIVITÉS =====

  Future<List<TaskActivity>> getActivitiesByTask(String taskId) async {
    await initialize();
    return _activities.where((a) => a.taskId == taskId).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }
}
