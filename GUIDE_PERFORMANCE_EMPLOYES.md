# Guide de Performance des Employés

## 📊 Vue d'ensemble

Le système de performance des employés calcule automatiquement le salaire mensuel basé sur le nombre de clients valides ayant passé des commandes d'au moins 3 000 FCFA.

## ⚙️ Paramètres de calcul

- **Salaire fixe** : 70 000 FCFA
- **Objectif mensuel** : 200 clients avec commande ≥ 3 000 FCFA
- **Bonus maximum** : 20 000 FCFA
- **Salaire maximum** : 90 000 FCFA (fixe + bonus max)

## 🧮 Formule de calcul

```
Pourcentage d'objectif = (Clients valides / 200) × 100%
Bonus = (Clients valides / 200) × 20 000 FCFA (plafonné à 100%)
Salaire total = 70 000 + Bonus
```

## 📈 Exemples de calcul

| Clients valides | Progression | Bonus | Salaire total |
|----------------|-------------|-------|---------------|
| 0              | 0%          | 0     | 70 000 FCFA   |
| 25             | 12.5%       | 2 500 | 72 500 FCFA   |
| 50             | 25%         | 5 000 | 75 000 FCFA   |
| 100            | 50%         | 10 000| 80 000 FCFA   |
| 150            | 75%         | 15 000| 85 000 FCFA   |
| 200            | 100%        | 20 000| 90 000 FCFA   |
| 250+           | 100%        | 20 000| 90 000 FCFA   |

## 🎯 Critères de validation des clients

Un client est considéré comme **valide** si :
- Il a passé au moins une commande dans le mois
- Le montant total de ses commandes ≥ 3 000 FCFA
- Les factures sont dans le statut "Payée"

## 📱 Interface utilisateur

### Tableau de bord principal
- Widget de performance affiché dans la section "PERFORMANCE EMPLOYÉ"
- Affichage du salaire total, progression, et statistiques clés
- Bouton de rafraîchissement et lien vers les détails

### Page de détails
- Carte de salaire avec détail fixe/bonus
- Progression vers l'objectif avec barre de progression
- Statistiques détaillées (CA, factures, panier moyen)
- Répartition des clients par tranche de commande
- Top 5 des meilleurs clients du mois

## 🔄 Mise à jour des données

Les données sont calculées en temps réel basées sur :
- Les factures payées du mois en cours
- Le regroupement par client (nom + numéro)
- La somme des montants par client

## 🛠️ Fonctionnalités techniques

### Services
- `EmployeePerformanceService` : Logique de calcul principal
- `InvoiceService.getEmployeePerformanceStats()` : Méthode d'extraction des données

### Widgets
- `EmployeePerformanceWidget` : Widget compact pour le tableau de bord
- `EmployeePerformancePage` : Page détaillée avec toutes les statistiques

### Modèles
- `EmployeePerformanceStats` : Statistiques complètes
- `ClientOrderInfo` : Informations par client
- `DateRange` : Période de calcul

## 📊 Métriques affichées

### Principales
- **Salaire total** : Fixe + bonus calculé
- **Clients valides** : Nombre de clients ≥ 3 000 FCFA
- **Progression** : Pourcentage vers l'objectif de 200 clients

### Secondaires
- **Total clients** : Tous les clients ayant commandé
- **CA mensuel** : Chiffre d'affaires total
- **Factures payées** : Nombre de factures
- **Panier moyen** : CA / nombre de factures

### Analyses
- **Répartition par tranche** : Distribution des clients par montant
- **Top clients** : 5 meilleurs clients du mois
- **Historique** : Évolution dans le temps (future amélioration)

## 🎉 Notifications de réussite

Quand l'objectif est atteint (≥ 200 clients valides) :
- Message de félicitations
- Indicateur visuel vert
- Confirmation du bonus maximum

## 🔧 Configuration

Les paramètres peuvent être modifiés dans `EmployeePerformanceService` :
```dart
static const double _fixedSalary = 70000.0;
static const double _maxBonus = 20000.0;
static const int _clientObjective = 200;
static const double _minimumOrderAmount = 3000.0;
```

## 📝 Notes importantes

1. **Période de calcul** : Mois calendaire en cours (1er au dernier jour)
2. **Regroupement clients** : Par combinaison nom + numéro de téléphone
3. **Montants** : Basés sur le sous-total des articles (hors frais de livraison)
4. **Statut requis** : Seules les factures "Payées" sont comptabilisées
5. **Plafonnement** : Le bonus est plafonné même si l'objectif est dépassé

## 🚀 Améliorations futures possibles

- Historique mensuel des performances
- Objectifs personnalisés par employé
- Comparaison avec les mois précédents
- Notifications push pour les étapes importantes
- Export des rapports de performance
- Graphiques d'évolution temporelle
