import 'package:flutter/material.dart';

class NotificationBadge extends StatelessWidget {
  final Widget child;
  final int count;
  final Color badgeColor;
  final Color textColor;
  final double badgeSize;
  final bool showBadge;

  const NotificationBadge({
    super.key,
    required this.child,
    required this.count,
    this.badgeColor = Colors.red,
    this.textColor = Colors.white,
    this.badgeSize = 18.0,
    this.showBadge = true,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        if (showBadge && count > 0)
          Positioned(
            right: -6,
            top: -6,
            child: Container(
              width: badgeSize,
              height: badgeSize,
              decoration: BoxDecoration(
                color: badgeColor,
                borderRadius: BorderRadius.circular(badgeSize / 2),
                border: Border.all(
                  color: Colors.white,
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6.0,
                  vertical: 2.0,
                ),
                child: Center(
                  child: Text(
                    count > 99 ? '99+' : count.toString(),
                    style: TextStyle(
                      color: textColor,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      height: 1.0,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}

// Widget spécialisé pour les badges de la barre de navigation
class NavigationBadge extends StatelessWidget {
  final Widget child;
  final int count;
  final BadgeType type;

  const NavigationBadge({
    super.key,
    required this.child,
    required this.count,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    Color badgeColor;
    
    switch (type) {
      case BadgeType.whatsapp:
        badgeColor = const Color(0xFF25D366); // Vert WhatsApp
        break;
      case BadgeType.tasks:
        badgeColor = Colors.orange;
        break;
      case BadgeType.inventory:
        badgeColor = Colors.red;
        break;
      case BadgeType.dashboard:
        badgeColor = Colors.blue;
        break;
    }

    return NotificationBadge(
      count: count,
      badgeColor: badgeColor,
      badgeSize: 16.0,
      child: child,
    );
  }
}

enum BadgeType {
  whatsapp,
  tasks,
  inventory,
  dashboard,
}