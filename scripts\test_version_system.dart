import 'dart:io';

/// Script pour tester le système de versioning
void main() async {
  print('🧪 Test du système de versioning');
  
  try {
    await testPubspecVersion();
    await testVersionService();
    await testChangelog();
    
    print('\n✅ Tous les tests sont passés avec succès!');
    print('🎉 Le système de versioning fonctionne correctement.');
    
  } catch (e) {
    print('\n❌ Erreur lors des tests: $e');
    exit(1);
  }
}

/// Tester la version dans pubspec.yaml
Future<void> testPubspecVersion() async {
  print('\n📋 Test de la version dans pubspec.yaml...');
  
  final pubspecFile = File('pubspec.yaml');
  if (!await pubspecFile.exists()) {
    throw Exception('Fichier pubspec.yaml non trouvé');
  }
  
  final content = await pubspecFile.readAsString();
  final versionLine = content
      .split('\n')
      .firstWhere((line) => line.startsWith('version:'), orElse: () => '');
  
  if (versionLine.isEmpty) {
    throw Exception('Ligne de version non trouvée dans pubspec.yaml');
  }
  
  final version = versionLine.split(':')[1].trim();
  print('   ✅ Version trouvée: $version');
  
  // Vérifier le format version+build
  if (!version.contains('+')) {
    throw Exception('Format de version invalide (doit contenir +build)');
  }
  
  final parts = version.split('+');
  if (parts.length != 2) {
    throw Exception('Format de version invalide');
  }
  
  print('   ✅ Format de version valide');
}

/// Tester le service de version
Future<void> testVersionService() async {
  print('\n🔧 Test du VersionService...');
  
  final serviceFile = File('lib/services/version_service.dart');
  if (!await serviceFile.exists()) {
    throw Exception('Fichier VersionService non trouvé');
  }
  
  final content = await serviceFile.readAsString();
  
  // Vérifier la présence des méthodes essentielles
  final requiredMethods = [
    'get version',
    'get buildNumber',
    'get fullVersion',
    'getChangelog()',
    '_clearCache()',
    'initialize()',
  ];
  
  for (final method in requiredMethods) {
    if (!content.contains(method)) {
      throw Exception('Méthode manquante dans VersionService: $method');
    }
  }
  
  print('   ✅ Toutes les méthodes requises sont présentes');
  
  // Vérifier la présence du changelog pour la version actuelle
  if (!content.contains('getChangelog()')) {
    throw Exception('Méthode getChangelog() manquante');
  }
  
  print('   ✅ VersionService est complet');
}

/// Tester le changelog
Future<void> testChangelog() async {
  print('\n📝 Test du changelog...');
  
  final changelogFile = File('CHANGELOG.md');
  if (!await changelogFile.exists()) {
    print('   ⚠️  Fichier CHANGELOG.md non trouvé (optionnel)');
    return;
  }
  
  final content = await changelogFile.readAsString();
  
  if (content.isEmpty) {
    throw Exception('Fichier CHANGELOG.md vide');
  }
  
  // Vérifier la structure de base
  if (!content.contains('# Changelog')) {
    print('   ⚠️  En-tête de changelog manquant');
  }
  
  print('   ✅ Fichier changelog présent et valide');
}

/// Tester les scripts
Future<void> testScripts() async {
  print('\n🛠️  Test des scripts...');
  
  final scripts = [
    'scripts/update_version.dart',
    'scripts/build_and_deploy.dart',
  ];
  
  for (final scriptPath in scripts) {
    final scriptFile = File(scriptPath);
    if (!await scriptFile.exists()) {
      throw Exception('Script manquant: $scriptPath');
    }
    
    final content = await scriptFile.readAsString();
    if (content.isEmpty) {
      throw Exception('Script vide: $scriptPath');
    }
  }
  
  print('   ✅ Tous les scripts sont présents');
}

/// Tester la page d'informations
Future<void> testAppInfoPage() async {
  print('\n📱 Test de la page d\'informations...');
  
  final pageFile = File('lib/pages/app_info_page.dart');
  if (!await pageFile.exists()) {
    throw Exception('Page AppInfoPage non trouvée');
  }
  
  final content = await pageFile.readAsString();
  
  // Vérifier les imports essentiels
  final requiredImports = [
    'version_service.dart',
    'package:flutter/material.dart',
  ];
  
  for (final import in requiredImports) {
    if (!content.contains(import)) {
      throw Exception('Import manquant dans AppInfoPage: $import');
    }
  }
  
  print('   ✅ Page AppInfoPage est complète');
}
