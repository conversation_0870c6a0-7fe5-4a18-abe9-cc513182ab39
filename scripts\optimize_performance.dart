import 'dart:io';
import 'dart:developer' as developer;

/// Script d'optimisation automatique des performances
void main() async {
  developer.log('🚀 Démarrage de l\'optimisation des performances...');
  
  final optimizer = PerformanceOptimizationScript();
  await optimizer.run();
  
  developer.log('✅ Optimisation terminée !');
}

class PerformanceOptimizationScript {
  final List<String> _optimizationTasks = [];
  
  Future<void> run() async {
    developer.log('📊 Analyse des performances en cours...');
    
    // 1. Analyser les fichiers Dart
    await _analyzeDartFiles();
    
    // 2. Optimiser les images
    await _optimizeImages();
    
    // 3. Nettoyer les caches
    await _cleanCaches();
    
    // 4. Optimiser les dépendances
    await _optimizeDependencies();
    
    // 5. Générer le rapport
    await _generateReport();
  }
  
  Future<void> _analyzeDartFiles() async {
    developer.log('🔍 Analyse des fichiers Dart...');
    
    final libDir = Directory('lib');
    if (!libDir.existsSync()) {
      developer.log('❌ Dossier lib non trouvé');
      return;
    }
    
    final dartFiles = libDir
        .listSync(recursive: true)
        .whereType<File>()
        .where((file) => file.path.endsWith('.dart'))
        .toList();
    
    for (final file in dartFiles) {
      await _analyzeFile(file);
    }
    
    developer.log('✅ Analyse des fichiers terminée (${dartFiles.length} fichiers)');
  }
  
  Future<void> _analyzeFile(File file) async {
    final content = await file.readAsString();
    final lines = content.split('\n');
    
    // Rechercher les problèmes de performance
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();
      
      // setState() dans des boucles
      if (line.contains('setState') && _isInLoop(lines, i)) {
        _optimizationTasks.add(
          '⚠️  setState dans une boucle détecté: ${file.path}:${i + 1}'
        );
      }
      
      // Image.network sans optimisation
      if (line.contains('Image.network') && !line.contains('cacheWidth')) {
        _optimizationTasks.add(
          '🖼️  Image.network non optimisée: ${file.path}:${i + 1}'
        );
      }
      
      // ListView sans builder
      if (line.contains('ListView(') && !line.contains('ListView.builder')) {
        _optimizationTasks.add(
          '📋 ListView non optimisée: ${file.path}:${i + 1}'
        );
      }
      
      // Animations multiples
      if (line.contains('AnimationController') && _hasMultipleAnimations(lines, i)) {
        _optimizationTasks.add(
          '🎬 Animations multiples détectées: ${file.path}:${i + 1}'
        );
      }
    }
  }
  
  bool _isInLoop(List<String> lines, int index) {
    // Rechercher for, while, forEach dans les lignes précédentes
    for (int i = index - 1; i >= 0 && i >= index - 10; i--) {
      final line = lines[i].trim();
      if (line.contains('for (') || 
          line.contains('while (') || 
          line.contains('.forEach(')) {
        return true;
      }
    }
    return false;
  }
  
  bool _hasMultipleAnimations(List<String> lines, int index) {
    int animationCount = 0;
    for (int i = index; i < lines.length && i < index + 20; i++) {
      if (lines[i].contains('AnimationController')) {
        animationCount++;
      }
    }
    return animationCount > 2;
  }
  
  Future<void> _optimizeImages() async {
    developer.log('🖼️  Optimisation des images...');
    
    final assetsDir = Directory('assets');
    if (!assetsDir.existsSync()) {
      developer.log('📁 Dossier assets non trouvé');
      return;
    }
    
    final imageFiles = assetsDir
        .listSync(recursive: true)
        .whereType<File>()
        .where((file) => _isImageFile(file.path))
        .toList();
    
    for (final file in imageFiles) {
      final stats = await file.stat();
      final sizeInMB = stats.size / (1024 * 1024);
      
      if (sizeInMB > 1.0) {
        _optimizationTasks.add(
          '📦 Image volumineuse (${sizeInMB.toStringAsFixed(2)}MB): ${file.path}'
        );
      }
    }
    
    developer.log('✅ Analyse des images terminée (${imageFiles.length} images)');
  }
  
  bool _isImageFile(String path) {
    final extensions = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];
    return extensions.any((ext) => path.toLowerCase().endsWith(ext));
  }
  
  Future<void> _cleanCaches() async {
    developer.log('🧹 Nettoyage des caches...');
    
    final buildDir = Directory('build');
    if (buildDir.existsSync()) {
      try {
        await buildDir.delete(recursive: true);
        developer.log('✅ Cache build supprimé');
      } catch (e) {
        developer.log('❌ Erreur lors de la suppression du cache build: $e');
      }
    }
    
    final dartToolDir = Directory('.dart_tool');
    if (dartToolDir.existsSync()) {
      try {
        await dartToolDir.delete(recursive: true);
        developer.log('✅ Cache dart_tool supprimé');
      } catch (e) {
        developer.log('❌ Erreur lors de la suppression du cache dart_tool: $e');
      }
    }
  }
  
  Future<void> _optimizeDependencies() async {
    developer.log('📦 Analyse des dépendances...');
    
    final pubspecFile = File('pubspec.yaml');
    if (!pubspecFile.existsSync()) {
      developer.log('❌ pubspec.yaml non trouvé');
      return;
    }
    
    final content = await pubspecFile.readAsString();
    final lines = content.split('\n');
    
    // Rechercher les dépendances non utilisées
    for (final line in lines) {
      if (line.trim().startsWith('- ') && line.contains(':')) {
        final packageName = line.split(':')[0].trim().replaceAll('- ', '');
        if (await _isPackageUnused(packageName)) {
          _optimizationTasks.add(
            '📦 Dépendance potentiellement non utilisée: $packageName'
          );
        }
      }
    }
  }
  
  Future<bool> _isPackageUnused(String packageName) async {
    // Rechercher l'utilisation du package dans les fichiers Dart
    final libDir = Directory('lib');
    if (!libDir.existsSync()) return true;
    
    final dartFiles = libDir
        .listSync(recursive: true)
        .whereType<File>()
        .where((file) => file.path.endsWith('.dart'));
    
    for (final file in dartFiles) {
      final content = await file.readAsString();
      if (content.contains('package:$packageName') || 
          content.contains("'$packageName'") ||
          content.contains('"$packageName"')) {
        return false;
      }
    }
    
    return true;
  }
  
  Future<void> _generateReport() async {
    developer.log('📋 Génération du rapport d\'optimisation...');
    
    final reportFile = File('performance_report.md');
    final buffer = StringBuffer();
    
    buffer.writeln('# Rapport d\'Optimisation des Performances');
    buffer.writeln('');
    buffer.writeln('Généré le: ${DateTime.now()}');
    buffer.writeln('');
    
    if (_optimizationTasks.isEmpty) {
      buffer.writeln('✅ **Aucun problème de performance détecté !**');
    } else {
      buffer.writeln('## Problèmes détectés (${_optimizationTasks.length})');
      buffer.writeln('');
      
      for (final task in _optimizationTasks) {
        buffer.writeln('- $task');
      }
    }
    
    buffer.writeln('');
    buffer.writeln('## Recommandations générales');
    buffer.writeln('');
    buffer.writeln('1. **Utilisez ListView.builder** pour les listes longues');
    buffer.writeln('2. **Optimisez les images** avec cacheWidth/cacheHeight');
    buffer.writeln('3. **Limitez les setState()** et utilisez des widgets const');
    buffer.writeln('4. **Implémentez la pagination** pour les grandes listes');
    buffer.writeln('5. **Utilisez le cache** pour les données fréquemment utilisées');
    buffer.writeln('6. **Évitez les animations simultanées** multiples');
    
    await reportFile.writeAsString(buffer.toString());
    
    developer.log('✅ Rapport généré: performance_report.md');
    developer.log('📊 Problèmes détectés: ${_optimizationTasks.length}');
  }
}
