import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import '../models/advanced_task.dart';

class CalendarView extends StatefulWidget {
  final List<AdvancedTask> tasks;
  final Function(AdvancedTask) onTaskTap;

  const CalendarView({super.key, required this.tasks, required this.onTaskTap});

  @override
  State<CalendarView> createState() => _CalendarViewState();
}

class _CalendarViewState extends State<CalendarView> {
  late final ValueNotifier<List<AdvancedTask>> _selectedTasks;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    _selectedTasks = ValueNotifier(_getTasksForDay(_selectedDay!));
  }

  @override
  void dispose() {
    _selectedTasks.dispose();
    super.dispose();
  }

  List<AdvancedTask> _getTasksForDay(DateTime day) {
    return widget.tasks.where((task) {
      if (task.dueDate == null) return false;
      return isSameDay(task.dueDate!, day);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return Column(
      children: [
        _buildCalendarHeader(isMobile),
        Expanded(
          child: isMobile ? _buildMobileLayout() : _buildDesktopLayout(),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        Expanded(flex: 2, child: _buildCalendar()),
        Container(height: 1, color: Colors.grey.withValues(alpha: 0.2)),
        Expanded(flex: 1, child: _buildTasksList()),
      ],
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      children: [
        Expanded(flex: 2, child: _buildCalendar()),
        Container(width: 1, color: Colors.grey.withValues(alpha: 0.2)),
        Expanded(flex: 1, child: _buildTasksList()),
      ],
    );
  }

  Widget _buildCalendarHeader(bool isMobile) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 12 : 16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child:
          isMobile
              ? Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Calendrier',
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  _buildFormatToggle(isMobile),
                ],
              )
              : Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Calendrier des tâches',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const Spacer(),
                  _buildFormatToggle(isMobile),
                ],
              ),
    );
  }

  Widget _buildFormatToggle(bool isMobile) {
    if (isMobile) {
      return PopupMenuButton<CalendarFormat>(
        icon: const Icon(Icons.view_module),
        onSelected: (format) {
          setState(() {
            _calendarFormat = format;
          });
        },
        itemBuilder:
            (context) => [
              const PopupMenuItem(
                value: CalendarFormat.month,
                child: Row(
                  children: [
                    Icon(Icons.calendar_view_month),
                    SizedBox(width: 8),
                    Text('Mois'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: CalendarFormat.twoWeeks,
                child: Row(
                  children: [
                    Icon(Icons.calendar_view_week),
                    SizedBox(width: 8),
                    Text('2 semaines'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: CalendarFormat.week,
                child: Row(
                  children: [
                    Icon(Icons.view_week),
                    SizedBox(width: 8),
                    Text('Semaine'),
                  ],
                ),
              ),
            ],
      );
    } else {
      return SegmentedButton<CalendarFormat>(
        segments: const [
          ButtonSegment(
            value: CalendarFormat.month,
            label: Text('Mois'),
            icon: Icon(Icons.calendar_view_month),
          ),
          ButtonSegment(
            value: CalendarFormat.twoWeeks,
            label: Text('2 sem.'),
            icon: Icon(Icons.calendar_view_week),
          ),
          ButtonSegment(
            value: CalendarFormat.week,
            label: Text('Semaine'),
            icon: Icon(Icons.view_week),
          ),
        ],
        selected: {_calendarFormat},
        onSelectionChanged: (Set<CalendarFormat> newSelection) {
          setState(() {
            _calendarFormat = newSelection.first;
          });
        },
      );
    }
  }

  Widget _buildCalendar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TableCalendar<AdvancedTask>(
        firstDay: DateTime.utc(2020, 1, 1),
        lastDay: DateTime.utc(2030, 12, 31),
        focusedDay: _focusedDay,
        calendarFormat: _calendarFormat,
        eventLoader: _getTasksForDay,
        startingDayOfWeek: StartingDayOfWeek.monday,
        calendarStyle: CalendarStyle(
          outsideDaysVisible: false,
          weekendTextStyle: TextStyle(color: Colors.red[400]),
          holidayTextStyle: TextStyle(color: Colors.red[400]),
          markerDecoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          selectedDecoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          todayDecoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
            shape: BoxShape.circle,
          ),
        ),
        headerStyle: const HeaderStyle(
          formatButtonVisible: false,
          titleCentered: true,
          leftChevronIcon: Icon(Icons.chevron_left),
          rightChevronIcon: Icon(Icons.chevron_right),
        ),
        onDaySelected: _onDaySelected,
        onFormatChanged: (format) {
          if (_calendarFormat != format) {
            setState(() {
              _calendarFormat = format;
            });
          }
        },
        onPageChanged: (focusedDay) {
          _focusedDay = focusedDay;
        },
        selectedDayPredicate: (day) {
          return isSameDay(_selectedDay, day);
        },
        calendarBuilders: CalendarBuilders(
          markerBuilder: (context, day, tasks) {
            if (tasks.isEmpty) return null;

            return Positioned(
              bottom: 1,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children:
                    tasks.take(3).map((task) {
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 1),
                        width: 6,
                        height: 6,
                        decoration: BoxDecoration(
                          color: task.priority.color,
                          shape: BoxShape.circle,
                        ),
                      );
                    }).toList(),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildTasksList() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _selectedDay != null
                ? 'Tâches du ${_formatSelectedDate(_selectedDay!)}'
                : 'Sélectionnez une date',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ValueListenableBuilder<List<AdvancedTask>>(
              valueListenable: _selectedTasks,
              builder: (context, tasks, _) {
                if (tasks.isEmpty) {
                  return _buildEmptyTasksList();
                }

                return ListView.builder(
                  itemCount: tasks.length,
                  itemBuilder: (context, index) {
                    final task = tasks[index];
                    return _buildTaskItem(task);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTasksList() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_available,
            size: 64,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Aucune tâche',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Aucune tâche prévue\npour cette date',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey.withValues(alpha: 0.5)),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskItem(AdvancedTask task) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => widget.onTaskTap(task),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: task.priority.color,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      task.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  _buildStatusChip(task.status),
                ],
              ),

              if (task.description.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  task.description,
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 8),

              Row(
                children: [
                  Icon(Icons.flag, color: task.priority.color, size: 14),
                  const SizedBox(width: 4),
                  Text(
                    task.priority.displayName,
                    style: TextStyle(fontSize: 11, color: task.priority.color),
                  ),
                  const Spacer(),
                  if (task.isOverdue)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'En retard',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(TaskStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getStatusColor(status).withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          fontSize: 10,
          color: _getStatusColor(status),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.inReview:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.blocked:
        return Colors.red;
    }
  }

  void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
    if (!isSameDay(_selectedDay, selectedDay)) {
      setState(() {
        _selectedDay = selectedDay;
        _focusedDay = focusedDay;
      });
      _selectedTasks.value = _getTasksForDay(selectedDay);
    }
  }

  String _formatSelectedDate(DateTime date) {
    final now = DateTime.now();
    if (isSameDay(date, now)) {
      return "aujourd'hui";
    } else if (isSameDay(date, now.add(const Duration(days: 1)))) {
      return "demain";
    } else if (isSameDay(date, now.subtract(const Duration(days: 1)))) {
      return "hier";
    } else {
      final months = [
        'janvier',
        'février',
        'mars',
        'avril',
        'mai',
        'juin',
        'juillet',
        'août',
        'septembre',
        'octobre',
        'novembre',
        'décembre',
      ];
      return "${date.day} ${months[date.month - 1]} ${date.year}";
    }
  }
}
