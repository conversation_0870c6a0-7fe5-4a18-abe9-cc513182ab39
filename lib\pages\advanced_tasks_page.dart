import 'package:flutter/material.dart';
import '../models/advanced_task.dart';
import '../models/task_user.dart';
import '../services/advanced_task_service.dart';
import '../widgets/advanced_task_widgets.dart';
import '../widgets/task_dialogs.dart';
import '../widgets/kanban_view.dart';
import '../widgets/calendar_view.dart';
import '../widgets/timeline_view.dart';
import 'task_detail_page.dart';

class AdvancedTasksPage extends StatefulWidget {
  const AdvancedTasksPage({super.key});

  @override
  State<AdvancedTasksPage> createState() => _AdvancedTasksPageState();
}

class _AdvancedTasksPageState extends State<AdvancedTasksPage>
    with TickerProviderStateMixin {
  final AdvancedTaskService _taskService = AdvancedTaskService();

  late TabController _tabController;
  TaskFilter _currentFilter = TaskFilter();
  TaskSortOption _currentSort = TaskSortOption.dueDate;

  List<Project> _projects = [];
  List<AdvancedTask> _tasks = [];

  bool _isLoading = true;
  String? _selectedProjectId;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      await _taskService.initialize();

      final projects = await _taskService.getProjects();
      final tasks = await _taskService.getTasks(filter: _currentFilter);

      setState(() {
        _projects = projects;
        _tasks = tasks;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return Scaffold(
      appBar: AppBar(
        title: Text('Tâches', style: TextStyle(fontSize: isMobile ? 18 : 20)),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        elevation: 0,
        bottom: _buildResponsiveTabBar(context, isMobile),
        actions: _buildAppBarActions(context, isMobile),
      ),
      body: SafeArea(
        child:
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : Column(
                  children: [
                    // Barre de statistiques compacte
                    _buildCompactStatsBar(isMobile),

                    // Contenu principal
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildListView(),
                          _buildKanbanView(),
                          _buildCalendarView(),
                          _buildTimelineView(),
                        ],
                      ),
                    ),
                  ],
                ),
      ),
      floatingActionButton: Padding(
        padding: EdgeInsets.only(
          bottom: 70 + 16 * 2 + MediaQuery.viewPaddingOf(context).bottom + 16,
        ),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          child: FloatingActionButton(
            onPressed: _showAddTaskDialog,
            tooltip: 'Nouvelle tâche',
            backgroundColor: Theme.of(context).primaryColor,
            foregroundColor: Colors.white,
            elevation: 6,
            highlightElevation: 12,
            child: const Icon(Icons.add),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactStatsBar(bool isMobile) {
    final totalTasks = _tasks.length;
    final completedTasks = _tasks.where((t) => t.isCompleted).length;
    final overdueTasks = _tasks.where((t) => t.isOverdue).length;
    final todayTasks =
        _tasks
            .where(
              (t) =>
                  t.dueDate != null &&
                  DateUtils.isSameDay(t.dueDate!, DateTime.now()),
            )
            .length;

    if (isMobile) {
      // Version mobile : bandeau horizontal compact avec pastilles colorées
      return Container(
        margin: const EdgeInsets.all(8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor.withValues(alpha: 0.1),
              Theme.of(context).primaryColor.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.1)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildCompactStatChip(totalTasks, 'Total', Colors.blue),
            _buildCompactStatChip(completedTasks, 'OK', Colors.green),
            _buildCompactStatChip(overdueTasks, 'Retard', Colors.red),
            _buildCompactStatChip(todayTasks, 'Aujourd\'hui', Colors.orange),
          ],
        ),
      );
    } else {
      // Version desktop : barre classique
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          border: Border(
            bottom: BorderSide(color: Theme.of(context).dividerColor, width: 1),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              'Total',
              totalTasks.toString(),
              Icons.task_alt,
              Colors.blue,
            ),
            _buildStatItem(
              'Terminées',
              completedTasks.toString(),
              Icons.check_circle,
              Colors.green,
            ),
            _buildStatItem(
              'En retard',
              overdueTasks.toString(),
              Icons.warning,
              Colors.red,
            ),
            _buildStatItem(
              'Aujourd\'hui',
              todayTasks.toString(),
              Icons.today,
              Colors.orange,
            ),
          ],
        ),
      );
    }
  }

  Widget _buildCompactStatChip(int count, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 4),
          Text(
            '$count',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
              color: color,
            ),
          ),
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(fontSize: 10, color: color.withValues(alpha: 0.8)),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(label, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }

  Widget _buildListView() {
    if (_tasks.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _tasks.length,
      itemBuilder: (context, index) {
        final task = _tasks[index];
        return TaskListTile(
          task: task,
          onTap: () => _showTaskDetails(task),
          onStatusChanged: (newStatus) => _updateTaskStatus(task, newStatus),
        );
      },
    );
  }

  Widget _buildKanbanView() {
    if (_tasks.isEmpty) {
      return _buildEmptyState();
    }
    return KanbanView(
      tasks: _tasks,
      onTaskTap: _showTaskDetails,
      onStatusChanged: _updateTaskStatus,
    );
  }

  Widget _buildCalendarView() {
    if (_tasks.isEmpty) {
      return _buildEmptyState();
    }
    return CalendarView(tasks: _tasks, onTaskTap: _showTaskDetails);
  }

  Widget _buildTimelineView() {
    if (_tasks.isEmpty) {
      return _buildEmptyState();
    }
    return TimelineView(tasks: _tasks, onTaskTap: _showTaskDetails);
  }

  IconData _getSortIcon(TaskSortOption option) {
    switch (option) {
      case TaskSortOption.dueDate:
        return Icons.schedule;
      case TaskSortOption.priority:
        return Icons.priority_high;
      case TaskSortOption.status:
        return Icons.flag;
      case TaskSortOption.assignee:
        return Icons.person;
      case TaskSortOption.createdDate:
        return Icons.access_time;
      case TaskSortOption.title:
        return Icons.title;
      case TaskSortOption.project:
        return Icons.folder;
    }
  }

  void _applySorting() {
    setState(() {
      _tasks.sort((a, b) {
        switch (_currentSort) {
          case TaskSortOption.dueDate:
            if (a.dueDate == null && b.dueDate == null) return 0;
            if (a.dueDate == null) return 1;
            if (b.dueDate == null) return -1;
            return a.dueDate!.compareTo(b.dueDate!);
          case TaskSortOption.priority:
            return b.priority.index.compareTo(a.priority.index);
          case TaskSortOption.status:
            return a.status.index.compareTo(b.status.index);
          case TaskSortOption.title:
            return a.title.compareTo(b.title);
          case TaskSortOption.createdDate:
            return b.createdAt.compareTo(a.createdAt);
          default:
            return 0;
        }
      });
    });
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => FilterDialog(
            currentFilter: _currentFilter,
            onFilterChanged: (filter) {
              setState(() {
                _currentFilter = filter;
              });
              _loadData();
            },
          ),
    );
  }

  void _showAddTaskDialog() {
    showDialog(
      context: context,
      builder:
          (context) => TaskDialog(
            projectId: _selectedProjectId,
            onTaskSaved: (task) async {
              final messenger = ScaffoldMessenger.of(context);
              try {
                await _taskService.addTask(task);
                _loadData();
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Tâche "${task.title}" créée avec succès'),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Erreur lors de la création: $e')),
                  );
                }
              }
            },
          ),
    );
  }

  void _showTaskDetails(AdvancedTask task) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => TaskDetailPage(task: task)),
    ).then((_) => _loadData()); // Recharger les données au retour
  }

  void _updateTaskStatus(AdvancedTask task, TaskStatus newStatus) async {
    try {
      final updatedTask = task.copyWith(status: newStatus);
      await _taskService.updateTask(updatedTask);
      _loadData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la mise à jour: $e')),
        );
      }
    }
  }

  // Méthodes pour l'interface responsive
  PreferredSizeWidget _buildResponsiveTabBar(
    BuildContext context,
    bool isMobile,
  ) {
    if (isMobile) {
      return TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        tabs: const [
          Tab(icon: Icon(Icons.list, size: 20), text: 'Liste'),
          Tab(icon: Icon(Icons.view_kanban, size: 20), text: 'Kanban'),
          Tab(icon: Icon(Icons.calendar_month, size: 20), text: 'Calendrier'),
          Tab(icon: Icon(Icons.timeline, size: 20), text: 'Timeline'),
        ],
      );
    } else {
      return TabBar(
        controller: _tabController,
        tabs: const [
          Tab(icon: Icon(Icons.list), text: 'Liste'),
          Tab(icon: Icon(Icons.view_kanban), text: 'Kanban'),
          Tab(icon: Icon(Icons.calendar_month), text: 'Calendrier'),
          Tab(icon: Icon(Icons.timeline), text: 'Timeline'),
        ],
      );
    }
  }

  List<Widget> _buildAppBarActions(BuildContext context, bool isMobile) {
    if (isMobile) {
      // Version mobile : menu hamburger avec toutes les options
      return [
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'filter':
                _showFilterDialog();
                break;
              case 'sort':
                _showSortDialog();
                break;

              case 'project':
                _showProjectSelector();
                break;
            }
          },
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'project',
                  child: Row(
                    children: [
                      Icon(Icons.folder),
                      SizedBox(width: 8),
                      Text('Changer de projet'),
                    ],
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'filter',
                  child: Row(
                    children: [
                      Icon(Icons.filter_list),
                      SizedBox(width: 8),
                      Text('Filtres'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'sort',
                  child: Row(
                    children: [
                      Icon(Icons.sort),
                      SizedBox(width: 8),
                      Text('Trier'),
                    ],
                  ),
                ),
              ],
        ),
      ];
    } else {
      // Version tablette/desktop : boutons séparés
      return [
        if (_projects.isNotEmpty)
          PopupMenuButton<String>(
            icon: const Icon(Icons.folder),
            tooltip: 'Sélectionner un projet',
            onSelected: (projectId) {
              setState(() {
                _selectedProjectId = projectId == 'all' ? null : projectId;
                _currentFilter = _currentFilter.copyWith(
                  projectId: _selectedProjectId,
                );
              });
              _loadData();
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'all',
                    child: Row(
                      children: [
                        Icon(Icons.all_inclusive),
                        SizedBox(width: 8),
                        Text('Tous les projets'),
                      ],
                    ),
                  ),
                  const PopupMenuDivider(),
                  ..._projects.map(
                    (project) => PopupMenuItem(
                      value: project.id,
                      child: Row(
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: project.color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(child: Text(project.name)),
                        ],
                      ),
                    ),
                  ),
                ],
          ),
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterDialog,
          tooltip: 'Filtres',
        ),
        PopupMenuButton<TaskSortOption>(
          icon: const Icon(Icons.sort),
          tooltip: 'Trier',
          onSelected: (sortOption) {
            setState(() => _currentSort = sortOption);
            _applySorting();
          },
          itemBuilder:
              (context) =>
                  TaskSortOption.values
                      .map(
                        (option) => PopupMenuItem(
                          value: option,
                          child: Row(
                            children: [
                              Icon(_getSortIcon(option)),
                              const SizedBox(width: 8),
                              Text(option.displayName),
                            ],
                          ),
                        ),
                      )
                      .toList(),
        ),
        IconButton(
          icon: const Icon(Icons.add),
          onPressed: _showAddTaskDialog,
          tooltip: 'Nouvelle tâche',
        ),
      ];
    }
  }

  void _showProjectSelector() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sélectionner un projet'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView(
                shrinkWrap: true,
                children: [
                  ListTile(
                    leading: const Icon(Icons.all_inclusive),
                    title: const Text('Tous les projets'),
                    onTap: () {
                      setState(() {
                        _selectedProjectId = null;
                        _currentFilter = _currentFilter.copyWith(
                          projectId: null,
                        );
                      });
                      _loadData();
                      Navigator.pop(context);
                    },
                  ),
                  const Divider(),
                  ..._projects.map(
                    (project) => ListTile(
                      leading: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: project.color,
                          shape: BoxShape.circle,
                        ),
                      ),
                      title: Text(project.name),
                      onTap: () {
                        setState(() {
                          _selectedProjectId = project.id;
                          _currentFilter = _currentFilter.copyWith(
                            projectId: project.id,
                          );
                        });
                        _loadData();
                        Navigator.pop(context);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Trier par'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children:
                  TaskSortOption.values.map((option) {
                    return RadioListTile<TaskSortOption>(
                      title: Row(
                        children: [
                          Icon(_getSortIcon(option)),
                          const SizedBox(width: 8),
                          Text(option.displayName),
                        ],
                      ),
                      value: option,
                      groupValue: _currentSort,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() => _currentSort = value);
                          _applySorting();
                          Navigator.pop(context);
                        }
                      },
                    );
                  }).toList(),
            ),
          ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Illustration moderne avec gradient
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  Theme.of(context).primaryColor.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              Icons.task_alt_outlined,
              size: 64,
              color: Theme.of(context).primaryColor.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Aucune tâche trouvée',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Créez votre première tâche pour commencer',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          // Bouton d'action suggéré
          ElevatedButton.icon(
            onPressed: _showAddTaskDialog,
            icon: const Icon(Icons.add),
            label: const Text('Créer une tâche'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
