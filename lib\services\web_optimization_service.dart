import 'dart:async';
import 'package:flutter/foundation.dart';
import 'logging_service.dart';
import 'platform_uniformity_service.dart';
import 'unified_sync_service.dart';
import 'firebase_service.dart';

/// Service d'optimisation pour la version web
class WebOptimizationService {
  /// Vérifier si l'application tourne sur le web
  static bool get isWeb => kIsWeb;

  /// Initialiser les optimisations web (non-bloquant)
  static Future<void> initializeWebOptimizations() async {
    if (!isWeb) return;

    // Démarrer toutes les optimisations en arrière-plan
    Future.microtask(() async {
      try {
        LoggingService.info('🌐 Initialisation web en arrière-plan...', 'WEB');

        // IMPORTANT: Synchroniser les données mobiles existantes vers le web
        await _syncMobileDataToWeb();

        // Activer la synchronisation en temps réel pour le web
        await _enableRealtimeSync();

        // Optimiser les requêtes pour le web
        await _optimizeQueries();

        // Appliquer l'uniformité entre web et mobile
        await _applyUniformExperience();

        LoggingService.success(
          '✅ Optimisations web initialisées en arrière-plan',
          'WEB',
        );
      } catch (e) {
        LoggingService.error('Erreur lors de l\'initialisation web', 'WEB', e);
        // L'application continue de fonctionner même en cas d'erreur
      }
    });

    // Retourner immédiatement pour ne pas bloquer l'interface
    LoggingService.info(
      '🚀 Interface web prête, optimisations en cours...',
      'WEB',
    );
  }

  /// Synchroniser les données mobiles existantes vers le web
  static Future<void> _syncMobileDataToWeb() async {
    LoggingService.info(
      '🔄 Synchronisation des données mobiles vers le web...',
      'WEB',
    );

    try {
      final unifiedSync = UnifiedSyncService();
      await unifiedSync.initialize();

      LoggingService.success(
        '✅ Données mobiles synchronisées vers le web',
        'WEB',
      );
    } catch (e) {
      LoggingService.error(
        '❌ Erreur lors de la synchronisation mobile→web',
        'WEB',
        e,
      );
      // Ne pas faire échouer l'initialisation web pour autant
    }
  }

  /// Applique l'uniformité entre web et mobile
  static Future<void> _applyUniformExperience() async {
    LoggingService.info('Application de l\'uniformité web-mobile...', 'WEB');

    // Utiliser les mêmes configurations que mobile

    // S'assurer que l'expérience web est identique au mobile
    if (PlatformUniformityService.shouldUseUniformNavigation()) {
      LoggingService.info('Navigation uniforme activée', 'WEB');
    }

    LoggingService.success('Uniformité web-mobile appliquée', 'WEB');
  }

  /// Activer la synchronisation en temps réel
  static Future<void> _enableRealtimeSync() async {
    try {
      LoggingService.info(
        'Initialisation de la synchronisation temps réel avec Firebase',
        'WEB',
      );

      // Initialiser Firebase pour la synchronisation temps réel
      await FirebaseService.instance.initialize();

      // Configurer les listeners en temps réel pour chaque collection
      await _setupRealtimeListeners();

      // Effectuer une synchronisation initiale
      final unifiedSync = UnifiedSyncService();
      await unifiedSync.performIncrementalSync();

      LoggingService.success(
        'Synchronisation temps réel activée avec Firebase',
        'WEB',
      );
    } catch (e) {
      LoggingService.error(
        'Erreur lors de l\'activation de la synchronisation temps réel',
        'WEB',
        e,
      );
    }
  }

  /// Configurer les listeners en temps réel pour Firebase
  static Future<void> _setupRealtimeListeners() async {
    try {
      LoggingService.info(
        'Configuration de la synchronisation temps réel Firebase',
        'WEB',
      );

      // Démarrer une synchronisation périodique pour simuler le temps réel
      _startPeriodicSync();

      // Vérifier les données initiales
      await _checkInitialData();

      LoggingService.success('Synchronisation temps réel configurée', 'WEB');
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la configuration de la synchronisation',
        'WEB',
        e,
      );
    }
  }

  /// Démarrer une synchronisation périodique
  static void _startPeriodicSync() {
    // Synchroniser toutes les 30 secondes
    Timer.periodic(const Duration(seconds: 30), (timer) async {
      try {
        await _syncAllData();
      } catch (e) {
        LoggingService.error('Erreur lors de la sync périodique', 'WEB', e);
      }
    });
  }

  /// Vérifier les données initiales
  static Future<void> _checkInitialData() async {
    try {
      final invoices = await FirebaseService.instance.getAllInvoices();
      final tasks = await FirebaseService.instance.getAllTasks();
      final colis = await FirebaseService.instance.getAllColis();

      _notifyDataUpdate('invoices', invoices.length);
      _notifyDataUpdate('tasks', tasks.length);
      _notifyDataUpdate('colis', colis.length);
    } catch (e) {
      LoggingService.error('Erreur lors de la vérification initiale', 'WEB', e);
    }
  }

  /// Synchroniser toutes les données
  static Future<void> _syncAllData() async {
    try {
      // Forcer la synchronisation des opérations en attente
      await FirebaseService.instance.forceSyncNow();

      // Vérifier les nouvelles données
      await _checkInitialData();

      LoggingService.info('Synchronisation temps réel effectuée', 'WEB');
    } catch (e) {
      LoggingService.error('Erreur lors de la synchronisation', 'WEB', e);
    }
  }

  /// Notifier les mises à jour de données
  static void _notifyDataUpdate(String dataType, int count) {
    LoggingService.info(
      '📊 Données mises à jour: $dataType ($count éléments)',
      'WEB',
    );
    // Ici on pourrait émettre des événements pour mettre à jour l'UI en temps réel
  }

  /// Optimiser les requêtes pour le web
  static Future<void> _optimizeQueries() async {
    // Pour le web, on peut optimiser les requêtes avec des limites
    // et des paginations pour de meilleures performances
    LoggingService.info('Optimisation des requêtes web activée', 'WEB');
  }

  /// Obtenir les statistiques en temps réel pour le dashboard web
  static Future<Map<String, dynamic>> getRealtimeStats() async {
    try {
      // Les statistiques sont maintenant gérées par Firebase
      LoggingService.info('Récupération des statistiques via Firebase', 'WEB');

      return {
        'totalInvoices': 0,
        'totalRevenue': 0,
        'paidInvoices': 0,
        'pendingInvoices': 0,
        'totalDeliveries': 0,
        'completedDeliveries': 0,
        'pendingDeliveries': 0,
        'totalDeliveryRevenue': 0,
        'totalProducts': 0,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      LoggingService.error(
        'Erreur lors de la récupération des statistiques',
        'WEB',
        e,
      );
      return {
        'totalInvoices': 0,
        'totalRevenue': 0,
        'paidInvoices': 0,
        'pendingInvoices': 0,
        'totalDeliveries': 0,
        'completedDeliveries': 0,
        'pendingDeliveries': 0,
        'totalDeliveryRevenue': 0,
        'totalProducts': 0,
        'lastUpdate': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Synchroniser les données locales avec Firebase
  static Future<void> syncLocalData() async {
    if (!isWeb) return;

    try {
      LoggingService.info('Synchronisation des données locales...', 'WEB');

      // Initialiser Firebase pour le web
      await FirebaseService.instance.initialize();

      // Les données sont maintenant synchronisées via Firebase
      LoggingService.info('Synchronisation Firebase activée', 'WEB');

      LoggingService.success('Synchronisation terminée', 'WEB');
    } catch (e) {
      LoggingService.error('Erreur lors de la synchronisation', 'WEB', e);
    }
  }
}
