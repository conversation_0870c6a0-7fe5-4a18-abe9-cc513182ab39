import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/user.dart';
import '../services/user_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:io';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen> {
  final UserService _userService = UserService.instance;
  final TextEditingController _searchController = TextEditingController();

  List<User> _users = [];
  List<User> _filteredUsers = [];
  String _searchQuery = '';
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _isSaving = false;
  String? _errorMessage;
  final ScrollController _scrollController = ScrollController();
  bool _hasMoreUsers = true; // Indique s'il y a plus d'utilisateurs à charger

  @override
  void initState() {
    super.initState();
    _checkConnectivityAndLoadUsers();
    _searchController.addListener(_onSearchChanged);
    
    // Ajouter un écouteur de défilement pour le chargement progressif
    _scrollController.addListener(_scrollListener);
  }
  
  // Écouteur de défilement pour détecter quand l'utilisateur atteint la fin de la liste
  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 500 &&
        !_isLoadingMore && _hasMoreUsers && !_isLoading && _searchQuery.isEmpty) {
      _loadMoreUsers();
    }
  }
  
  // Charger plus d'utilisateurs
  Future<void> _loadMoreUsers() async {
    if (!_hasMoreUsers || _isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
    });
    
    try {
      // Simuler un délai pour éviter les requêtes trop fréquentes
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Charger plus d'utilisateurs à partir du dernier ID
      final lastUserId = _users.isNotEmpty ? _users.last.id : null;
      final moreUsers = await _userService.getMoreUsers(lastUserId);
      
      if (moreUsers.isEmpty) {
        setState(() {
          _hasMoreUsers = false;
          _isLoadingMore = false;
        });
        return;
      }
      
      setState(() {
        _users.addAll(moreUsers);
        _filterUsers();
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingMore = false;
      });
    }
  }
  
  // Vérifier la connectivité avant de charger les utilisateurs
  Future<void> _checkConnectivityAndLoadUsers() async {
    try {
      // Vérifier la connectivité
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Aucune connexion Internet. Veuillez vérifier votre connexion et réessayer.';
        });
        return;
      }
      
      // Vérifier si le serveur est accessible
      try {
        final result = await InternetAddress.lookup('firebase.google.com');
        if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
          // Internet est disponible, charger les utilisateurs
          _loadUsers();
        }
      } on SocketException catch (_) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Problème de connexion au serveur. Veuillez réessayer plus tard.';
        });
      }
    } catch (e) {
      // En cas d'erreur, essayer quand même de charger les utilisateurs
      _loadUsers();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text;
      _filterUsers();
    });
  }

  void _filterUsers() {
    if (_searchQuery.isEmpty) {
      _filteredUsers = List.from(_users);
    } else {
      final query = _searchQuery.toLowerCase();
      _filteredUsers =
          _users.where((user) {
            return user.name.toLowerCase().contains(query) ||
                user.email.toLowerCase().contains(query) ||
                user.role.toLowerCase().contains(query);
          }).toList();
    }
  }

  Future<void> _loadUsers() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Ajouter un timeout pour éviter un chargement interminable
      final users = await _userService.getUsers().timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          throw Exception('Le chargement a pris trop de temps. Veuillez réessayer.');
        },
      );

      if (!mounted) return;

      setState(() {
        _users = users;
        _filterUsers();
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      String errorMsg = 'Erreur lors du chargement';
      
      if (e.toString().contains('timeout') || e.toString().contains('trop de temps')) {
        errorMsg = 'Le chargement a pris trop de temps. Veuillez réessayer.';
      } else if (e.toString().contains('network')) {
        errorMsg = 'Problème de connexion réseau. Vérifiez votre connexion et réessayez.';
      } else if (e.toString().contains('permission') || e.toString().contains('autorisé')) {
        errorMsg = 'Vous n\'avez pas les permissions nécessaires pour accéder à ces données.';
      } else {
        errorMsg = 'Erreur lors du chargement: ${e.toString()}';
      }

      setState(() {
        _errorMessage = errorMsg;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            const Text('Gestion des Utilisateurs'),
            if (_isSaving) ...[              const SizedBox(width: 16),
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ],
          ],
        ),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF2196F3), // Bleu
                Color(0xFF3F51B5), // Indigo
                Color(0xFF9C27B0), // Violet
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _isSaving ? null : () => _showAddUserDialog(),
            tooltip: 'Ajouter un utilisateur',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isSaving ? null : () {
              // Vider le cache et recharger les utilisateurs
              setState(() {
                _hasMoreUsers = true;
                _isLoading = true;
                _errorMessage = null;
              });
              _checkConnectivityAndLoadUsers();
            },
            tooltip: 'Actualiser la liste',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'export':
                  _exportUsers();
                  break;
                case 'stats':
                  _showUserStats();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'export',
                    child: ListTile(
                      leading: Icon(Icons.download),
                      title: Text('Exporter'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'stats',
                    child: ListTile(
                      leading: Icon(Icons.analytics),
                      title: Text('Statistiques'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Column(
        children: [
          // Barre de recherche
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                labelText: 'Rechercher un utilisateur',
                hintText: 'Nom, email ou rôle...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon:
                    _searchQuery.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            setState(() => _searchQuery = '');
                          },
                        )
                        : null,
                border: const OutlineInputBorder(),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
          ),

          // Liste des utilisateurs
          Expanded(child: _buildUsersList()),
        ],
      ),
    );
  }

  Widget _buildUsersList() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Indicateur de chargement plus visible
            SizedBox(
              width: 60,
              height: 60,
              child: CircularProgressIndicator(
                strokeWidth: 4,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              'Chargement des utilisateurs...',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Text(
              'Veuillez patienter...',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            // Option pour annuler le chargement après 5 secondes
            if (_isLoading) ...[  
              const SizedBox(height: 32),
              TextButton.icon(
                onPressed: () {
                  setState(() {
                    _isLoading = false;
                    _errorMessage = 'Chargement annulé par l\'utilisateur';
                  });
                },
                icon: const Icon(Icons.cancel_outlined),
                label: const Text('Annuler'),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
              ),
            ],
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(24),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                // ignore: deprecated_member_use
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 2,
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _errorMessage!.contains('connexion') || _errorMessage!.contains('réseau')
                    ? Icons.wifi_off_rounded
                    : _errorMessage!.contains('temps') || _errorMessage!.contains('timeout')
                        ? Icons.timer_off_rounded
                        : _errorMessage!.contains('permission') || _errorMessage!.contains('autorisé')
                            ? Icons.no_accounts_rounded
                            : Icons.error_outline_rounded,
                color: _errorMessage!.contains('annulé') ? Colors.orange : Colors.red[300],
                size: 70,
              ),
              const SizedBox(height: 24),
              Text(
                _errorMessage!,
                style: TextStyle(
                  color: _errorMessage!.contains('annulé') ? Colors.orange[700] : Colors.red[700],
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                     onPressed: () {
                       setState(() {
                         _isLoading = true;
                         _errorMessage = null;
                       });
                       _checkConnectivityAndLoadUsers();
                     },
                     icon: const Icon(Icons.refresh_rounded),
                     label: const Text('Réessayer'),
                     style: ElevatedButton.styleFrom(
                       backgroundColor: Colors.blue,
                       foregroundColor: Colors.white,
                       padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                     ),
                   ),
                  const SizedBox(width: 16),
                  OutlinedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    icon: const Icon(Icons.arrow_back_rounded),
                    label: const Text('Retour'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    }

    if (_filteredUsers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _searchQuery.isNotEmpty ? Icons.search_off : Icons.people_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? 'Aucun utilisateur trouvé pour "$_searchQuery"'
                  : 'Aucun utilisateur trouvé',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
            ),
            if (!_hasMoreUsers && _users.isNotEmpty && _searchQuery.isEmpty) ...[  
              const SizedBox(height: 16),
              OutlinedButton.icon(
                onPressed: () {
                  setState(() {
                    _hasMoreUsers = true;
                    _isLoading = true;
                    _errorMessage = null;
                  });
                  _checkConnectivityAndLoadUsers();
                },
                icon: const Icon(Icons.refresh_rounded),
                label: const Text('Rafraîchir'),
              ),
            ],
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              TextButton.icon(
                onPressed: () {
                  _searchController.clear();
                  setState(() => _searchQuery = '');
                },
                icon: const Icon(Icons.clear),
                label: const Text('Effacer la recherche'),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUsers,
      child: Column(
        children: [
          // Indicateur de progression
          if (!_isLoading && _searchQuery.isEmpty) ...[
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${_users.length} utilisateurs chargés',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                  if (!_hasMoreUsers)
                    Text(
                      'Tous les utilisateurs sont chargés',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                ],
              ),
            ),
          ],
          Expanded(
            child: AnimatedList(
              key: GlobalKey<AnimatedListState>(),
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              initialItemCount: _filteredUsers.length + (_isLoadingMore ? 1 : 0),
              itemBuilder: (context, index, animation) {
                // Afficher l'indicateur de chargement en bas de la liste
                if (index == _filteredUsers.length && _isLoadingMore) {
                  return Container(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    alignment: Alignment.center,
                    child: const CircularProgressIndicator(),
                  );
                }
                
                if (index >= _filteredUsers.length) return const SizedBox.shrink();

                final user = _filteredUsers[index];
                return SlideTransition(
                  position: animation.drive(
                    Tween(
                      begin: const Offset(1.0, 0.0),
                      end: Offset.zero,
                    ).chain(CurveTween(curve: Curves.easeOut)),
                  ),
                  child: FadeTransition(
                    opacity: animation,
                    child: _buildUserCard(user),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserCard(User user) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Semantics(
        label:
            'Utilisateur ${user.name}, ${user.email}, ${user.isActive ? "actif" : "inactif"}',
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => _showUserDetails(user),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Avatar avec indicateur de statut
                Hero(
                  tag: 'user_avatar_${user.id}',
                  child: Stack(
                    children: [
                      CircleAvatar(
                        radius: 24,
                        backgroundColor:
                            user.isActive
                                ? Colors.green[100]
                                : Colors.grey[300],
                        child: Text(
                          user.name.isNotEmpty
                              ? user.name[0].toUpperCase()
                              : 'U',
                          style: TextStyle(
                            color:
                                user.isActive
                                    ? Colors.green[800]
                                    : Colors.grey[600],
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: user.isActive ? Colors.green : Colors.red,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.white, width: 2),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),

                // Informations utilisateur
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.email,
                        style: TextStyle(color: Colors.grey[600], fontSize: 14),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (user.phone != null && user.phone!.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          user.phone!,
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      const SizedBox(height: 8),

                      // Tags de rôle et statut
                      Wrap(
                        spacing: 8,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getRoleColor(user.role),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getRoleDisplayName(user.role),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: user.isActive ? Colors.green : Colors.red,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              user.isActive ? 'Actif' : 'Inactif',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Menu d'actions
                PopupMenuButton<String>(
                  onSelected: (value) => _handleUserAction(value, user),
                  icon: const Icon(Icons.more_vert, color: Colors.grey),
                  tooltip: 'Actions pour ${user.name}',
                  itemBuilder:
                      (context) => [
                        const PopupMenuItem(
                          value: 'view',
                          child: ListTile(
                            leading: Icon(Icons.visibility, color: Colors.blue),
                            title: Text('Voir les détails'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'edit',
                          child: ListTile(
                            leading: Icon(Icons.edit, color: Colors.orange),
                            title: Text('Modifier'),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        PopupMenuItem(
                          value: user.isActive ? 'deactivate' : 'activate',
                          child: ListTile(
                            leading: Icon(
                              user.isActive ? Icons.block : Icons.check_circle,
                              color: user.isActive ? Colors.red : Colors.green,
                            ),
                            title: Text(
                              user.isActive ? 'Désactiver' : 'Activer',
                            ),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                        if (user.id != _userService.currentUser?.id)
                          const PopupMenuItem(
                            value: 'delete',
                            child: ListTile(
                              leading: Icon(Icons.delete, color: Colors.red),
                              title: Text(
                                'Supprimer',
                                style: TextStyle(color: Colors.red),
                              ),
                              contentPadding: EdgeInsets.zero,
                            ),
                          ),
                      ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return Colors.red;
      case 'manager':
        return Colors.orange;
      case 'employee':
        return Colors.blue;
      case 'viewer':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  void _handleUserAction(String action, User user) {
    switch (action) {
      case 'view':
        _showUserDetails(user);
        break;
      case 'edit':
        _showUserDialog(user: user);
        break;
      case 'activate':
      case 'deactivate':
        _toggleUserStatus(user);
        break;
      case 'delete':
        _showDeleteConfirmation(user);
        break;
    }
  }

  String _getRoleDisplayName(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'Administrateur';
      case 'manager':
        return 'Gestionnaire';
      case 'employee':
        return 'Employé';
      case 'viewer':
        return 'Observateur';
      default:
        return role;
    }
  }

  void _showUserDetails(User user) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                CircleAvatar(
                  backgroundColor:
                      user.isActive ? Colors.green[100] : Colors.grey[300],
                  child: Text(
                    user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                    style: TextStyle(
                      color:
                          user.isActive ? Colors.green[800] : Colors.grey[600],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(user.name, style: const TextStyle(fontSize: 18)),
                      Text(
                        user.isActive ? 'Actif' : 'Inactif',
                        style: TextStyle(
                          color: user.isActive ? Colors.green : Colors.red,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildDetailRow('Email', user.email, Icons.email),
                  if (user.phone != null && user.phone!.isNotEmpty)
                    _buildDetailRow('Téléphone', user.phone!, Icons.phone),
                  _buildDetailRow(
                    'Rôle',
                    _getRoleDisplayName(user.role),
                    Icons.person,
                  ),
                  _buildDetailRow(
                    'Date de création',
                    DateFormat('dd/MM/yyyy HH:mm').format(user.createdAt),
                    Icons.calendar_today,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
              if (user.id != _userService.currentUser?.id) ...[
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    _showUserDialog(user: user);
                  },
                  child: const Text('Modifier'),
                ),
              ],
            ],
          ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _exportUsers() async {
    try {
      setState(() {
        _isSaving = true;
      });

      // Simuler l'export (remplacer par votre logique d'export)
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      _showSuccessSnackBar('Export des utilisateurs réussi!');
    } catch (e) {
      if (!mounted) return;
      _showErrorSnackBar('Erreur lors de l\'export: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  void _showUserStats() {
    final totalUsers = _users.length;
    final activeUsers = _users.where((u) => u.isActive).length;
    final inactiveUsers = totalUsers - activeUsers;
    final adminCount =
        _users.where((u) => u.role.toLowerCase() == 'admin').length;
    final managerCount =
        _users.where((u) => u.role.toLowerCase() == 'manager').length;
    final employeeCount =
        _users.where((u) => u.role.toLowerCase() == 'employee').length;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue),
                SizedBox(width: 8),
                Text('Statistiques des utilisateurs'),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildStatCard(
                    'Total des utilisateurs',
                    totalUsers.toString(),
                    Colors.blue,
                  ),
                  const SizedBox(height: 8),
                  _buildStatCard(
                    'Utilisateurs actifs',
                    activeUsers.toString(),
                    Colors.green,
                  ),
                  const SizedBox(height: 8),
                  _buildStatCard(
                    'Utilisateurs inactifs',
                    inactiveUsers.toString(),
                    Colors.red,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Répartition par rôle:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  _buildStatCard(
                    'Administrateurs',
                    adminCount.toString(),
                    Colors.purple,
                  ),
                  const SizedBox(height: 8),
                  _buildStatCard(
                    'Gestionnaires',
                    managerCount.toString(),
                    Colors.orange,
                  ),
                  const SizedBox(height: 8),
                  _buildStatCard(
                    'Employés',
                    employeeCount.toString(),
                    Colors.teal,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  Widget _buildStatCard(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddUserDialog() {
    _showUserDialog();
  }

  void _showUserDialog({User? user}) {
    final isEditing = user != null;
    final formKey = GlobalKey<FormState>();
    final nameController = TextEditingController(text: user?.name ?? '');
    final emailController = TextEditingController(text: user?.email ?? '');
    final phoneController = TextEditingController(text: user?.phone ?? '');
    String selectedRole = user?.role ?? 'employee';
    final passwordController = TextEditingController();
    bool isPasswordVisible = false;
    bool isDialogSaving = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => StatefulBuilder(
            builder:
                (context, setDialogState) => AlertDialog(
                  title: Row(
                    children: [
                      Icon(
                        isEditing ? Icons.edit : Icons.person_add,
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isEditing
                            ? 'Modifier l\'utilisateur'
                            : 'Ajouter un utilisateur',
                      ),
                    ],
                  ),
                  content: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.8,
                    child: Form(
                      key: formKey,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            TextFormField(
                              controller: nameController,
                              decoration: const InputDecoration(
                                labelText: 'Nom complet *',
                                prefixIcon: Icon(Icons.person),
                                border: OutlineInputBorder(),
                              ),
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'Le nom est requis';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: emailController,
                              decoration: const InputDecoration(
                                labelText: 'Email *',
                                prefixIcon: Icon(Icons.email),
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value == null || value.trim().isEmpty) {
                                  return 'L\'email est requis';
                                }
                                if (!RegExp(
                                  r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                                ).hasMatch(value)) {
                                  return 'Format d\'email invalide';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              controller: phoneController,
                              decoration: const InputDecoration(
                                labelText: 'Téléphone',
                                prefixIcon: Icon(Icons.phone),
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.phone,
                            ),
                            const SizedBox(height: 16),
                            DropdownButtonFormField<String>(
                              value: selectedRole,
                              decoration: const InputDecoration(
                                labelText: 'Rôle *',
                                prefixIcon: Icon(Icons.security),
                                border: OutlineInputBorder(),
                              ),
                              items: [
                                DropdownMenuItem(
                                  value: 'admin',
                                  child: Text('Administrateur'),
                                ),
                                DropdownMenuItem(
                                  value: 'manager',
                                  child: Text('Gestionnaire'),
                                ),
                                DropdownMenuItem(
                                  value: 'employee',
                                  child: Text('Employé'),
                                ),
                                DropdownMenuItem(
                                  value: 'viewer',
                                  child: Text('Observateur'),
                                ),
                              ],
                              onChanged: (value) {
                                setDialogState(() {
                                  selectedRole = value!;
                                });
                              },
                            ),
                            if (!isEditing) ...[
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: passwordController,
                                decoration: InputDecoration(
                                  labelText: 'Mot de passe *',
                                  prefixIcon: const Icon(Icons.lock),
                                  suffixIcon: IconButton(
                                    icon: Icon(
                                      isPasswordVisible
                                          ? Icons.visibility
                                          : Icons.visibility_off,
                                    ),
                                    onPressed: () {
                                      setDialogState(() {
                                        isPasswordVisible = !isPasswordVisible;
                                      });
                                    },
                                  ),
                                  border: const OutlineInputBorder(),
                                ),
                                obscureText: !isPasswordVisible,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Le mot de passe est requis';
                                  }
                                  if (value.length < 6) {
                                    return 'Le mot de passe doit contenir au moins 6 caractères';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Annuler'),
                    ),
                    ElevatedButton(
                      onPressed:
                          isDialogSaving
                              ? null
                              : () async {
                                if (!formKey.currentState!.validate()) return;

                                setDialogState(() {
                                  isDialogSaving = true;
                                });

                                try {
                                  if (isEditing) {
                                    // Mise à jour
                                    final updatedUser = user.copyWith(
                                      name: nameController.text.trim(),
                                      email: emailController.text.trim(),
                                      phone:
                                          phoneController.text.trim().isEmpty
                                              ? null
                                              : phoneController.text.trim(),
                                      role: selectedRole,
                                    );

                                    await _userService.updateUser(updatedUser);
                                    _showSuccessSnackBar(
                                      'Utilisateur modifié avec succès!',
                                    );
                                  } else {
                                    // Création
                                    final newUser = User(
                                      id: '',
                                      name: nameController.text.trim(),
                                      email: emailController.text.trim(),
                                      phone:
                                          phoneController.text.trim().isEmpty
                                              ? null
                                              : phoneController.text.trim(),
                                      role: selectedRole,
                                      isActive: true,
                                      createdAt: DateTime.now(),
                                      lastLoginAt: DateTime.now(),
                                    );

                                    await _userService.createUser(
                                      newUser,
                                      passwordController.text,
                                    );
                                    _showSuccessSnackBar(
                                      'Utilisateur créé avec succès!',
                                    );
                                  }

                                  if (!mounted) return;
                                  // ignore: use_build_context_synchronously
                                  Navigator.of(context).pop();
                                  _loadUsers();
                                } catch (e) {
                                  if (!mounted) return;
                                  _showErrorSnackBar('Erreur: ${e.toString()}');
                                } finally {
                                  if (mounted) {
                                    setDialogState(() {
                                      isDialogSaving = false;
                                    });
                                  }
                                }
                              },
                      child:
                          isDialogSaving
                              ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                              : Text(isEditing ? 'Modifier' : 'Créer'),
                    ),
                  ],
                ),
          ),
    );
  }

  void _toggleUserStatus(User user) async {
    try {
      final updatedUser = user.copyWith(isActive: !user.isActive);
      await _userService.updateUser(updatedUser);

      if (!mounted) return;
      _showSuccessSnackBar(
        'Utilisateur ${user.isActive ? 'désactivé' : 'activé'} avec succès!',
      );

      _loadUsers();
    } catch (e) {
      if (!mounted) return;
      _showErrorSnackBar('Erreur: ${e.toString()}');
    }
  }

  void _showDeleteConfirmation(User user) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.warning, color: Colors.red),
                SizedBox(width: 8),
                Text('Confirmer la suppression'),
              ],
            ),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer l\'utilisateur "${user.name}" ?\n\nCette action est irréversible.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();

                  try {
                    await _userService.deleteUser(user.id);
                    if (!mounted) return;
                    _showSuccessSnackBar('Utilisateur supprimé avec succès!');
                    _loadUsers();
                  } catch (e) {
                    if (!mounted) return;
                    _showErrorSnackBar('Erreur: ${e.toString()}');
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
