import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:firebase_database/firebase_database.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal();

  // Getter pour l'instance singleton
  static UserService get instance => _instance;

  final auth.FirebaseAuth _auth = auth.FirebaseAuth.instance;
  DatabaseReference? _database;

  User? _currentUser;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;

  // Initialiser la base de données de manière paresseuse
  DatabaseReference get database {
    _database ??= FirebaseDatabase.instance.ref();
    return _database!;
  }

  // Initialiser le service utilisateur
  Future<void> initialize() async {
    try {
      final authUser = _auth.currentUser;
      if (authUser != null) {
        await _loadUserData(authUser.uid);
      }
    } catch (e) {
      // Ignorer les erreurs d'initialisation Firebase pour ne pas bloquer l'app
      print('Erreur lors de l\'initialisation UserService: $e');
    }
  }

  // Connexion avec email et mot de passe
  Future<User?> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadUserData(credential.user!.uid);
        await _updateLastLogin();
        return _currentUser;
      }
    } catch (e) {
      throw Exception('Erreur de connexion: $e');
    }
    return null;
  }

  // Inscription avec email et mot de passe (alias pour createUserWithEmailAndPassword)
  Future<User?> signUp(
    String email,
    String password,
    String name,
    String role,
  ) async {
    return await createUserWithEmailAndPassword(
      email: email,
      password: password,
      name: name,
      role: role,
    );
  }

  // Inscription avec email et mot de passe
  Future<User?> createUserWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    String role = 'employee',
    String? phone,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        final user = User(
          id: credential.user!.uid,
          email: email,
          name: name,
          role: role,
          phone: phone,
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );

        await _saveUserData(user);
        _currentUser = user;
        return user;
      }
    } catch (e) {
      throw Exception('Erreur lors de la création du compte: $e');
    }
    return null;
  }

  // Connexion anonyme (pour la compatibilité)
  Future<User?> signInAnonymously() async {
    try {
      final credential = await _auth.signInAnonymously();

      if (credential.user != null) {
        // Créer un utilisateur anonyme temporaire
        final user = User(
          id: credential.user!.uid,
          email: '<EMAIL>',
          name: 'Utilisateur Anonyme',
          role: 'employee',
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );

        _currentUser = user;
        return user;
      }
    } catch (e) {
      throw Exception('Erreur de connexion anonyme: $e');
    }
    return null;
  }

  // Déconnexion
  Future<void> signOut() async {
    await _auth.signOut();
    _currentUser = null;

    // Nettoyer les préférences locales
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('current_user_id');
  }

  // Charger les données utilisateur depuis Firebase
  Future<void> _loadUserData(String userId) async {
    try {
      final snapshot = await database.child('users').child(userId).get();

      if (snapshot.exists) {
        final userData = Map<String, dynamic>.from(snapshot.value as Map);
        _currentUser = User.fromJson(userData);
      } else {
        // Si l'utilisateur n'existe pas dans la base, créer un profil basique
        final authUser = _auth.currentUser;
        if (authUser != null) {
          final user = User(
            id: authUser.uid,
            email: authUser.email ?? '<EMAIL>',
            name: authUser.displayName ?? 'Utilisateur',
            role: 'employee',
            createdAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
          );

          await _saveUserData(user);
          _currentUser = user;
        }
      }

      // Sauvegarder l'ID utilisateur localement
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('current_user_id', userId);
    } catch (e) {
      // Erreur lors du chargement des données utilisateur
    }
  }

  // Sauvegarder les données utilisateur dans Firebase
  Future<void> _saveUserData(User user) async {
    try {
      await database.child('users').child(user.id).set(user.toJson());
    } catch (e) {
      // Erreur lors de la sauvegarde des données utilisateur
    }
  }

  // Mettre à jour la dernière connexion
  Future<void> _updateLastLogin() async {
    if (_currentUser != null) {
      final updatedUser = _currentUser!.copyWith(lastLoginAt: DateTime.now());

      await _saveUserData(updatedUser);
      _currentUser = updatedUser;
    }
  }

  // Mettre à jour le profil utilisateur
  Future<void> updateUserProfile({
    String? name,
    String? phone,
    String? avatar,
  }) async {
    if (_currentUser != null) {
      final updatedUser = _currentUser!.copyWith(
        name: name,
        phone: phone,
        avatar: avatar,
      );

      await _saveUserData(updatedUser);
      _currentUser = updatedUser;
    }
  }

  // Obtenir tous les utilisateurs (admin seulement)
  Future<List<User>> getAllUsers() async {
    if (_currentUser?.role != 'admin') {
      throw Exception('Accès non autorisé');
    }

    try {
      // Utiliser une requête limitée pour améliorer les performances
      final snapshot =
          await database
              .child('users')
              .orderByKey()
              .limitToFirst(100) // Limiter à 100 utilisateurs à la fois
              .get();

      if (snapshot.exists) {
        final usersData = Map<String, dynamic>.from(snapshot.value as Map);
        final users =
            usersData.values
                .map(
                  (userData) =>
                      User.fromJson(Map<String, dynamic>.from(userData)),
                )
                .toList();

        // Trier les utilisateurs par date de création (plus récent en premier)
        users.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        return users;
      }
    } catch (e) {
      // Erreur lors de la récupération des utilisateurs
      throw Exception('Erreur lors de la récupération des utilisateurs: $e');
    }

    return [];
  }

  // Cache des utilisateurs
  List<User>? _cachedUsers;
  DateTime? _lastCacheTime;

  // Alias pour getAllUsers avec mise en cache
  Future<List<User>> getUsers() async {
    // Vérifier si nous avons un cache récent (moins de 2 minutes)
    final now = DateTime.now();
    if (_cachedUsers != null &&
        _lastCacheTime != null &&
        now.difference(_lastCacheTime!).inMinutes < 2) {
      return _cachedUsers!;
    }

    // Sinon, charger les données fraîches
    final users = await getAllUsers();

    // Mettre à jour le cache
    _cachedUsers = users;
    _lastCacheTime = now;

    return users;
  }

  // Charger plus d'utilisateurs (pour le chargement progressif)
  Future<List<User>> getMoreUsers(String? lastUserId) async {
    if (_currentUser?.role != 'admin') {
      throw Exception('Accès non autorisé');
    }

    if (lastUserId == null) {
      return getUsers(); // Premier chargement
    }

    try {
      // Charger les utilisateurs après le dernier ID
      final snapshot =
          await database
              .child('users')
              .orderByKey()
              .startAfter(lastUserId)
              .limitToFirst(20) // Charger 20 utilisateurs à la fois
              .get();

      if (snapshot.exists) {
        final usersData = Map<String, dynamic>.from(snapshot.value as Map);
        final moreUsers =
            usersData.values
                .map(
                  (userData) =>
                      User.fromJson(Map<String, dynamic>.from(userData)),
                )
                .toList();

        // Trier les utilisateurs par date de création (plus récent en premier)
        moreUsers.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        return moreUsers;
      }
    } catch (e) {
      throw Exception('Erreur lors du chargement de plus d\'utilisateurs: $e');
    }

    return [];
  }

  // Vider le cache des utilisateurs
  void clearUserCache() {
    _cachedUsers = null;
    _lastCacheTime = null;
  }

  // Mettre à jour un utilisateur
  Future<void> updateUser(User user) async {
    if (_currentUser?.role != 'admin') {
      throw Exception('Accès non autorisé');
    }

    try {
      await _saveUserData(user);
      // Vider le cache après modification
      clearUserCache();
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour de l\'utilisateur: $e');
    }
  }

  // Créer un utilisateur
  Future<void> createUser(User user, String password) async {
    if (_currentUser?.role != 'admin') {
      throw Exception('Accès non autorisé');
    }

    try {
      // Créer l'utilisateur dans Firebase Auth
      final credential = await _auth.createUserWithEmailAndPassword(
        email: user.email,
        password: password,
      );

      // Créer l'utilisateur avec l'ID généré par Firebase
      final newUser = user.copyWith(
        id: credential.user!.uid,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );

      await _saveUserData(newUser);
      // Vider le cache après modification
      clearUserCache();
    } catch (e) {
      throw Exception('Erreur lors de la création de l\'utilisateur: $e');
    }
  }

  // Supprimer un utilisateur
  Future<void> deleteUser(String userId) async {
    if (_currentUser?.role != 'admin') {
      throw Exception('Accès non autorisé');
    }

    try {
      // Supprimer de la base de données
      await database.child('users').child(userId).remove();

      // Supprimer de Firebase Auth (nécessite des permissions spéciales)
      // await _auth.deleteUser(credential);

      // Vider le cache après modification
      clearUserCache();
    } catch (e) {
      throw Exception('Erreur lors de la suppression de l\'utilisateur: $e');
    }
  }

  // Mettre à jour les permissions d'un utilisateur (admin seulement)
  Future<void> updateUserPermissions(
    String userId,
    Map<String, dynamic> permissions,
  ) async {
    if (_currentUser?.role != 'admin') {
      throw Exception('Accès non autorisé');
    }

    try {
      await database
          .child('users')
          .child(userId)
          .child('permissions')
          .set(permissions);

      // Vider le cache après modification
      clearUserCache();
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour des permissions: $e');
    }
  }

  // Désactiver/activer un utilisateur (admin seulement)
  Future<void> toggleUserStatus(String userId, [bool? isActive]) async {
    if (_currentUser?.role != 'admin') {
      throw Exception('Accès non autorisé');
    }

    try {
      if (isActive == null) {
        // Si isActive n'est pas fourni, récupérer l'état actuel et l'inverser
        final snapshot =
            await database.child('users').child(userId).child('isActive').get();
        final currentStatus = snapshot.exists ? snapshot.value as bool : true;
        await database
            .child('users')
            .child(userId)
            .child('isActive')
            .set(!currentStatus);
      } else {
        await database
            .child('users')
            .child(userId)
            .child('isActive')
            .set(isActive);
      }

      // Vider le cache après modification
      clearUserCache();
    } catch (e) {
      throw Exception(
        'Erreur lors de la mise à jour du statut utilisateur: $e',
      );
    }
  }

  // Changer le rôle d'un utilisateur (admin seulement)
  Future<void> updateUserRole(String userId, String role) async {
    if (_currentUser?.role != 'admin') {
      throw Exception('Accès non autorisé');
    }

    try {
      await database.child('users').child(userId).child('role').set(role);

      // Vider le cache après modification
      clearUserCache();
    } catch (e) {
      throw Exception('Erreur lors de la mise à jour du rôle: $e');
    }
  }

  // Vérifier les permissions
  bool hasPermission(String permission) {
    return _currentUser?.hasPermission(permission) ?? false;
  }

  bool canManageUsers() => _currentUser?.canManageUsers() ?? false;
  bool canManageProducts() => _currentUser?.canManageProducts() ?? false;
  bool canManageInvoices() => _currentUser?.canManageInvoices() ?? false;
  bool canManageColis() => _currentUser?.canManageColis() ?? false;
  bool canViewReports() => _currentUser?.canViewReports() ?? false;
}
