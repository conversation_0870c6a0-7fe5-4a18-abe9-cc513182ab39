import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:general_hcp_crm/models/product.dart';
import 'package:general_hcp_crm/models/category.dart' as model;
import 'package:uuid/uuid.dart';
import 'firebase_service.dart';
import 'offline_config_service.dart';

class InventoryService {
  static InventoryService? _instance;
  static InventoryService get instance {
    _instance ??= InventoryService._internal();
    return _instance!;
  }

  InventoryService._internal();

  static const String _productsKey = 'inventory_products';
  static const String _categoriesKey = 'inventory_categories';
  final List<Product> _products = [];
  final List<model.Category> _categories = [];
  final Uuid _uuid = const Uuid();
  bool _isInitialized = false;

  Future<void> _initializeIfNeeded() async {
    if (!_isInitialized) {
      await _loadFromStorage();
      _isInitialized = true;
    }
  }

  Future<void> _loadFromStorage() async {
    final prefs = await SharedPreferences.getInstance();

    // Charger les produits
    final productsJson = prefs.getString(_productsKey);
    if (productsJson != null) {
      final List<dynamic> productsList = jsonDecode(productsJson);
      _products.clear();
      _products.addAll(
        productsList.map((json) => Product.fromJson(json)).toList(),
      );
    }

    // Charger les catégories
    final categoriesJson = prefs.getString(_categoriesKey);
    if (categoriesJson != null) {
      final List<dynamic> categoriesList = jsonDecode(categoriesJson);
      _categories.clear();
      _categories.addAll(
        categoriesList.map((json) => model.Category.fromJson(json)).toList(),
      );
    }
  }

  Future<void> _saveToStorage() async {
    final prefs = await SharedPreferences.getInstance();

    // Sauvegarder les produits
    final productsJson = jsonEncode(_products.map((p) => p.toJson()).toList());
    await prefs.setString(_productsKey, productsJson);

    // Sauvegarder les catégories
    final categoriesJson = jsonEncode(
      _categories.map((c) => c.toJson()).toList(),
    );
    await prefs.setString(_categoriesKey, categoriesJson);
  }

  // Product methods - Version simplifiée et rapide
  Future<List<Product>> getProducts() async {
    try {
      debugPrint('🔄 Chargement des produits...');

      // Charger directement depuis le cache local
      await _initializeIfNeeded();

      debugPrint('✅ ${_products.length} produits chargés depuis le cache');
      return List.unmodifiable(_products);
    } catch (e) {
      debugPrint('❌ Erreur chargement produits: $e');
      return [];
    }
  }

  Future<Product> addProduct(Product product) async {
    await _initializeIfNeeded();
    final newProduct = product.copyWith(id: _uuid.v4());

    // Vérifier si Firebase est disponible avant d'essayer de l'utiliser
    final canUseFirebase = await OfflineConfigService.instance.canUseFirebase();
    if (canUseFirebase) {
      try {
        // Essayer de sauvegarder dans Firebase d'abord
        await FirebaseService.instance.addProduct(newProduct);
        debugPrint('✅ Produit sauvegardé dans Firebase: ${newProduct.name}');
      } catch (e) {
        debugPrint('⚠️ Firebase save failed, saving locally: $e');
      }
    } else {
      debugPrint('🔒 Mode offline - sauvegarde locale uniquement');
    }

    // Toujours sauvegarder localement
    _products.add(newProduct);
    await _saveToStorage();
    debugPrint('✅ Produit sauvegardé localement: ${newProduct.name}');
    return newProduct;
  }

  Future<Product> updateProduct(Product product) async {
    await _initializeIfNeeded();
    final index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      // Vérifier si Firebase est disponible avant d'essayer de l'utiliser
      final canUseFirebase = await OfflineConfigService.instance.canUseFirebase();
      if (canUseFirebase) {
        try {
          // Essayer de mettre à jour dans Firebase d'abord
          await FirebaseService.instance.updateProduct(product);
          debugPrint('✅ Produit mis à jour dans Firebase: ${product.name}');
        } catch (e) {
          debugPrint('⚠️ Firebase update failed, updating locally: $e');
        }
      } else {
        debugPrint('🔒 Mode offline - mise à jour locale uniquement');
      }

      // Toujours mettre à jour localement
      _products[index] = product;
      await _saveToStorage();
      debugPrint('✅ Produit mis à jour localement: ${product.name}');
      return product;
    } else {
      throw Exception('Product not found');
    }
  }

  Future<void> deleteProduct(String productId) async {
    await _initializeIfNeeded();

    // Vérifier si Firebase est disponible avant d'essayer de l'utiliser
    final canUseFirebase = await OfflineConfigService.instance.canUseFirebase();
    if (canUseFirebase) {
      try {
        // Essayer de supprimer de Firebase d'abord
        await FirebaseService.instance.deleteProduct(productId);
        debugPrint('✅ Produit supprimé de Firebase: $productId');
      } catch (e) {
        debugPrint('⚠️ Firebase delete failed, deleting locally: $e');
      }
    } else {
      debugPrint('🔒 Mode offline - suppression locale uniquement');
    }

    // Toujours supprimer localement
    _products.removeWhere((p) => p.id == productId);
    await _saveToStorage();
    debugPrint('✅ Produit supprimé localement: $productId');
  }

  // Category methods - Version ultra-rapide et fiable
  Future<List<model.Category>> getCategories() async {
    try {
      debugPrint('🔄 Chargement rapide des catégories...');

      // Si déjà en mémoire, retourner immédiatement
      if (_categories.isNotEmpty) {
        debugPrint('⚡ ${_categories.length} catégories depuis la mémoire');
        return List.unmodifiable(_categories);
      }

      // Essayer de charger depuis SharedPreferences (sans _initializeIfNeeded)
      final prefs = await SharedPreferences.getInstance();
      final categoriesJson = prefs.getString(_categoriesKey);

      if (categoriesJson != null && categoriesJson.isNotEmpty) {
        try {
          final List<dynamic> categoriesList = jsonDecode(categoriesJson);
          _categories.clear();
          _categories.addAll(
            categoriesList
                .map((json) => model.Category.fromJson(json))
                .toList(),
          );
          debugPrint(
            '✅ ${_categories.length} catégories chargées depuis le stockage',
          );
          return List.unmodifiable(_categories);
        } catch (e) {
          debugPrint('⚠️ Erreur parsing catégories: $e');
        }
      }

      // Créer des catégories par défaut immédiatement
      debugPrint('📝 Création immédiate des catégories par défaut...');
      _categories.clear();
      _categories.addAll([
        model.Category(id: _uuid.v4(), name: 'Électronique'),
        model.Category(id: _uuid.v4(), name: 'Vêtements'),
        model.Category(id: _uuid.v4(), name: 'Alimentation'),
        model.Category(id: _uuid.v4(), name: 'Maison & Jardin'),
        model.Category(id: _uuid.v4(), name: 'Santé & Beauté'),
      ]);

      // Sauvegarder en arrière-plan (non bloquant)
      _saveToStorageBackground();

      debugPrint(
        '✅ ${_categories.length} catégories par défaut créées instantanément',
      );
      return List.unmodifiable(_categories);
    } catch (e) {
      debugPrint('❌ Erreur critique catégories: $e');

      // Fallback ultime : catégories minimales en mémoire
      if (_categories.isEmpty) {
        _categories.addAll([
          model.Category(id: _uuid.v4(), name: 'Général'),
          model.Category(id: _uuid.v4(), name: 'Divers'),
        ]);
      }
      return List.unmodifiable(_categories);
    }
  }

  // Sauvegarde en arrière-plan pour ne pas bloquer l'UI
  void _saveToStorageBackground() {
    Future.microtask(() async {
      try {
        await _saveToStorage();
        debugPrint('💾 Sauvegarde catégories terminée en arrière-plan');
      } catch (e) {
        debugPrint('⚠️ Erreur sauvegarde arrière-plan: $e');
      }
    });
  }

  Future<model.Category> addCategory(model.Category category) async {
    await _initializeIfNeeded();
    
    // Vérifier d'abord si une catégorie avec le même nom existe déjà
    try {
      final existingCategory = _categories.firstWhere(
        (cat) => cat.name.toLowerCase() == category.name.toLowerCase(),
      );
      debugPrint('⚠️ Catégorie "${category.name}" existe déjà (ID: ${existingCategory.id})');
      return existingCategory; // Retourner la catégorie existante
    } catch (e) {
      // Catégorie n'existe pas, continuer avec la création
    }
    
    final newCategory = category.copyWith(id: _uuid.v4());

    // Vérifier si Firebase est disponible avant d'essayer de l'utiliser
    final canUseFirebase = await OfflineConfigService.instance.canUseFirebase();
    if (canUseFirebase) {
      try {
        // Essayer de sauvegarder dans Firebase d'abord
        await FirebaseService.instance.addCategory(newCategory);
        debugPrint('✅ Catégorie sauvegardée dans Firebase: ${newCategory.name}');
      } catch (e) {
        debugPrint('⚠️ Firebase save failed, saving locally: $e');
      }
    } else {
      debugPrint('🔒 Mode offline - sauvegarde locale uniquement');
    }

    // Toujours sauvegarder localement
    _categories.add(newCategory);
    await _saveToStorage();
    debugPrint('✅ Catégorie sauvegardée localement: ${newCategory.name}');
    return newCategory;
  }

  Future<model.Category> updateCategory(model.Category category) async {
    await _initializeIfNeeded();
    final index = _categories.indexWhere((c) => c.id == category.id);
    if (index != -1) {
      // Vérifier si Firebase est disponible avant d'essayer de l'utiliser
      final canUseFirebase = await OfflineConfigService.instance.canUseFirebase();
      if (canUseFirebase) {
        try {
          // Essayer de mettre à jour dans Firebase d'abord
          await FirebaseService.instance.updateCategory(category);
          debugPrint('✅ Catégorie mise à jour dans Firebase: ${category.name}');
        } catch (e) {
          debugPrint('⚠️ Firebase update failed, updating locally: $e');
        }
      } else {
        debugPrint('🔒 Mode offline - mise à jour locale uniquement');
      }

      // Toujours mettre à jour localement
      _categories[index] = category;
      await _saveToStorage();
      debugPrint('✅ Catégorie mise à jour localement: ${category.name}');
      return category;
    } else {
      throw Exception('Category not found');
    }
  }

  Future<void> deleteCategory(String categoryId) async {
    await _initializeIfNeeded();
    // Vérifier si la catégorie est utilisée par des produits avant de la supprimer
    final isCategoryUsed = _products.any(
      (product) => product.categoryId == categoryId,
    );
    if (isCategoryUsed) {
      throw Exception('Category is in use and cannot be deleted.');
    }

    // Vérifier si Firebase est disponible avant d'essayer de l'utiliser
    final canUseFirebase = await OfflineConfigService.instance.canUseFirebase();
    if (canUseFirebase) {
      try {
        // Essayer de supprimer de Firebase d'abord
        await FirebaseService.instance.deleteCategory(categoryId);
        debugPrint('✅ Catégorie supprimée de Firebase: $categoryId');
      } catch (e) {
        debugPrint('⚠️ Firebase delete failed, deleting locally: $e');
      }
    } else {
      debugPrint('🔒 Mode offline - suppression locale uniquement');
    }

    // Toujours supprimer localement
    _categories.removeWhere((c) => c.id == categoryId);
    await _saveToStorage();
    debugPrint('✅ Catégorie supprimée localement: $categoryId');
  }

  // Helper pour obtenir une catégorie par ID
  Future<model.Category?> getCategoryById(String categoryId) async {
    await _initializeIfNeeded();
    try {
      return _categories.firstWhere((c) => c.id == categoryId);
    } catch (e) {
      return null;
    }
  }

  // Méthode pour nettoyer les catégories dupliquées
  Future<int> removeDuplicateCategories() async {
    await _initializeIfNeeded();
    
    Map<String, model.Category> uniqueCategories = {};
    List<model.Category> categoriesToRemove = [];
    
    // Identifier les doublons (garder la première occurrence)
    for (var category in _categories) {
      String normalizedName = category.name.toLowerCase();
      if (uniqueCategories.containsKey(normalizedName)) {
        // C'est un doublon, l'ajouter à la liste de suppression
        categoriesToRemove.add(category);
        debugPrint('🗑️ Doublon détecté: "${category.name}" (ID: ${category.id})');
      } else {
        // Première occurrence, la garder
        uniqueCategories[normalizedName] = category;
      }
    }
    
    // Supprimer les doublons
    for (var categoryToRemove in categoriesToRemove) {
      _categories.removeWhere((c) => c.id == categoryToRemove.id);
    }
    
    if (categoriesToRemove.isNotEmpty) {
      await _saveToStorage();
      debugPrint('✅ ${categoriesToRemove.length} catégories dupliquées supprimées');
    }
    
    return categoriesToRemove.length;
  }
}
