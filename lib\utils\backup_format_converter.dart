import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';

/// Utilitaire pour convertir les sauvegardes segmentées vers le format standard
class BackupFormatConverter {
  /// Convertit une sauvegarde segmentée vers le format standard
  static Map<String, dynamic> convertSegmentedToStandard(
    Map<String, dynamic> segmentedBackup,
  ) {
    try {
      // Extraire les métadonnées de base
      final version = segmentedBackup['version'] ?? '2.2.0';
      final timestamp = segmentedBackup['timestamp'] ?? DateTime.now().toIso8601String();
      final data = segmentedBackup['data'] as Map<String, dynamic>? ?? {};
      
      // Créer la structure standard
      final standardBackup = {
        'version': version,
        'timestamp': timestamp,
        'data': {
          'products': <Map<String, dynamic>>[],
          'categories': <Map<String, dynamic>>[],
          'invoices': <Map<String, dynamic>>[],
          'tasks': <Map<String, dynamic>>[],
          'colis': <Map<String, dynamic>>[],
        },
        'images': {
          'invoices': <String, dynamic>{},
          'colis': <String, dynamic>{},
        },
      };
      
      // Convertir chaque type de données
      if (data.containsKey('invoices')) {
        final invoices = data['invoices'] as List<dynamic>? ?? [];
        standardBackup['data']['invoices'] = invoices
            .map((invoice) => _convertInvoice(invoice as Map<String, dynamic>))
            .toList();
      }
      
      if (data.containsKey('products')) {
        final products = data['products'] as List<dynamic>? ?? [];
        standardBackup['data']['products'] = products
            .map((product) => _convertProduct(product as Map<String, dynamic>))
            .toList();
      }
      
      if (data.containsKey('categories')) {
        final categories = data['categories'] as List<dynamic>? ?? [];
        standardBackup['data']['categories'] = categories
            .map((category) => _convertCategory(category as Map<String, dynamic>))
            .toList();
      }
      
      if (data.containsKey('tasks')) {
        final tasks = data['tasks'] as List<dynamic>? ?? [];
        standardBackup['data']['tasks'] = tasks
            .map((task) => _convertTask(task as Map<String, dynamic>))
            .toList();
      }
      
      if (data.containsKey('colis')) {
        final colis = data['colis'] as List<dynamic>? ?? [];
        standardBackup['data']['colis'] = colis
            .map((col) => _convertColis(col as Map<String, dynamic>))
            .toList();
      }
      
      // Gérer les images si présentes
      if (segmentedBackup.containsKey('images')) {
        final images = segmentedBackup['images'] as Map<String, dynamic>;
        standardBackup['images'] = {
          'invoices': images['invoices'] ?? {},
          'colis': images['colis'] ?? {},
        };
      }
      
      debugPrint('✅ Conversion réussie: ${standardBackup['data']['invoices'].length} factures converties');
      return standardBackup;
    } catch (e) {
      debugPrint('❌ Erreur lors de la conversion: $e');
      rethrow;
    }
  }
  
  /// Convertit une facture du format segmenté vers le format standard
  static Map<String, dynamic> _convertInvoice(Map<String, dynamic> invoice) {
    // La structure des factures est déjà compatible, on retourne telle quelle
    return Map<String, dynamic>.from(invoice);
  }
  
  /// Convertit un produit du format segmenté vers le format standard
  static Map<String, dynamic> _convertProduct(Map<String, dynamic> product) {
    // La structure des produits est déjà compatible, on retourne telle quelle
    return Map<String, dynamic>.from(product);
  }
  
  /// Convertit une catégorie du format segmenté vers le format standard
  static Map<String, dynamic> _convertCategory(Map<String, dynamic> category) {
    // La structure des catégories est déjà compatible, on retourne telle quelle
    return Map<String, dynamic>.from(category);
  }
  
  /// Convertit une tâche du format segmenté vers le format standard
  static Map<String, dynamic> _convertTask(Map<String, dynamic> task) {
    // La structure des tâches est déjà compatible, on retourne telle quelle
    return Map<String, dynamic>.from(task);
  }
  
  /// Convertit un colis du format segmenté vers le format standard
  static Map<String, dynamic> _convertColis(Map<String, dynamic> colis) {
    // La structure des colis est déjà compatible, on retourne telle quelle
    return Map<String, dynamic>.from(colis);
  }
  
  /// Convertit un fichier de sauvegarde segmentée vers le format standard
  static Future<String> convertFileToStandard(
    String segmentedFilePath,
    String outputFilePath,
  ) async {
    try {
      // Lire le fichier segmenté
      final file = File(segmentedFilePath);
      if (!await file.exists()) {
        throw Exception('Le fichier de sauvegarde segmentée n\'existe pas: $segmentedFilePath');
      }
      
      final jsonString = await file.readAsString();
      final segmentedBackup = jsonDecode(jsonString) as Map<String, dynamic>;
      
      // Convertir vers le format standard
      final standardBackup = convertSegmentedToStandard(segmentedBackup);
      
      // Écrire le fichier converti
      final outputFile = File(outputFilePath);
      await outputFile.writeAsString(
        const JsonEncoder.withIndent('  ').convert(standardBackup),
      );
      
      debugPrint('✅ Fichier converti avec succès: $outputFilePath');
      return outputFilePath;
    } catch (e) {
      debugPrint('❌ Erreur lors de la conversion du fichier: $e');
      rethrow;
    }
  }
  
  /// Valide qu'une sauvegarde est au format segmenté
  static bool isSegmentedBackup(Map<String, dynamic> backup) {
    return backup.containsKey('segmented') && 
           backup['segmented'] == true &&
           backup.containsKey('config');
  }
  
  /// Valide qu'une sauvegarde est au format standard
  static bool isStandardBackup(Map<String, dynamic> backup) {
    return backup.containsKey('data') &&
           backup.containsKey('version') &&
           backup.containsKey('timestamp') &&
           !backup.containsKey('segmented');
  }
  
  /// Obtient des informations sur une sauvegarde (segmentée ou standard)
  static Map<String, dynamic> getBackupInfo(Map<String, dynamic> backup) {
    final isSegmented = isSegmentedBackup(backup);
    final isStandard = isStandardBackup(backup);
    
    Map<String, dynamic> data;
    if (isSegmented) {
      data = backup['data'] as Map<String, dynamic>? ?? {};
    } else if (isStandard) {
      data = backup['data'] as Map<String, dynamic>? ?? {};
    } else {
      data = {};
    }
    
    return {
      'version': backup['version'] ?? 'Inconnue',
      'timestamp': backup['timestamp'] ?? 'Inconnue',
      'type': isSegmented ? 'Segmentée' : (isStandard ? 'Standard' : 'Inconnue'),
      'isSegmented': isSegmented,
      'isStandard': isStandard,
      'productsCount': (data['products'] as List?)?.length ?? 0,
      'categoriesCount': (data['categories'] as List?)?.length ?? 0,
      'invoicesCount': (data['invoices'] as List?)?.length ?? 0,
      'tasksCount': (data['tasks'] as List?)?.length ?? 0,
      'colisCount': (data['colis'] as List?)?.length ?? 0,
      'config': isSegmented ? backup['config'] : null,
    };
  }
}