import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/employee_performance_service.dart';
import '../pages/employee_performance_page.dart';

class EmployeePerformanceWidget extends StatefulWidget {
  const EmployeePerformanceWidget({super.key});

  @override
  State<EmployeePerformanceWidget> createState() =>
      _EmployeePerformanceWidgetState();
}

class _EmployeePerformanceWidgetState extends State<EmployeePerformanceWidget> {
  EmployeePerformanceStats? _performanceStats;
  bool _isLoading = true;
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  final EmployeePerformanceService _performanceService =
      EmployeePerformanceService.instance;

  @override
  void initState() {
    super.initState();
    _loadPerformanceStats();
  }

  Future<void> _loadPerformanceStats() async {
    setState(() => _isLoading = true);
    try {
      final stats = await _performanceService.getCurrentMonthPerformance();
      setState(() {
        _performanceStats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des performances: $e'),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: CircularProgressIndicator()),
        ),
      );
    }

    if (_performanceStats == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(child: Text('Aucune donnée de performance disponible')),
        ),
      );
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildSalarySection(),
            const SizedBox(height: 16),
            _buildProgressSection(),
            const SizedBox(height: 16),
            _buildStatsGrid(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green[600],
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(Icons.trending_up, color: Colors.white, size: 24),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Performance Employé',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Text(
                'Mois en cours',
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        Row(
          children: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadPerformanceStats,
            ),
            IconButton(
              icon: const Icon(Icons.open_in_new),
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const EmployeePerformancePage(),
                  ),
                );
              },
              tooltip: 'Voir les détails',
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSalarySection() {
    final stats = _performanceStats!;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green[400]!, Colors.green[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Salaire Total',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
              Text(
                '${_currencyFormat.format(stats.totalSalary)} FCFA',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Fixe: ${_currencyFormat.format(stats.fixedSalary)} FCFA',
                style: const TextStyle(color: Colors.white70, fontSize: 12),
              ),
              Text(
                'Bonus: ${_currencyFormat.format(stats.bonus)} FCFA',
                style: const TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProgressSection() {
    final stats = _performanceStats!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Progression Objectif',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            Text(
              '${stats.validClients}/${stats.clientObjective}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.green[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: stats.objectiveProgress / 100,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(
            stats.objectiveProgress >= 100 ? Colors.green : Colors.orange,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '${stats.objectiveProgress.toStringAsFixed(1)}% de l\'objectif atteint',
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
      ],
    );
  }

  Widget _buildStatsGrid() {
    final stats = _performanceStats!;
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 1.5,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      children: [
        _buildStatCard(
          'Clients Valides',
          '${stats.validClients}',
          Icons.people,
          Colors.blue[600]!,
          subtitle: '≥ 3000 FCFA',
        ),
        _buildStatCard(
          'Total Clients',
          '${stats.totalClients}',
          Icons.group,
          Colors.purple[600]!,
        ),
        _buildStatCard(
          'CA Mensuel',
          _currencyFormat.format(stats.totalRevenue),
          Icons.attach_money,
          Colors.green[600]!,
          subtitle: 'FCFA',
        ),
        _buildStatCard(
          'Factures',
          '${stats.totalInvoices}',
          Icons.receipt,
          Colors.orange[600]!,
          subtitle: 'Payées',
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (subtitle != null)
            Text(
              subtitle,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 8,
              ),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }
}
