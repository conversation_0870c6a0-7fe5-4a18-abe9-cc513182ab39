import 'package:flutter/material.dart';
import 'dart:math';

/// Widget de notification motivante qui apparaît de temps en temps
class MotivationNotification extends StatefulWidget {
  final String message;
  final IconData icon;
  final Color color;
  final VoidCallback? onTap;
  final Duration duration;

  const MotivationNotification({
    super.key,
    required this.message,
    this.icon = Icons.star,
    this.color = Colors.orange,
    this.onTap,
    this.duration = const Duration(seconds: 4),
  });

  @override
  State<MotivationNotification> createState() => _MotivationNotificationState();

  /// Afficher une notification motivante aléatoire
  static void showRandom(BuildContext context) {
    final messages = [
      MotivationMessage(
        '🎯 Continue comme ça !',
        Icons.track_changes,
        Colors.blue,
      ),
      MotivationMessage(
        '💪 Tu es sur la bonne voie !',
        Icons.trending_up,
        Colors.green,
      ),
      MotivationMessage('⭐ Excellent travail !', Icons.star, Colors.amber),
      MotivationMessage(
        '🚀 Performance de champion !',
        Icons.rocket_launch,
        Colors.purple,
      ),
      MotivationMessage(
        '🔥 Tu assures !',
        Icons.local_fire_department,
        Colors.orange,
      ),
      MotivationMessage('💎 Brillant !', Icons.diamond, Colors.indigo),
      MotivationMessage(
        '🏆 Tu es le meilleur !',
        Icons.emoji_events,
        Colors.yellow,
      ),
      MotivationMessage(
        '💯 Performance parfaite !',
        Icons.verified,
        Colors.green,
      ),
      MotivationMessage('🌟 Fantastique !', Icons.auto_awesome, Colors.pink),
      MotivationMessage('⚡ Énergie au top !', Icons.bolt, Colors.cyan),
    ];

    final random = Random();
    final selectedMessage = messages[random.nextInt(messages.length)];

    show(
      context,
      message: selectedMessage.text,
      icon: selectedMessage.icon,
      color: selectedMessage.color,
    );
  }

  /// Afficher une notification motivante personnalisée
  static void show(
    BuildContext context, {
    required String message,
    IconData icon = Icons.star,
    Color color = Colors.orange,
    VoidCallback? onTap,
    Duration duration = const Duration(seconds: 4),
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            top: MediaQuery.of(context).padding.top + 10,
            left: 16,
            right: 16,
            child: MotivationNotification(
              message: message,
              icon: icon,
              color: color,
              duration: duration,
              onTap: () {
                overlayEntry.remove();
                onTap?.call();
              },
            ),
          ),
    );

    overlay.insert(overlayEntry);

    // Supprimer automatiquement après la durée spécifiée
    Future.delayed(duration, () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }
}

class _MotivationNotificationState extends State<MotivationNotification>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  void _startAnimations() {
    _slideController.forward();

    // Démarrer l'animation de pulsation après un délai
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _pulseController.repeat(reverse: true);
      }
    });

    // Commencer à disparaître avant la fin
    Future.delayed(widget.duration - const Duration(milliseconds: 500), () {
      if (mounted) {
        _slideController.reverse();
      }
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _pulseAnimation.value,
            child: Material(
              elevation: 8,
              borderRadius: BorderRadius.circular(16),
              child: InkWell(
                onTap: widget.onTap,
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        widget.color.withValues(alpha: 0.8),
                        widget.color,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: widget.color.withValues(alpha: 0.3),
                        blurRadius: 12,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(widget.icon, color: Colors.white, size: 24),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          widget.message,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.close,
                        color: Colors.white.withValues(alpha: 0.7),
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Modèle pour les messages de motivation
class MotivationMessage {
  final String text;
  final IconData icon;
  final Color color;

  const MotivationMessage(this.text, this.icon, this.color);
}

/// Service pour gérer les notifications motivantes
class MotivationService {
  static DateTime? _lastNotificationTime;
  static const Duration _minimumInterval = Duration(minutes: 30);

  /// Afficher une notification motivante si assez de temps s'est écoulé
  static void showIfAppropriate(BuildContext context) {
    final now = DateTime.now();

    if (_lastNotificationTime == null ||
        now.difference(_lastNotificationTime!) > _minimumInterval) {
      // 30% de chance d'afficher une notification
      if (Random().nextDouble() < 0.3) {
        MotivationNotification.showRandom(context);
        _lastNotificationTime = now;
      }
    }
  }

  /// Afficher une notification de félicitations pour une action spécifique
  static void showCongratulations(
    BuildContext context, {
    required String action,
  }) {
    final congratsMessages = {
      'invoice_created': MotivationMessage(
        '🎉 Nouvelle facture créée ! Continue !',
        Icons.receipt_long,
        Colors.green,
      ),
      'client_added': MotivationMessage(
        '🆕 Nouveau client ajouté ! Excellent !',
        Icons.person_add,
        Colors.blue,
      ),
      'big_sale': MotivationMessage(
        '💰 Grosse vente ! Tu es un champion !',
        Icons.diamond,
        Colors.purple,
      ),
      'streak': MotivationMessage(
        '🔥 Série en cours ! Tu es en feu !',
        Icons.local_fire_department,
        Colors.orange,
      ),
    };

    final message = congratsMessages[action];
    if (message != null) {
      MotivationNotification.show(
        context,
        message: message.text,
        icon: message.icon,
        color: message.color,
      );
    }
  }
}
