import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/invoice.dart';
import 'order_service.dart';

/// Service de synchronisation avec l'application PC CommandTaker
class PCSyncService {
  static const String _defaultServerUrl = 'http://192.168.1.16:8080';
  String _serverUrl = _defaultServerUrl;
  bool _isConnected = false;

  /// Singleton
  static final PCSyncService _instance = PCSyncService._internal();
  factory PCSyncService() => _instance;
  PCSyncService._internal();

  /// Getters
  String get serverUrl => _serverUrl;
  bool get isConnected => _isConnected;

  /// Configurer l'URL du serveur
  void setServerUrl(String url) {
    _serverUrl = url;
  }

  /// Tester la connexion avec le serveur PC
  Future<bool> testConnection() async {
    try {
      final response = await http
          .get(
            Uri.parse('$_serverUrl/api/status'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 5));

      _isConnected = response.statusCode == 200;
      return _isConnected;
    } catch (e) {
      print('Erreur de connexion au serveur PC: $e');
      _isConnected = false;
      return false;
    }
  }

  /// Synchroniser une commande avec le serveur PC
  Future<bool> syncOrder(Invoice order) async {
    if (!_isConnected) {
      await testConnection();
    }

    if (!_isConnected) {
      return false;
    }

    try {
      final orderData = {
        'id': order.id,
        'clientName': order.clientName,
        'clientNumber': order.clientNumber,
        'clientAddress': order.clientAddress,
        'items':
            order.items
                .map(
                  (item) => {
                    'name': item.name,
                    'quantity': item.quantity,
                    'price': item.price,
                    'isFromStock': item.isFromStock,
                  },
                )
                .toList(),
        'status': _mapStatusToPC(order.status),
        'total': order.total,
        'createdAt': order.createdAt.toIso8601String(),
        'deliveryLocation': order.deliveryLocation,
        'notes': order.notes,
      };

      final response = await http
          .post(
            Uri.parse('$_serverUrl/api/orders'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode(orderData),
          )
          .timeout(const Duration(seconds: 10));

      return response.statusCode == 200 || response.statusCode == 201;
    } catch (e) {
      print('Erreur lors de la synchronisation: $e');
      return false;
    }
  }

  /// Récupérer les commandes depuis le serveur PC
  Future<List<Map<String, dynamic>>> getOrdersFromPC() async {
    if (!_isConnected) {
      await testConnection();
    }

    if (!_isConnected) {
      return [];
    }

    try {
      final response = await http
          .get(
            Uri.parse('$_serverUrl/api/orders'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.cast<Map<String, dynamic>>();
      }
      return [];
    } catch (e) {
      print('Erreur lors de la récupération des commandes: $e');
      return [];
    }
  }

  /// Mettre à jour le statut d'une commande sur le serveur PC
  Future<bool> updateOrderStatus(String orderId, InvoiceStatus status) async {
    if (!_isConnected) {
      await testConnection();
    }

    if (!_isConnected) {
      return false;
    }

    try {
      final response = await http
          .put(
            Uri.parse('$_serverUrl/api/orders/$orderId/status'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({
              'status': _mapStatusToPC(status),
              'updatedAt': DateTime.now().toIso8601String(),
            }),
          )
          .timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      print('Erreur lors de la mise à jour du statut: $e');
      return false;
    }
  }

  /// Obtenir les statistiques du serveur PC
  Future<Map<String, dynamic>?> getServerStats() async {
    if (!_isConnected) {
      await testConnection();
    }

    if (!_isConnected) {
      return null;
    }

    try {
      final response = await http
          .get(
            Uri.parse('$_serverUrl/api/stats'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
      return null;
    } catch (e) {
      print('Erreur lors de la récupération des statistiques: $e');
      return null;
    }
  }

  /// Scanner le réseau pour trouver le serveur CommandTaker
  Future<List<String>> scanForServers() async {
    final List<String> foundServers = [];

    try {
      // Obtenir l'adresse IP locale
      final interfaces = await NetworkInterface.list();
      String? localIP;

      for (final interface in interfaces) {
        for (final addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4 && !addr.isLoopback) {
            localIP = addr.address;
            break;
          }
        }
        if (localIP != null) break;
      }

      if (localIP == null) return foundServers;

      // Extraire le sous-réseau (ex: 192.168.1.x)
      final parts = localIP.split('.');
      if (parts.length != 4) return foundServers;

      final subnet = '${parts[0]}.${parts[1]}.${parts[2]}';

      // Scanner les adresses IP du sous-réseau
      final futures = <Future<void>>[];
      for (int i = 1; i <= 254; i++) {
        final ip = '$subnet.$i';
        futures.add(_checkServer(ip, foundServers));
      }

      await Future.wait(futures);
    } catch (e) {
      print('Erreur lors du scan réseau: $e');
    }

    return foundServers;
  }

  /// Vérifier si une IP héberge le serveur CommandTaker
  Future<void> _checkServer(String ip, List<String> foundServers) async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$ip:8080/api/status'),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 2));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['app'] == 'CommandTaker') {
          foundServers.add('http://$ip:8080');
        }
      }
    } catch (e) {
      // Ignorer les erreurs de connexion
    }
  }

  /// Mapper le statut mobile vers le statut PC
  String _mapStatusToPC(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.enAttente:
        return 'pending';
      case InvoiceStatus.enCours:
        return 'in_progress';
      case InvoiceStatus.terminee:
        return 'completed';
      case InvoiceStatus.enRetard:
        return 'delayed';
      case InvoiceStatus.annulee:
        return 'cancelled';
    }
  }

  /// Synchroniser toutes les commandes avec le serveur PC (batch)
  Future<Map<String, dynamic>> syncAllOrders() async {
    try {
      // Récupérer toutes les commandes locales
      final localOrders = await OrderService.instance.getAllOrders();

      // Préparer les données pour la synchronisation
      final ordersData =
          localOrders.map((order) => _mapOrderToPC(order)).toList();

      // Envoyer les commandes au serveur PC
      final response = await http
          .post(
            Uri.parse('$_serverUrl/orders-batch'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'User-Agent': 'CommandTaker-Mobile/1.0',
              'X-Client-Version': '1.0.0',
              'X-Platform': 'mobile',
            },
            body: json.encode({'orders': ordersData}),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // Traiter la réponse selon le format spécifié
        if (data['success'] == true) {
          return {
            'success': true,
            'created': data['data']['created'] ?? 0,
            'updated': data['data']['updated'] ?? 0,
            'errors': data['data']['errors'] ?? [],
            'message': data['message'] ?? 'Synchronisation réussie',
            'total': ordersData.length,
          };
        } else {
          throw Exception(
            data['error']['message'] ?? 'Erreur de synchronisation',
          );
        }
      } else {
        throw Exception('Erreur HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      return {'success': false, 'error': e.toString(), 'total': 0};
    }
  }

  /// Mapper une commande mobile vers le format PC
  Map<String, dynamic> _mapOrderToPC(Invoice order) {
    return {
      'id': order.id,
      'clientName': order.clientName,
      'clientNumber': order.clientNumber,
      'products': order.products,
      'items':
          order.items
              .map(
                (item) => {
                  'id': item.id,
                  'name': item.name,
                  'price': item.price,
                  'quantity': item.quantity,
                  'isCustom': item.isCustom,
                  'categoryName': item.categoryName,
                  'productId': item.productId,
                  'isFromStock': item.isFromStock,
                },
              )
              .toList(),
      'deliveryLocation': order.deliveryLocation,
      'deliveryDetails': order.deliveryDetails,
      'deliveryPrice': order.deliveryPrice,
      'discountAmount': order.discountAmount,
      'tipAmount': order.tipAmount,
      'advance': order.advance,
      'subtotal': order.subtotal,
      'total': order.total,
      'notes': order.notes,
      'status': _mapStatusToPC(order.status),
      'createdAt': order.createdAt.toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
      'photoIds': order.photoIds,
      'photoGroups': order.photoGroups,
      'linkedInvoiceId': order.linkedInvoiceId,
      'type': order.type.name,
    };
  }

  /// Uploader une photo vers le serveur PC
  Future<Map<String, dynamic>> uploadPhoto(
    String filePath,
    String orderId, {
    String? title,
    String? description,
    Map<String, String>? ocrData,
  }) async {
    try {
      final file = File(filePath);
      if (!file.existsSync()) {
        throw Exception('Fichier non trouvé: $filePath');
      }

      final request = http.MultipartRequest(
        'POST',
        Uri.parse('$_serverUrl/upload-photo'),
      );

      // Headers
      request.headers.addAll({
        'User-Agent': 'CommandTaker-Mobile/1.0',
        'X-Client-Version': '1.0.0',
        'X-Platform': 'mobile',
      });

      // Fichier
      request.files.add(await http.MultipartFile.fromPath('file', filePath));

      // Métadonnées
      request.fields['orderId'] = orderId;
      if (title != null) request.fields['title'] = title;
      if (description != null) request.fields['description'] = description;
      if (ocrData != null) request.fields['ocrData'] = json.encode(ocrData);

      final response = await request.send().timeout(
        const Duration(seconds: 60),
      );
      final responseBody = await response.stream.bytesToString();

      if (response.statusCode == 200) {
        final data = json.decode(responseBody);
        if (data['success'] == true) {
          return {
            'success': true,
            'photoId': data['data']['id'],
            'url': data['data']['url'],
            'fileName': data['data']['fileName'],
            'fileSize': data['data']['fileSize'],
            'mimeType': data['data']['mimeType'],
          };
        } else {
          throw Exception(data['error']['message'] ?? 'Erreur upload');
        }
      } else {
        throw Exception('Erreur HTTP ${response.statusCode}: $responseBody');
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  // Méthode _mapStatusFromPC supprimée car non utilisée actuellement
  // Elle sera réactivée quand la synchronisation bidirectionnelle sera implémentée
}
