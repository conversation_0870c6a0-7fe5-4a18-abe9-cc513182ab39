import 'package:csv/csv.dart';
import 'package:general_hcp_crm/models/product.dart';
import 'package:general_hcp_crm/models/category.dart'; // Utilisé pour obtenir le nom de la catégorie
import 'package:flutter/foundation.dart'
    hide
        Category; // Pour debugPrint, en cachant Category pour éviter le conflit
import 'package:general_hcp_crm/services/inventory_service.dart'; // Pour récupérer les catégories

class CsvService {
  final InventoryService _inventoryService = InventoryService.instance;

  Future<String> exportProductsToCsv(List<Product> products) async {
    List<Category> categories = await _inventoryService.getCategories();
    Map<String, String> categoryMap = {
      for (var cat in categories) cat.id: cat.name,
    };

    List<List<dynamic>> rows = [];
    // En-têtes CSV (cohérent avec Excel)
    rows.add(['Nom', 'Quantité', 'Catégorie']);

    for (var product in products) {
      rows.add([
        product.name,
        product.quantity,
        categoryMap[product.categoryId] ?? 'N/A', // Nom de la catégorie
      ]);
    }
    return const ListToCsvConverter().convert(rows);
  }

  // Note: L'importation est plus complexe car elle nécessite de gérer les catégories (création si inexistante ou liaison)
  // et potentiellement la validation des données.
  // Pour cet exemple, nous allons supposer que les catégories existent déjà par leur nom.
  Future<List<Product>> importProductsFromCsv(String csvString) async {
    List<List<dynamic>> csvTable = const CsvToListConverter(
      eol: '\n',
      fieldDelimiter: ',',
    ).convert(csvString);
    List<Product> importedProducts = [];
    List<Category> existingCategories = await _inventoryService.getCategories();

    if (csvTable.length < 2) {
      // Au moins une ligne d'en-tête et une ligne de données
      throw Exception('Fichier CSV vide ou ne contient que les en-têtes.');
    }

    // Valider les en-têtes (cohérent avec Excel)
    List<dynamic> headerRow = csvTable.first;
    if (headerRow.length < 3 ||
        headerRow[0].toString().toLowerCase().trim() != 'nom' ||
        headerRow[1].toString().toLowerCase().trim() != 'quantité' ||
        headerRow[2].toString().toLowerCase().trim() != 'catégorie') {
      throw Exception(
        'Format d\'en-tête CSV invalide. Attendu: Nom, Quantité, Catégorie',
      );
    }

    for (int i = 1; i < csvTable.length; i++) {
      List<dynamic> row = csvTable[i];
      if (row.length < 3) {
        // S'assurer qu'on a au moins les champs obligatoires
        debugPrint('Ligne ignorée (pas assez de colonnes): $row');
        continue;
      }

      String productName = row[0].toString().trim();
      int? productQuantity = int.tryParse(row[1].toString().trim());
      String categoryName = row[2].toString().trim();

      if (productName.isEmpty ||
          productQuantity == null ||
          categoryName.isEmpty) {
        debugPrint('Ligne ignorée (données invalides ou manquantes): $row');
        continue;
      }

      // Trouver ou créer la catégorie
      Category? category;
      try {
        category = existingCategories.firstWhere(
          (cat) => cat.name.toLowerCase() == categoryName.toLowerCase(),
        );
      } catch (e) {
        // Catégorie non trouvée
        category = null;
      }
      if (category == null) {
        // Option: créer la catégorie si elle n'existe pas
        // Pour cet exemple, on va ignorer le produit si la catégorie n'existe pas pour simplifier
        // ou lever une exception.
        // category = await _inventoryService.addCategory(Category(id: '', name: categoryName));
        // existingCategories.add(category); // Ajouter à la liste pour les prochaines itérations
        debugPrint(
          'Catégorie "$categoryName" non trouvée pour le produit "$productName". Produit ignoré.',
        );
        continue;
      }

      importedProducts.add(
        Product(
          id: '', // Sera généré par le service lors de l'ajout
          name: productName,
          price: 0.0, // Prix par défaut car non inclus dans le CSV simplifié
          quantity: productQuantity,
          description:
              '', // Description vide car non incluse dans le CSV simplifié
          categoryId: category.id,
          imageUrl: null, // Pas d'image dans le CSV simplifié
        ),
      );
    }
    return importedProducts;
  }

  String getCsvTemplate() {
    List<List<dynamic>> rows = [];
    // En-têtes CSV (cohérent avec Excel)
    rows.add(['Nom', 'Quantité', 'Catégorie']);

    // Exemple de produit
    rows.add(['Produit Exemple', '5', 'Catégorie Exemple']);

    return const ListToCsvConverter().convert(rows);
  }
}
