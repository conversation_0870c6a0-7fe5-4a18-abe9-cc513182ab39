import 'package:flutter/material.dart';
import '../models/advanced_task.dart';

class KanbanView extends StatefulWidget {
  final List<AdvancedTask> tasks;
  final Function(AdvancedTask) onTaskTap;
  final Function(AdvancedTask, TaskStatus) onStatusChanged;

  const Kanban<PERSON>iew({
    super.key,
    required this.tasks,
    required this.onTaskTap,
    required this.onStatusChanged,
  });

  @override
  State<KanbanView> createState() => _KanbanViewState();
}

class _KanbanViewState extends State<KanbanView> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;
    final columnWidth =
        isMobile
            ? screenWidth * 0.85
            : 320.0; // Plus large pour éviter la troncature

    return SingleChildScrollView(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      padding: EdgeInsets.symmetric(
        horizontal: isMobile ? 12 : 16,
        vertical: isMobile ? 8 : 16,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children:
            TaskStatus.values.map((status) {
              final statusTasks =
                  widget.tasks.where((task) => task.status == status).toList();
              return _buildKanbanColumn(
                status,
                statusTasks,
                columnWidth,
                isMobile,
              );
            }).toList(),
      ),
    );
  }

  Widget _buildKanbanColumn(
    TaskStatus status,
    List<AdvancedTask> tasks,
    double columnWidth,
    bool isMobile,
  ) {
    return Container(
      width: columnWidth,
      margin: EdgeInsets.only(right: isMobile ? 12 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildColumnHeader(status, tasks.length),
          const SizedBox(height: 12),
          Expanded(
            child: DragTarget<AdvancedTask>(
              onAcceptWithDetails: (details) {
                final task = details.data;
                if (task.status != status) {
                  widget.onStatusChanged(task, status);
                }
              },
              builder: (context, candidateData, rejectedData) {
                return Container(
                  decoration: BoxDecoration(
                    color:
                        candidateData.isNotEmpty
                            ? _getStatusColor(status).withValues(alpha: 0.1)
                            : Colors.grey.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color:
                          candidateData.isNotEmpty
                              ? _getStatusColor(status)
                              : Colors.grey.withValues(alpha: 0.2),
                      width: candidateData.isNotEmpty ? 2 : 1,
                    ),
                  ),
                  child:
                      tasks.isEmpty
                          ? _buildEmptyColumn(status)
                          : ListView.builder(
                            padding: const EdgeInsets.all(8),
                            itemCount: tasks.length,
                            itemBuilder: (context, index) {
                              final task = tasks[index];
                              return _buildKanbanCard(task, columnWidth);
                            },
                          ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColumnHeader(TaskStatus status, int taskCount) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: _getStatusColor(status)),
      ),
      child: Row(
        children: [
          Icon(
            _getStatusIcon(status),
            color: _getStatusColor(status),
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              status.displayName,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: _getStatusColor(status),
                fontSize: 16,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getStatusColor(status),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              taskCount.toString(),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyColumn(TaskStatus status) {
    return SizedBox(
      height: 200,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getStatusIcon(status),
              size: 48,
              color: Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 12),
            Text(
              'Aucune tâche',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Glissez une tâche ici',
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.5),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKanbanCard(AdvancedTask task, double columnWidth) {
    return Draggable<AdvancedTask>(
      data: task,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(12),
        child: SizedBox(
          width: columnWidth - 20,
          child: _buildTaskCard(task, isDragging: true),
        ),
      ),
      childWhenDragging: Opacity(opacity: 0.5, child: _buildTaskCard(task)),
      child: _buildTaskCard(task),
    );
  }

  Widget _buildTaskCard(AdvancedTask task, {bool isDragging = false}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isDragging ? 12 : 3,
      shadowColor: Colors.black.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16), // Arrondi 2xl
      ),
      child: InkWell(
        onTap: () => widget.onTaskTap(task),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Colors.white, Colors.grey.withValues(alpha: 0.02)],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // En-tête avec priorité
                Row(
                  children: [
                    Container(
                      width: 4,
                      height: 20,
                      decoration: BoxDecoration(
                        color: task.priority.color,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        task.title,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(Icons.flag, color: task.priority.color, size: 16),
                  ],
                ),

                if (task.description.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    task.description,
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                const SizedBox(height: 8),

                // Tags
                if (task.tags.isNotEmpty) ...[
                  Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children:
                        task.tags
                            .take(3)
                            .map(
                              (tag) => Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.blue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.blue.withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Text(
                                  tag,
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                  ),
                  const SizedBox(height: 8),
                ],

                // Pied de carte
                Row(
                  children: [
                    if (task.dueDate != null) ...[
                      Icon(
                        task.isOverdue ? Icons.warning : Icons.schedule,
                        size: 14,
                        color: task.isOverdue ? Colors.red : Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatDate(task.dueDate!),
                        style: TextStyle(
                          fontSize: 11,
                          color: task.isOverdue ? Colors.red : Colors.grey,
                        ),
                      ),
                    ],
                    const Spacer(),
                    if (task.assigneeId != null) ...[
                      CircleAvatar(
                        radius: 10,
                        backgroundColor: Colors.blue.withValues(alpha: 0.1),
                        child: Text(
                          task.assigneeId!.substring(0, 1).toUpperCase(),
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.inReview:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.blocked:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Icons.radio_button_unchecked;
      case TaskStatus.inProgress:
        return Icons.play_circle;
      case TaskStatus.inReview:
        return Icons.rate_review;
      case TaskStatus.completed:
        return Icons.check_circle;
      case TaskStatus.blocked:
        return Icons.block;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference == 0) {
      return "Aujourd'hui";
    } else if (difference == 1) {
      return "Demain";
    } else if (difference == -1) {
      return "Hier";
    } else if (difference > 0) {
      return "Dans $difference j";
    } else {
      return "Il y a ${-difference} j";
    }
  }
}
