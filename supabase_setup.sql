-- Script de configuration Supabase pour HCP CRM
-- À exécuter dans l'éditeur SQL de votre projet Supabase

-- ===== TABLE CATÉGORIES =====
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    default_price DECIMAL(10,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== TABLE PRODUITS =====
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 0,
    description TEXT DEFAULT '',
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== TABLE FACTURES =====
CREATE TABLE IF NOT EXISTS invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    client_name TEXT NOT NULL,
    client_number TEXT NOT NULL,
    products TEXT NOT NULL,
    items JSONB NOT NULL, -- Liste des items de la facture
    delivery_location TEXT NOT NULL,
    delivery_details TEXT,
    delivery_price DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    advance DECIMAL(10,2) NOT NULL DEFAULT 0,
    subtotal DECIMAL(10,2) NOT NULL,
    total DECIMAL(10,2) NOT NULL,
    notes TEXT,
    logo_path TEXT,
    footer_note TEXT,
    status TEXT NOT NULL DEFAULT 'en_attente',
    product_image_path TEXT,
    type TEXT NOT NULL DEFAULT 'normale',
    validity_date TIMESTAMP WITH TIME ZONE,
    client_address TEXT,
    client_email TEXT,
    company_rccm TEXT,
    company_tax_number TEXT,
    payment_methods JSONB, -- Liste des modes de paiement
    special_conditions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== TABLE COLIS =====
CREATE TABLE IF NOT EXISTS colis (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    libelle TEXT NOT NULL,
    photo_path TEXT NOT NULL,
    zone_livraison TEXT NOT NULL,
    numero_client TEXT NOT NULL,
    reste_a_payer DECIMAL(10,2) NOT NULL DEFAULT 0,
    frais_livraison DECIMAL(10,2) NOT NULL DEFAULT 0,
    date_ajout TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    statut TEXT NOT NULL DEFAULT 'brouillon',
    nom_client TEXT,
    adresse_livraison TEXT,
    notes TEXT,
    facture_id UUID REFERENCES invoices(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== TABLE TÂCHES =====
CREATE TABLE IF NOT EXISTS tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    due_date TIMESTAMP WITH TIME ZONE NOT NULL,
    priority TEXT NOT NULL DEFAULT 'medium',
    is_completed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===== INDEXES POUR LES PERFORMANCES =====

-- Index pour les factures
CREATE INDEX IF NOT EXISTS idx_invoices_client_name ON invoices(client_name);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_invoices_created_at ON invoices(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_invoices_type ON invoices(type);

-- Index pour les colis
CREATE INDEX IF NOT EXISTS idx_colis_statut ON colis(statut);
CREATE INDEX IF NOT EXISTS idx_colis_date_ajout ON colis(date_ajout DESC);
CREATE INDEX IF NOT EXISTS idx_colis_facture_id ON colis(facture_id);

-- Index pour les produits
CREATE INDEX IF NOT EXISTS idx_products_category_id ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);
CREATE INDEX IF NOT EXISTS idx_products_quantity ON products(quantity);

-- Index pour les tâches
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority);
CREATE INDEX IF NOT EXISTS idx_tasks_is_completed ON tasks(is_completed);

-- Index pour les catégories
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);

-- ===== POLITIQUES RLS (Row Level Security) =====

-- Activer RLS sur toutes les tables
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE colis ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;

-- Politiques pour permettre l'accès anonyme (pour l'application mobile)
-- Note: En production, vous devriez implémenter une authentification appropriée

-- Catégories
CREATE POLICY "Allow anonymous access to categories" ON categories
    FOR ALL USING (true);

-- Produits
CREATE POLICY "Allow anonymous access to products" ON products
    FOR ALL USING (true);

-- Factures
CREATE POLICY "Allow anonymous access to invoices" ON invoices
    FOR ALL USING (true);

-- Colis
CREATE POLICY "Allow anonymous access to colis" ON colis
    FOR ALL USING (true);

-- Tâches
CREATE POLICY "Allow anonymous access to tasks" ON tasks
    FOR ALL USING (true);

-- ===== FONCTIONS POUR MISE À JOUR AUTOMATIQUE =====

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour mettre à jour updated_at
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_colis_updated_at BEFORE UPDATE ON colis
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===== DONNÉES INITIALES =====

-- Insérer quelques catégories par défaut
INSERT INTO categories (name, default_price) VALUES
    ('Électronique', 50000),
    ('Vêtements', 15000),
    ('Livres', 5000),
    ('Accessoires', 10000),
    ('Autres', 0)
ON CONFLICT DO NOTHING;

-- ===== BUCKET DE STOCKAGE =====

-- Note: Le bucket 'hcp-crm-files' doit être créé manuellement dans l'interface Supabase
-- Aller dans Storage > New bucket > Nom: hcp-crm-files > Public bucket

-- ===== COMMENTAIRES =====

COMMENT ON TABLE categories IS 'Catégories de produits';
COMMENT ON TABLE products IS 'Produits du catalogue';
COMMENT ON TABLE invoices IS 'Factures et devis';
COMMENT ON TABLE colis IS 'Colis et livraisons';
COMMENT ON TABLE tasks IS 'Tâches et rappels';

COMMENT ON COLUMN invoices.items IS 'Liste des items de la facture au format JSON';
COMMENT ON COLUMN invoices.payment_methods IS 'Modes de paiement acceptés au format JSON';
COMMENT ON COLUMN colis.facture_id IS 'Référence vers la facture associée';
COMMENT ON COLUMN products.category_id IS 'Référence vers la catégorie du produit'; 