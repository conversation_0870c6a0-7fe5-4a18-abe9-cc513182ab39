import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import '../models/invoice.dart';
import '../models/product.dart';
import '../services/invoice_service.dart';
import '../services/inventory_service.dart';
import '../services/gamification_service.dart';
import '../widgets/celebration_animation.dart';

class CreateInvoicePage extends StatefulWidget {
  final Invoice? duplicateFrom;

  const CreateInvoicePage({super.key, this.duplicateFrom});

  @override
  State<CreateInvoicePage> createState() => _CreateInvoicePageState();
}

class _CreateInvoicePageState extends State<CreateInvoicePage> {
  final _formKey = GlobalKey<FormState>();
  final _clientNameController = TextEditingController();
  final _clientNumberController = TextEditingController();
  final _productsController = TextEditingController();
  final _deliveryDetailsController =
      TextEditingController(); // Nouveau contrôleur pour les détails de livraison
  final _discountController =
      TextEditingController(); // Nouveau contrôleur pour la remise
  final _tipController =
      TextEditingController(); // Nouveau contrôleur pour le pourboire
  final _advanceController = TextEditingController();
  final _notesController = TextEditingController();
  final _footerNoteController = TextEditingController();
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');

  String? _selectedDeliveryZone;
  String? _selectedLogoPath;
  String? _selectedProductImagePath; // Chemin de l'image du produit principal
  final List<InvoiceItem> _items = [];
  final List<Product> _filteredStockProducts = [];
  final TextEditingController _stockSearchController = TextEditingController();
  bool _isLoading = false;
  List<Product> _stockProducts = [];
  final InventoryService _inventoryService = InventoryService.instance;
  final InvoiceService _invoiceService = InvoiceService();

  @override
  void initState() {
    super.initState();
    _loadStockProducts();

    // Si on duplique une facture, initialiser les champs
    if (widget.duplicateFrom != null) {
      _initializeFromDuplicate();
    } else {
      _addNewItem(); // Ajouter un premier article par défaut
    }
  }

  Future<void> _loadStockProducts() async {
    try {
      final products = await _inventoryService.getProducts();
      setState(() {
        _stockProducts = products;
        _filteredStockProducts.clear();
        _filteredStockProducts.addAll(products);
      });
    } catch (e) {
      // Gérer l'erreur silencieusement
    }
  }

  /// Initialise les champs avec les données de la facture à dupliquer
  void _initializeFromDuplicate() {
    final invoice = widget.duplicateFrom!;

    // Remplir les champs client
    _clientNameController.text = invoice.clientName;
    _clientNumberController.text = invoice.clientNumber;
    _notesController.text = invoice.notes ?? '';

    // Ajouter les articles de la facture originale
    _items.clear();
    for (final item in invoice.items) {
      _items.add(
        InvoiceItem(
          id: _invoiceService.generateItemId(),
          name: item.name,
          quantity: item.quantity,
          price: item.price,
          isCustom: item.isCustom,
          categoryName: item.categoryName,
          productId: item.productId,
          isFromStock: item.isFromStock,
        ),
      );
    }

    // Si aucun article, ajouter un article vide
    if (_items.isEmpty) {
      _addNewItem();
    }
  }

  void _filterStockProducts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredStockProducts.clear();
        _filteredStockProducts.addAll(_stockProducts);
      } else {
        _filteredStockProducts.clear();
        _filteredStockProducts.addAll(
          _stockProducts
              .where(
                (product) =>
                    product.name.toLowerCase().contains(query.toLowerCase()),
              )
              .toList(),
        );
      }
    });
  }

  void _showEditCategoriesDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Modifier les catégories prédéfinies'),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child: Column(
                children: [
                  const Text(
                    'Note: Les modifications seront temporaires pour cette session uniquement.',
                    style: TextStyle(fontSize: 12, color: Colors.orange),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: ListView.builder(
                      itemCount: PredefinedCategories.categories.length,
                      itemBuilder: (context, index) {
                        final category = PredefinedCategories.categories[index];
                        return Card(
                          child: ListTile(
                            title: Text(category['name']),
                            subtitle: Text(
                              '${_currencyFormat.format(category['price'])} FCFA',
                            ),
                            trailing: IconButton(
                              icon: const Icon(Icons.edit),
                              onPressed: () => _editCategory(index),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  void _editCategory(int index) {
    final category = PredefinedCategories.categories[index];
    final nameController = TextEditingController(text: category['name']);
    final priceController = TextEditingController(
      text: category['price'].toString(),
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Modifier la catégorie'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Nom de la catégorie',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: priceController,
                  decoration: const InputDecoration(
                    labelText: 'Prix (FCFA)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  final newPrice = double.tryParse(priceController.text);
                  if (nameController.text.isNotEmpty &&
                      newPrice != null &&
                      newPrice > 0) {
                    setState(() {
                      PredefinedCategories.categories[index] = {
                        'name': nameController.text,
                        'price': newPrice,
                      };
                    });
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Catégorie modifiée avec succès'),
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                          'Veuillez remplir tous les champs correctement',
                        ),
                      ),
                    );
                  }
                },
                child: const Text('Sauvegarder'),
              ),
            ],
          ),
    );
  }

  void _addNewItem() {
    setState(() {
      _items.add(
        InvoiceItem(
          id: _invoiceService.generateItemId(),
          name: '',
          price: 0,
          quantity: 1,
          isCustom: false,
        ),
      );
    });
  }

  void _removeItem(int index) {
    if (_items.length > 1) {
      setState(() {
        _items.removeAt(index);
      });
    }
  }

  void _updateItem(int index, InvoiceItem updatedItem) {
    setState(() {
      _items[index] = updatedItem;
    });
  }

  double get _subtotal => _invoiceService.calculateSubtotal(_items);
  double get _deliveryPrice =>
      _selectedDeliveryZone != null
          ? DeliveryZones.getDeliveryPrice(_selectedDeliveryZone!)
          : 0;
  double get _advance => double.tryParse(_advanceController.text) ?? 0;
  double get _discount =>
      double.tryParse(_discountController.text) ?? 0; // Getter pour la remise
  double get _tip =>
      double.tryParse(_tipController.text) ?? 0; // Getter pour le pourboire
  double get _total => _invoiceService.calculateTotal(
    subtotal: _subtotal,
    deliveryPrice: _deliveryPrice,
    discountAmount: _discount, // Ajout de la remise au calcul
    tipAmount: _tip, // Ajout du pourboire au calcul
    advance: _advance,
  );

  Future<void> _selectProductImage() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null) {
        setState(() {
          _selectedProductImagePath = result.files.single.path;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Image du produit sélectionnée')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sélection: $e')),
        );
      }
    }
  }

  Future<void> _saveInvoice() async {
    if (!_formKey.currentState!.validate()) return;
    if (_items.isEmpty || _items.every((item) => item.name.isEmpty)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez ajouter au moins un article')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final invoice = Invoice(
        id: '', // Sera généré par le service
        clientName: _clientNameController.text.trim(),
        clientNumber: _clientNumberController.text.trim(),
        products: _productsController.text.trim(),
        items: _items.where((item) => item.name.isNotEmpty).toList(),
        deliveryLocation: _selectedDeliveryZone ?? '',
        deliveryDetails:
            _deliveryDetailsController.text.trim().isEmpty
                ? null
                : _deliveryDetailsController.text
                    .trim(), // Ajout des détails de livraison
        discountAmount: _discount, // Ajout de la remise
        tipAmount: _tip, // Ajout du pourboire
        deliveryPrice: _deliveryPrice,
        advance: _advance,
        subtotal: _subtotal,
        total: _total,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        logoPath: _selectedLogoPath,
        productImagePath:
            _selectedProductImagePath, // Ajout du chemin de l'image du produit
        footerNote:
            _footerNoteController.text.trim().isEmpty
                ? null
                : _footerNoteController.text.trim(),
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
      );

      // Utiliser la méthode avec gamification
      final result = await _invoiceService.addInvoiceWithGamification(invoice);
      final newInvoice = result['invoice'] as Invoice;
      final reward = result['reward'] as GamificationReward?;

      if (mounted) {
        // Afficher l'animation de célébration si il y a une récompense
        if (reward != null && newInvoice.status == InvoiceStatus.terminee) {
          await showDialog(
            context: context,
            barrierDismissible: false,
            builder:
                (context) => CelebrationAnimation(
                  reward: reward,
                  onComplete: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Facture créée avec succès'),
                      ),
                    );
                    Navigator.pop(context, true);
                  },
                ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Facture créée avec succès')),
          );
          Navigator.pop(context, true);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la création: $e')),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Widget _buildItemCard(int index) {
    final item = _items[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Article ${index + 1}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_items.length > 1)
                  IconButton(
                    onPressed: () => _removeItem(index),
                    icon: const Icon(Icons.delete, color: Colors.red),
                    tooltip: 'Supprimer cet article',
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Choix entre catégorie prédéfinie, produit en stock et article personnalisé
            Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('Catégorie prédéfinie'),
                        value: 'predefined',
                        groupValue:
                            item.isFromStock
                                ? 'stock'
                                : (item.isCustom ? 'custom' : 'predefined'),
                        onChanged: (value) {
                          _updateItem(
                            index,
                            item.copyWith(
                              isCustom: false,
                              isFromStock: false,
                              name: '',
                              price: 0,
                              productId: null,
                            ),
                          );
                        },
                        dense: true,
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<String>(
                        title: const Text('Produit en stock'),
                        value: 'stock',
                        groupValue:
                            item.isFromStock
                                ? 'stock'
                                : (item.isCustom ? 'custom' : 'predefined'),
                        onChanged: (value) {
                          _updateItem(
                            index,
                            item.copyWith(
                              isCustom: false,
                              isFromStock: true,
                              name: '',
                              price: 0,
                              productId: null,
                            ),
                          );
                        },
                        dense: true,
                      ),
                    ),
                  ],
                ),
                RadioListTile<String>(
                  title: const Text('Article personnalisé'),
                  value: 'custom',
                  groupValue:
                      item.isFromStock
                          ? 'stock'
                          : (item.isCustom ? 'custom' : 'predefined'),
                  onChanged: (value) {
                    _updateItem(
                      index,
                      item.copyWith(
                        isCustom: true,
                        isFromStock: false,
                        name: '',
                        price: 0,
                        productId: null,
                      ),
                    );
                  },
                  dense: true,
                ),
              ],
            ),

            const SizedBox(height: 16),

            if (!item.isCustom && !item.isFromStock)
              // Sélection de catégorie prédéfinie avec option de modification
              Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<String>(
                          decoration: const InputDecoration(
                            labelText: 'Catégorie prédéfinie',
                            border: OutlineInputBorder(),
                          ),
                          value: item.name.isEmpty ? null : item.name,
                          items:
                              PredefinedCategories.categories.map((category) {
                                return DropdownMenuItem<String>(
                                  value: category['name'],
                                  child: Text(
                                    '${category['name']} - ${_currencyFormat.format(category['price'])} FCFA',
                                  ),
                                );
                              }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              final category = PredefinedCategories.categories
                                  .firstWhere((cat) => cat['name'] == value);
                              _updateItem(
                                index,
                                item.copyWith(
                                  name: value,
                                  price: category['price'].toDouble(),
                                  categoryName: value,
                                ),
                              );
                            }
                          },
                          validator:
                              (value) =>
                                  value == null
                                      ? 'Sélectionnez une catégorie'
                                      : null,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => _showEditCategoriesDialog(),
                        icon: const Icon(Icons.edit),
                        tooltip: 'Modifier les catégories',
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.blue[100],
                          foregroundColor: Colors.blue[900],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            if (item.isFromStock)
              // Sélection de produit en stock avec recherche
              Column(
                children: [
                  TextFormField(
                    controller: _stockSearchController,
                    decoration: const InputDecoration(
                      labelText: 'Rechercher un produit',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.search),
                      hintText: 'Tapez pour rechercher...',
                    ),
                    onChanged: _filterStockProducts,
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Produit en stock',
                      border: OutlineInputBorder(),
                    ),
                    value: item.productId,
                    items:
                        _filteredStockProducts.map((product) {
                          return DropdownMenuItem<String>(
                            value: product.id,
                            child: Text(
                              '${product.name} - ${_currencyFormat.format(product.price)} FCFA (Stock: ${product.quantity})',
                            ),
                          );
                        }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        final product = _stockProducts.firstWhere(
                          (p) => p.id == value,
                        );
                        _updateItem(
                          index,
                          item.copyWith(
                            name: product.name,
                            price: product.price,
                            productId: product.id,
                          ),
                        );
                      }
                    },
                    validator:
                        (value) =>
                            value == null ? 'Sélectionnez un produit' : null,
                  ),
                ],
              ),
            if (item.isCustom)
              // Article personnalisé
              Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Nom de l\'article',
                        border: OutlineInputBorder(),
                      ),
                      initialValue: item.name,
                      onChanged: (value) {
                        _updateItem(index, item.copyWith(name: value));
                      },
                      validator:
                          (value) =>
                              value?.isEmpty == true ? 'Nom requis' : null,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'Prix (FCFA)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      initialValue:
                          item.price > 0 ? item.price.toInt().toString() : '',
                      onChanged: (value) {
                        final price = double.tryParse(value) ?? 0;
                        _updateItem(index, item.copyWith(price: price));
                      },
                      validator: (value) {
                        final price = double.tryParse(value ?? '');
                        return price == null || price <= 0
                            ? 'Prix invalide'
                            : null;
                      },
                    ),
                  ),
                ],
              ),

            const SizedBox(height: 16),

            // Quantité et total
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Quantité',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    initialValue: item.quantity.toString(),
                    onChanged: (value) {
                      final quantity = int.tryParse(value) ?? 1;
                      _updateItem(index, item.copyWith(quantity: quantity));
                    },
                    validator: (value) {
                      final quantity = int.tryParse(value ?? '');
                      return quantity == null || quantity <= 0
                          ? 'Quantité invalide'
                          : null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Total article',
                          style: TextStyle(fontSize: 12, color: Colors.grey),
                        ),
                        Text(
                          '${_currencyFormat.format(item.total)} FCFA',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalSection() {
    return Card(
      color: Colors.blue[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'RÉCAPITULATIF',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildTotalRow(
              'Sous-total:',
              '${_currencyFormat.format(_subtotal)} FCFA',
            ),
            _buildTotalRow(
              'Livraison:',
              '${_currencyFormat.format(_deliveryPrice)} FCFA',
            ),
            if (_tip > 0)
              _buildTotalRow(
                'Pourboire:',
                '+${_currencyFormat.format(_tip)} FCFA',
              ),
            if (_discount > 0)
              _buildTotalRow(
                'Remise:',
                '-${_currencyFormat.format(_discount)} FCFA',
              ),
            if (_advance > 0)
              _buildTotalRow(
                'Avance:',
                '-${_currencyFormat.format(_advance)} FCFA',
              ),
            const Divider(),
            _buildTotalRow(
              'TOTAL:',
              '${_currencyFormat.format(_total)} FCFA',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.blue : null,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nouvelle Facture'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Informations client
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'INFORMATIONS CLIENT',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _clientNameController,
                      decoration: const InputDecoration(
                        labelText: 'Nom du client *',
                        border: OutlineInputBorder(),
                      ),
                      validator:
                          (value) =>
                              value?.isEmpty == true ? 'Nom requis' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _clientNumberController,
                      decoration: const InputDecoration(
                        labelText: 'Numéro de téléphone *',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.phone,
                      validator:
                          (value) =>
                              value?.isEmpty == true ? 'Numéro requis' : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _productsController,
                      decoration: const InputDecoration(
                        labelText: 'Description générale de la commande *',
                        border: OutlineInputBorder(),
                        hintText: 'Décrivez votre commande en détail...',
                      ),
                      keyboardType: TextInputType.multiline,
                      maxLines: 5,
                      minLines: 3,
                      validator:
                          (value) =>
                              value?.isEmpty == true
                                  ? 'Description requise'
                                  : null,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Articles
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'ARTICLES',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: _addNewItem,
                  icon: const Icon(Icons.add),
                  label: const Text('Ajouter'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[900],
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            ..._items.asMap().entries.map((entry) => _buildItemCard(entry.key)),

            const SizedBox(height: 16),

            // Livraison et avance
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'LIVRAISON ET PAIEMENT',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'Zone de livraison *',
                        border: OutlineInputBorder(),
                      ),
                      value: _selectedDeliveryZone,
                      items:
                          DeliveryZones.availableZones.map((zone) {
                            return DropdownMenuItem<String>(
                              value: zone,
                              child: Text(
                                '$zone - ${_currencyFormat.format(DeliveryZones.getDeliveryPrice(zone))} FCFA',
                              ),
                            );
                          }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedDeliveryZone = value;
                        });
                      },
                      validator:
                          (value) =>
                              value == null
                                  ? 'Zone de livraison requise'
                                  : null,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _deliveryDetailsController,
                      decoration: const InputDecoration(
                        labelText: 'Détails de livraison (optionnel)',
                        border: OutlineInputBorder(),
                        hintText: 'Ex: Laisser le colis à la réception',
                      ),
                      maxLines: 2,
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _discountController,
                      decoration: const InputDecoration(
                        labelText: 'Remise (optionnel)',
                        hintText: 'Montant de la remise',
                        border: OutlineInputBorder(),
                        prefixText: 'FCFA ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*$'),
                        ),
                      ],
                      onChanged:
                          (_) => setState(
                            () {},
                          ), // Recalculer le total lors de la saisie
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _tipController,
                      decoration: const InputDecoration(
                        labelText: 'Pourboire (optionnel)',
                        hintText: 'Montant du pourboire',
                        border: OutlineInputBorder(),
                        prefixText: 'FCFA ',
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*$'),
                        ),
                      ],
                      onChanged:
                          (_) => setState(
                            () {},
                          ), // Recalculer le total lors de la saisie
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _advanceController,
                      decoration: const InputDecoration(
                        labelText: 'Avance (FCFA)',
                        border: OutlineInputBorder(),
                        hintText: '0',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      onChanged: (value) => setState(() {}),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notes (optionnel)',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    // Sélection du logo
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _selectedLogoPath != null
                                ? 'Logo sélectionné: ${_selectedLogoPath!.split('\\').last}'
                                : 'Aucun logo sélectionné',
                            style: TextStyle(
                              color:
                                  _selectedLogoPath != null
                                      ? Colors.green
                                      : Colors.grey,
                            ),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _selectLogo,
                          icon: const Icon(Icons.image),
                          label: const Text('Choisir logo'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[900],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Sélection de l'image du produit
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _selectedProductImagePath != null
                                ? 'Image produit: ${_selectedProductImagePath!.split('\\').last}'
                                : 'Aucune image de produit sélectionnée',
                            style: TextStyle(
                              color:
                                  _selectedProductImagePath != null
                                      ? Colors.green
                                      : Colors.grey,
                            ),
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _selectProductImage,
                          icon: const Icon(Icons.photo),
                          label: const Text('Image produit'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[700],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _footerNoteController,
                      decoration: const InputDecoration(
                        labelText: 'Note de bas de page (optionnel)',
                        border: OutlineInputBorder(),
                        hintText: 'Ex: Merci pour votre confiance - HCP-DESIGN',
                      ),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Récapitulatif
            _buildTotalSection(),

            const SizedBox(height: 24),

            // Bouton de sauvegarde
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _saveInvoice,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[900],
                  foregroundColor: Colors.white,
                ),
                child:
                    _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                          'CRÉER LA FACTURE',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectLogo() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        setState(() {
          _selectedLogoPath = result.files.single.path;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sélection du logo: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _clientNameController.dispose();
    _clientNumberController.dispose();
    _productsController.dispose();
    _deliveryDetailsController.dispose();
    _discountController.dispose();
    _tipController.dispose();
    _advanceController.dispose();
    _notesController.dispose();
    _footerNoteController.dispose();
    _stockSearchController.dispose();
    super.dispose();
  }
}
