import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/colis.dart';
import '../services/colis_service.dart';

class MarquerLivraisonPage extends StatefulWidget {
  final Colis colis;

  const MarquerLivraisonPage({
    super.key,
    required this.colis,
  });

  @override
  State<MarquerLivraisonPage> createState() => _MarquerLivraisonPageState();
}

class _MarquerLivraisonPageState extends State<MarquerLivraisonPage> {
  final ColisService _colisService = ColisService();
  final _notesController = TextEditingController();
  
  DateTime _dateLivraisonSelectionnee = DateTime.now();
  TimeOfDay _heureLivraisonSelectionnee = TimeOfDay.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _notesController.text = widget.colis.notes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectionnerDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dateLivraisonSelectionnee,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('fr', 'FR'),
      helpText: 'Sélectionner la date de livraison',
      cancelText: 'Annuler',
      confirmText: 'Confirmer',
    );

    if (date != null) {
      setState(() {
        _dateLivraisonSelectionnee = date;
      });
    }
  }

  Future<void> _selectionnerHeure() async {
    final heure = await showTimePicker(
      context: context,
      initialTime: _heureLivraisonSelectionnee,
      helpText: 'Sélectionner l\'heure de livraison',
      cancelText: 'Annuler',
      confirmText: 'Confirmer',
    );

    if (heure != null) {
      setState(() {
        _heureLivraisonSelectionnee = heure;
      });
    }
  }

  DateTime get _dateTimeComplet {
    return DateTime(
      _dateLivraisonSelectionnee.year,
      _dateLivraisonSelectionnee.month,
      _dateLivraisonSelectionnee.day,
      _heureLivraisonSelectionnee.hour,
      _heureLivraisonSelectionnee.minute,
    );
  }

  Future<void> _marquerCommeLivree() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Mettre à jour le statut avec la date personnalisée
      await _colisService.updateStatus(
        widget.colis.id,
        StatutLivraison.livree,
        dateLivraison: _dateTimeComplet,
      );

      // Mettre à jour les notes si elles ont changé
      if (_notesController.text.trim() != (widget.colis.notes ?? '')) {
        final colisModifie = widget.colis.copyWith(
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
        );
        await _colisService.updateColis(colisModifie);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Livraison marquée comme effectuée le ${DateFormat('dd/MM/yyyy à HH:mm').format(_dateTimeComplet)}',
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
        Navigator.of(context).pop(StatutLivraison.livree);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la mise à jour: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Marquer comme livrée'),
        backgroundColor: Colors.green[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Informations du colis
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informations du colis',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildInfoRow('Libellé', widget.colis.libelle),
                    _buildInfoRow('Client', widget.colis.nomClient ?? widget.colis.numeroClient),
                    _buildInfoRow('Zone', widget.colis.zoneLivraison),
                    _buildInfoRow(
                      'Reste à payer',
                      '${NumberFormat('#,##0', 'fr_FR').format(widget.colis.resteAPayer)} FCFA',
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Sélection de la date et heure
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date et heure de livraison',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Sélection de la date
                    InkWell(
                      onTap: _selectionnerDate,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.calendar_today, color: Colors.blue[600]),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Date de livraison',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  Text(
                                    DateFormat('EEEE d MMMM yyyy', 'fr_FR')
                                        .format(_dateLivraisonSelectionnee),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Icon(Icons.arrow_forward_ios, 
                                 size: 16, 
                                 color: Colors.grey[400]),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // Sélection de l'heure
                    InkWell(
                      onTap: _selectionnerHeure,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey[300]!),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.access_time, color: Colors.blue[600]),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'Heure de livraison',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  Text(
                                    _heureLivraisonSelectionnee.format(context),
                                    style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Icon(Icons.arrow_forward_ios, 
                                 size: 16, 
                                 color: Colors.grey[400]),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Résumé de la date/heure sélectionnée
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green[200]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.check_circle, color: Colors.green[600]),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Livraison effectuée le ${DateFormat('dd/MM/yyyy à HH:mm').format(_dateTimeComplet)}',
                              style: TextStyle(
                                color: Colors.green[700],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Notes
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Notes de livraison (optionnel)',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextFormField(
                      controller: _notesController,
                      maxLines: 3,
                      decoration: const InputDecoration(
                        hintText: 'Ajouter des notes sur la livraison...',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 30),

            // Bouton de confirmation
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _marquerCommeLivree,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                        'MARQUER COMME LIVRÉE',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }
}
