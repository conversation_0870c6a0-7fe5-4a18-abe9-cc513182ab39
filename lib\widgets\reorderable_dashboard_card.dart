import 'package:flutter/material.dart';
import '../services/dashboard_preferences_service.dart';

/// Widget wrapper pour les cartes réorganisables
class ReorderableDashboardCard extends StatelessWidget {
  final DashboardCardType type;
  final Widget child;
  final bool isReorderMode;
  final VoidCallback? onTap;

  const ReorderableDashboardCard({
    super.key,
    required this.type,
    required this.child,
    this.isReorderMode = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (!isReorderMode) {
      return child;
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        children: [
          child,
          // Overlay pour le mode réorganisation
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: const Center(
                child: Icon(
                  Icons.drag_indicator,
                  size: 32,
                  color: Colors.blue,
                ),
              ),
            ),
          ),
          // Bouton d'information
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.info_outline,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
