import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../models/invoice.dart';
import '../models/order_photo.dart';
import 'ocr_service.dart';
import 'firebase_service.dart';

/// Service de gestion des commandes avec support des photos multiples
class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  static OrderService get instance => _instance;

  final FirebaseService _firebaseService = FirebaseService.instance;
  final OcrService _ocrService = OcrService.instance;
  final String _ordersKey = 'orders';
  final String _photosKey = 'order_photos';
  // _photoGroupsKey supprimé car non utilisé

  /// Initialiser le service
  Future<void> initialize() async {
    await _ocrService.initialize();
    debugPrint('✅ Service de commandes initialisé');
  }

  /// Créer une nouvelle commande
  Future<String> createOrder(Invoice order) async {
    try {
      final orderId = order.id.isEmpty ? const Uuid().v4() : order.id;
      final orderWithId = order.copyWith(id: orderId);

      // Sauvegarder localement
      await _saveOrderLocally(orderWithId);

      // Synchroniser avec Firebase si possible
      try {
        await _firebaseService.addInvoice(orderWithId);
        debugPrint('✅ Commande synchronisée avec Firebase: $orderId');
      } catch (e) {
        debugPrint('⚠️ Synchronisation Firebase échouée: $e');
      }

      debugPrint('✅ Commande créée: $orderId');
      return orderId;
    } catch (e) {
      debugPrint('❌ Erreur création commande: $e');
      rethrow;
    }
  }

  /// Ajouter une photo à une commande avec OCR automatique
  Future<OrderPhoto> addPhotoToOrder(
    String orderId,
    String imagePath, {
    String? title,
    String? description,
    String? groupId,
    String? groupName,
  }) async {
    try {
      // Copier l'image dans le dossier de l'app
      final appImagePath = await _copyImageToAppDirectory(imagePath);

      // Analyser l'image avec OCR
      final ocrData = await _ocrService.analyzeImage(appImagePath);

      // Créer l'objet photo
      final photo = OrderPhoto(
        filePath: appImagePath,
        title: title,
        description: description,
        groupId: groupId,
        groupName: groupName,
        ocrData: ocrData,
      );

      // Sauvegarder la photo
      await _savePhotoLocally(photo);

      // Ajouter l'ID de la photo à la commande
      await _addPhotoIdToOrder(orderId, photo.id);

      debugPrint('✅ Photo ajoutée à la commande $orderId: ${photo.id}');
      return photo;
    } catch (e) {
      debugPrint('❌ Erreur ajout photo: $e');
      rethrow;
    }
  }

  /// Copier une image dans le dossier de l'application
  Future<String> _copyImageToAppDirectory(String sourcePath) async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final ordersDir = Directory('${appDir.path}/orders/photos');

      if (!await ordersDir.exists()) {
        await ordersDir.create(recursive: true);
      }

      final sourceFile = File(sourcePath);
      final fileName = '${const Uuid().v4()}.${sourcePath.split('.').last}';
      final targetPath = '${ordersDir.path}/$fileName';

      await sourceFile.copy(targetPath);
      return targetPath;
    } catch (e) {
      debugPrint('❌ Erreur copie image: $e');
      rethrow;
    }
  }

  /// Ajouter l'ID d'une photo à une commande
  Future<void> _addPhotoIdToOrder(String orderId, String photoId) async {
    try {
      final order = await getOrder(orderId);
      if (order != null) {
        final updatedPhotoIds = List<String>.from(order.photoIds)..add(photoId);
        final updatedOrder = order.copyWith(photoIds: updatedPhotoIds);
        await updateOrder(updatedOrder);
      }
    } catch (e) {
      debugPrint('❌ Erreur ajout photo ID à commande: $e');
      rethrow;
    }
  }

  /// Obtenir une commande par ID
  Future<Invoice?> getOrder(String orderId) async {
    try {
      // Essayer de charger depuis le cache local d'abord
      final localOrder = await _getOrderLocally(orderId);
      if (localOrder != null) {
        return localOrder;
      }

      // Essayer Firebase en fallback
      try {
        final firebaseData = await _firebaseService.getData(
          'invoices',
          orderId,
        );
        if (firebaseData != null) {
          return Invoice.fromJson(firebaseData);
        }
      } catch (e) {
        debugPrint('⚠️ Erreur chargement Firebase: $e');
      }

      return null;
    } catch (e) {
      debugPrint('❌ Erreur récupération commande: $e');
      return null;
    }
  }

  /// Obtenir toutes les commandes
  Future<List<Invoice>> getAllOrders() async {
    try {
      final orders = <Invoice>[];

      // Charger depuis le cache local
      final localOrders = await _getAllOrdersLocally();
      orders.addAll(localOrders);

      // Essayer de synchroniser avec Firebase
      try {
        final firebaseOrders = await _firebaseService.getAllInvoices();
        for (final firebaseOrder in firebaseOrders) {
          // Ajouter seulement si pas déjà présent localement
          if (!orders.any((order) => order.id == firebaseOrder.id)) {
            orders.add(firebaseOrder);
          }
        }
      } catch (e) {
        debugPrint('⚠️ Synchronisation Firebase échouée: $e');
      }

      // Trier par date de création (plus récent en premier)
      orders.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return orders;
    } catch (e) {
      debugPrint('❌ Erreur récupération commandes: $e');
      return [];
    }
  }

  /// Obtenir les photos d'une commande
  Future<List<OrderPhoto>> getOrderPhotos(String orderId) async {
    try {
      final order = await getOrder(orderId);
      if (order == null) return [];

      final photos = <OrderPhoto>[];
      for (final photoId in order.photoIds) {
        final photo = await _getPhotoLocally(photoId);
        if (photo != null) {
          photos.add(photo);
        }
      }

      return photos;
    } catch (e) {
      debugPrint('❌ Erreur récupération photos: $e');
      return [];
    }
  }

  /// Mettre à jour une commande
  Future<void> updateOrder(Invoice order) async {
    try {
      await _saveOrderLocally(order);

      // Synchroniser avec Firebase si possible
      try {
        await _firebaseService.updateInvoice(order);
      } catch (e) {
        debugPrint('⚠️ Synchronisation Firebase échouée: $e');
      }

      debugPrint('✅ Commande mise à jour: ${order.id}');
    } catch (e) {
      debugPrint('❌ Erreur mise à jour commande: $e');
      rethrow;
    }
  }

  /// Supprimer une commande
  Future<void> deleteOrder(String orderId) async {
    try {
      // Supprimer les photos associées
      final photos = await getOrderPhotos(orderId);
      for (final photo in photos) {
        await _deletePhoto(photo);
      }

      // Supprimer la commande localement
      await _deleteOrderLocally(orderId);

      // Supprimer de Firebase si possible
      try {
        await _firebaseService.deleteInvoice(orderId);
      } catch (e) {
        debugPrint('⚠️ Suppression Firebase échouée: $e');
      }

      debugPrint('✅ Commande supprimée: $orderId');
    } catch (e) {
      debugPrint('❌ Erreur suppression commande: $e');
      rethrow;
    }
  }

  /// Sauvegarder une commande localement
  Future<void> _saveOrderLocally(Invoice order) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getString(_ordersKey) ?? '{}';
      final orders = Map<String, dynamic>.from(jsonDecode(ordersJson));

      orders[order.id] = order.toJson();

      await prefs.setString(_ordersKey, jsonEncode(orders));
    } catch (e) {
      debugPrint('❌ Erreur sauvegarde locale commande: $e');
      rethrow;
    }
  }

  /// Récupérer une commande localement
  Future<Invoice?> _getOrderLocally(String orderId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getString(_ordersKey) ?? '{}';
      final orders = Map<String, dynamic>.from(jsonDecode(ordersJson));

      final orderData = orders[orderId];
      if (orderData != null) {
        return Invoice.fromJson(Map<String, dynamic>.from(orderData));
      }

      return null;
    } catch (e) {
      debugPrint('❌ Erreur récupération locale commande: $e');
      return null;
    }
  }

  /// Récupérer toutes les commandes localement
  Future<List<Invoice>> _getAllOrdersLocally() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getString(_ordersKey) ?? '{}';
      final orders = Map<String, dynamic>.from(jsonDecode(ordersJson));

      return orders.values
          .map((data) => Invoice.fromJson(Map<String, dynamic>.from(data)))
          .toList();
    } catch (e) {
      debugPrint('❌ Erreur récupération locale commandes: $e');
      return [];
    }
  }

  /// Supprimer une commande localement
  Future<void> _deleteOrderLocally(String orderId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getString(_ordersKey) ?? '{}';
      final orders = Map<String, dynamic>.from(jsonDecode(ordersJson));

      orders.remove(orderId);

      await prefs.setString(_ordersKey, jsonEncode(orders));
    } catch (e) {
      debugPrint('❌ Erreur suppression locale commande: $e');
      rethrow;
    }
  }

  /// Sauvegarder une photo localement
  Future<void> _savePhotoLocally(OrderPhoto photo) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final photosJson = prefs.getString(_photosKey) ?? '{}';
      final photos = Map<String, dynamic>.from(jsonDecode(photosJson));

      photos[photo.id] = photo.toJson();

      await prefs.setString(_photosKey, jsonEncode(photos));
    } catch (e) {
      debugPrint('❌ Erreur sauvegarde locale photo: $e');
      rethrow;
    }
  }

  /// Récupérer une photo localement
  Future<OrderPhoto?> _getPhotoLocally(String photoId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final photosJson = prefs.getString(_photosKey) ?? '{}';
      final photos = Map<String, dynamic>.from(jsonDecode(photosJson));

      final photoData = photos[photoId];
      if (photoData != null) {
        return OrderPhoto.fromJson(Map<String, dynamic>.from(photoData));
      }

      return null;
    } catch (e) {
      debugPrint('❌ Erreur récupération locale photo: $e');
      return null;
    }
  }

  /// Supprimer une photo
  Future<void> _deletePhoto(OrderPhoto photo) async {
    try {
      // Supprimer le fichier image
      final file = File(photo.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Supprimer des préférences
      final prefs = await SharedPreferences.getInstance();
      final photosJson = prefs.getString(_photosKey) ?? '{}';
      final photos = Map<String, dynamic>.from(jsonDecode(photosJson));

      photos.remove(photo.id);

      await prefs.setString(_photosKey, jsonEncode(photos));
    } catch (e) {
      debugPrint('❌ Erreur suppression photo: $e');
      rethrow;
    }
  }
}
