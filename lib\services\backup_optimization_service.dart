import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/product.dart';
import '../models/category.dart' as model;
import '../models/invoice.dart';
import '../models/task.dart';
import '../models/colis.dart';
import 'inventory_service.dart';
import 'invoice_service.dart';
import 'task_service.dart';
import 'colis_service.dart';
import 'data_change_notifier.dart';

/// Service pour optimiser les sauvegardes et améliorer les performances
class BackupOptimizationService {
  static const String _backupVersion = '2.1.0';

  /// Créer une sauvegarde optimisée avec barre de progression
  static Future<Map<String, dynamic>> createOptimizedBackup({
    Function(double progress, String operation)? onProgress,
  }) async {
    try {
      debugPrint('📦 Création de sauvegarde optimisée...');
      final stopwatch = Stopwatch()..start();

      onProgress?.call(0.0, 'Initialisation...');

      // Collecter les données en parallèle pour améliorer les performances
      onProgress?.call(0.1, 'Collecte des données...');

      final results = await Future.wait([
        InventoryService.instance.getProducts(),
        InventoryService.instance.getCategories(),
        InvoiceService.loadInvoices(),
        TaskService.instance.getTasks(),
        ColisService.instance.getAllColis(),
      ]);

      onProgress?.call(0.4, 'Optimisation des données...');

      final products = results[0] as List<Product>;
      final categories = results[1] as List<model.Category>;
      final invoices = results[2] as List<Invoice>;
      final tasks = results[3] as List<Task>;
      final colis = results[4] as List<Colis>;

      // Optimiser les données pour réduire la taille
      final optimizedData = {
        'products': _optimizeProducts(products),
        'categories': _optimizeCategories(categories),
        'invoices': _optimizeInvoices(invoices),
        'tasks': _optimizeTasks(tasks),
        'colis': _optimizeColis(colis),
      };

      onProgress?.call(0.8, 'Finalisation...');

      final backup = {
        'version': _backupVersion,
        'timestamp': DateTime.now().toIso8601String(),
        'optimized': true,
        'data': optimizedData,
        'stats': {
          'totalProducts': products.length,
          'totalCategories': categories.length,
          'totalInvoices': invoices.length,
          'totalTasks': tasks.length,
          'totalColis': colis.length,
        },
      };

      stopwatch.stop();
      onProgress?.call(1.0, 'Sauvegarde terminée');

      final jsonString = jsonEncode(backup);
      debugPrint(
        '✅ Sauvegarde optimisée créée en ${stopwatch.elapsedMilliseconds}ms',
      );
      debugPrint('📊 Taille: ${jsonString.length} caractères');

      return backup;
    } catch (e) {
      debugPrint('❌ Erreur lors de la création de sauvegarde optimisée: $e');
      rethrow;
    }
  }

  /// Optimiser les produits en supprimant les champs vides
  static List<Map<String, dynamic>> _optimizeProducts(List<Product> products) {
    return products.map((product) {
      final json = product.toJson();
      // Supprimer les champs vides pour réduire la taille
      json.removeWhere(
        (key, value) => value == null || (value is String && value.isEmpty),
      );
      return json;
    }).toList();
  }

  /// Optimiser les catégories
  static List<Map<String, dynamic>> _optimizeCategories(
    List<model.Category> categories,
  ) {
    return categories.map((category) {
      final json = category.toJson();
      json.removeWhere(
        (key, value) => value == null || (value is String && value.isEmpty),
      );
      return json;
    }).toList();
  }

  /// Optimiser les factures
  static List<Map<String, dynamic>> _optimizeInvoices(List<Invoice> invoices) {
    return invoices.map((invoice) {
      final json = invoice.toJson();
      // Supprimer les champs vides et les images trop volumineuses
      json.removeWhere(
        (key, value) =>
            value == null ||
            (value is String && value.isEmpty) ||
            (value is List && value.isEmpty),
      );
      return json;
    }).toList();
  }

  /// Optimiser les tâches
  static List<Map<String, dynamic>> _optimizeTasks(List<Task> tasks) {
    return tasks.map((task) {
      final json = task.toJson();
      json.removeWhere(
        (key, value) => value == null || (value is String && value.isEmpty),
      );
      return json;
    }).toList();
  }

  /// Optimiser les colis
  static List<Map<String, dynamic>> _optimizeColis(List<Colis> colis) {
    return colis.map((c) {
      final json = c.toJson();
      json.removeWhere(
        (key, value) => value == null || (value is String && value.isEmpty),
      );
      return json;
    }).toList();
  }

  /// Restaurer depuis une sauvegarde optimisée
  static Future<void> restoreFromOptimizedBackup(
    Map<String, dynamic> backup, {
    Function(double progress, String operation)? onProgress,
  }) async {
    try {
      debugPrint('📂 Restauration depuis sauvegarde optimisée...');
      final stopwatch = Stopwatch()..start();

      onProgress?.call(0.0, 'Vérification de la sauvegarde...');

      final data = backup['data'] as Map<String, dynamic>;

      // Restaurer en parallèle pour améliorer les performances
      onProgress?.call(0.2, 'Restauration des données...');

      await Future.wait([
        _restoreProducts(data['products'] as List<dynamic>? ?? []),
        _restoreCategories(data['categories'] as List<dynamic>? ?? []),
        _restoreInvoices(data['invoices'] as List<dynamic>? ?? []),
        _restoreTasks(data['tasks'] as List<dynamic>? ?? []),
        _restoreColis(data['colis'] as List<dynamic>? ?? []),
      ]);

      onProgress?.call(0.9, 'Finalisation...');

      // Notifier les changements
      DataChangeNotifier.instance.notifyAllDataChanged();

      stopwatch.stop();
      onProgress?.call(1.0, 'Restauration terminée');
      debugPrint(
        '✅ Restauration optimisée terminée en ${stopwatch.elapsedMilliseconds}ms',
      );
    } catch (e) {
      debugPrint('❌ Erreur lors de la restauration optimisée: $e');
      rethrow;
    }
  }

  /// Restaurer les produits
  static Future<void> _restoreProducts(List<dynamic> productsData) async {
    if (productsData.isEmpty) return;

    final products =
        productsData
            .map((json) => Product.fromJson(json as Map<String, dynamic>))
            .toList();

    final prefs = await SharedPreferences.getInstance();
    final productsJson = jsonEncode(products.map((p) => p.toJson()).toList());
    await prefs.setString('inventory_products', productsJson);

    debugPrint('✅ ${products.length} produits restaurés');
  }

  /// Restaurer les catégories
  static Future<void> _restoreCategories(List<dynamic> categoriesData) async {
    if (categoriesData.isEmpty) return;

    final categories =
        categoriesData
            .map(
              (json) => model.Category.fromJson(json as Map<String, dynamic>),
            )
            .toList();

    final prefs = await SharedPreferences.getInstance();
    final categoriesJson = jsonEncode(
      categories.map((c) => c.toJson()).toList(),
    );
    await prefs.setString('inventory_categories', categoriesJson);

    debugPrint('✅ ${categories.length} catégories restaurées');
  }

  /// Restaurer les factures
  static Future<void> _restoreInvoices(List<dynamic> invoicesData) async {
    if (invoicesData.isEmpty) return;

    final invoices =
        invoicesData
            .map((json) => Invoice.fromJson(json as Map<String, dynamic>))
            .toList();

    await InvoiceService.saveInvoices(invoices);
    debugPrint('✅ ${invoices.length} factures restaurées');
  }

  /// Restaurer les tâches
  static Future<void> _restoreTasks(List<dynamic> tasksData) async {
    if (tasksData.isEmpty) return;

    final tasks =
        tasksData
            .map((json) => Task.fromJson(json as Map<String, dynamic>))
            .toList();

    final prefs = await SharedPreferences.getInstance();
    final tasksJson = jsonEncode(tasks.map((t) => t.toJson()).toList());
    await prefs.setString('tasks', tasksJson);

    debugPrint('✅ ${tasks.length} tâches restaurées');
  }

  /// Restaurer les colis
  static Future<void> _restoreColis(List<dynamic> colisData) async {
    if (colisData.isEmpty) return;

    final colis =
        colisData
            .map((json) => Colis.fromJson(json as Map<String, dynamic>))
            .toList();

    await ColisService.instance.saveColis(colis);
    debugPrint('✅ ${colis.length} colis restaurés');
  }

  /// Estimer la taille de la sauvegarde
  static Future<Map<String, int>> estimateBackupSize() async {
    try {
      final results = await Future.wait([
        InventoryService.instance.getProducts(),
        InventoryService.instance.getCategories(),
        InvoiceService.loadInvoices(),
        TaskService.instance.getTasks(),
        ColisService.instance.getAllColis(),
      ]);

      final products = results[0] as List<Product>;
      final categories = results[1] as List<model.Category>;
      final invoices = results[2] as List<Invoice>;
      final tasks = results[3] as List<Task>;
      final colis = results[4] as List<Colis>;

      // Estimation approximative en caractères JSON
      final estimatedSize = {
        'products': products.length * 200, // ~200 caractères par produit
        'categories': categories.length * 80, // ~80 caractères par catégorie
        'invoices': invoices.length * 500, // ~500 caractères par facture
        'tasks': tasks.length * 150, // ~150 caractères par tâche
        'colis': colis.length * 300, // ~300 caractères par colis
      };

      final totalEstimated = estimatedSize.values.reduce((a, b) => a + b);
      estimatedSize['total'] = totalEstimated;

      return estimatedSize;
    } catch (e) {
      debugPrint('❌ Erreur lors de l\'estimation: $e');
      return {
        'products': 0,
        'categories': 0,
        'invoices': 0,
        'tasks': 0,
        'colis': 0,
        'total': 0,
      };
    }
  }

  /// Vérifier si une sauvegarde est optimisée
  static bool isOptimizedBackup(Map<String, dynamic> backup) {
    return backup['optimized'] == true;
  }
}
