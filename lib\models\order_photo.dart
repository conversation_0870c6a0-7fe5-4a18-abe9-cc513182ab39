import 'package:uuid/uuid.dart';

/// Modèle pour les photos de commandes avec métadonnées et données OCR
class OrderPhoto {
  final String id;
  final String filePath;
  final String? title;
  final String? description;
  final String? groupId; // Pour grouper les photos par sélection
  final String? groupName; // Nom du groupe
  final Map<String, String> ocrData; // Données OCR extraites (prix, dates, noms)
  final DateTime createdAt;
  final DateTime? updatedAt;

  OrderPhoto({
    String? id,
    required this.filePath,
    this.title,
    this.description,
    this.groupId,
    this.groupName,
    this.ocrData = const {},
    DateTime? createdAt,
    this.updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  /// Convertir en Map pour la sérialisation JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filePath': filePath,
      'title': title,
      'description': description,
      'groupId': groupId,
      'groupName': groupName,
      'ocrData': ocrData,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Créer depuis un Map JSON
  factory OrderPhoto.fromJson(Map<String, dynamic> json) {
    return OrderPhoto(
      id: json['id'] as String,
      filePath: json['filePath'] as String,
      title: json['title'] as String?,
      description: json['description'] as String?,
      groupId: json['groupId'] as String?,
      groupName: json['groupName'] as String?,
      ocrData: Map<String, String>.from(json['ocrData'] as Map? ?? {}),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'] as String) 
          : null,
    );
  }

  /// Créer une copie avec des modifications
  OrderPhoto copyWith({
    String? id,
    String? filePath,
    String? title,
    String? description,
    String? groupId,
    String? groupName,
    Map<String, String>? ocrData,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return OrderPhoto(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      title: title ?? this.title,
      description: description ?? this.description,
      groupId: groupId ?? this.groupId,
      groupName: groupName ?? this.groupName,
      ocrData: ocrData ?? this.ocrData,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Vérifier si la photo a des données OCR
  bool get hasOcrData => ocrData.isNotEmpty;

  /// Obtenir le nom d'affichage de la photo
  String get displayName => title ?? 'Photo ${id.substring(0, 8)}';

  /// Vérifier si la photo appartient à un groupe
  bool get isGrouped => groupId != null;

  @override
  String toString() {
    return 'OrderPhoto(id: $id, filePath: $filePath, title: $title, groupId: $groupId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderPhoto && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Modèle pour grouper les photos
class PhotoGroup {
  final String id;
  final String name;
  final String? description;
  final List<String> photoIds;
  final DateTime createdAt;

  PhotoGroup({
    String? id,
    required this.name,
    this.description,
    this.photoIds = const [],
    DateTime? createdAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now();

  /// Convertir en Map pour la sérialisation JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'photoIds': photoIds,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  /// Créer depuis un Map JSON
  factory PhotoGroup.fromJson(Map<String, dynamic> json) {
    return PhotoGroup(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      photoIds: List<String>.from(json['photoIds'] as List? ?? []),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  /// Créer une copie avec des modifications
  PhotoGroup copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? photoIds,
    DateTime? createdAt,
  }) {
    return PhotoGroup(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      photoIds: photoIds ?? this.photoIds,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'PhotoGroup(id: $id, name: $name, photoCount: ${photoIds.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhotoGroup && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
