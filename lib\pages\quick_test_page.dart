import 'package:flutter/material.dart';
import '../services/invoice_service.dart';
import '../services/inventory_service.dart';
import '../models/invoice.dart';
import '../models/product.dart';

class QuickTestPage extends StatefulWidget {
  const QuickTestPage({super.key});

  @override
  State<QuickTestPage> createState() => _QuickTestPageState();
}

class _QuickTestPageState extends State<QuickTestPage> {
  final List<String> _logs = [];
  bool _isRunning = false;

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> _testInvoiceFlow() async {
    setState(() {
      _isRunning = true;
      _logs.clear();
    });

    try {
      _addLog('🚀 Test du flux complet factures');

      // 1. Créer une facture
      final testInvoice = Invoice(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        clientName: 'Client Test',
        clientNumber: '123456789',
        products: 'Produit Test',
        items: [],
        deliveryLocation: 'Test Location',
        deliveryPrice: 10.0,
        advance: 0.0,
        subtotal: 100.0,
        total: 110.0,
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
        type: InvoiceType.normale,
      );

      _addLog('📝 Facture créée');

      // 2. Sauvegarder
      final invoiceService = InvoiceService();
      final savedInvoice = await invoiceService.addInvoice(testInvoice);
      _addLog('✅ Facture sauvegardée: ${savedInvoice.id}');

      // 3. Charger toutes les factures
      final invoices = await InvoiceService.loadInvoices();
      _addLog('📊 ${invoices.length} factures chargées');

      // 4. Vérifier que notre facture est présente
      final found = invoices.any((inv) => inv.id == savedInvoice.id);
      if (found) {
        _addLog('🎯 Facture trouvée dans la liste !');
      } else {
        _addLog('❌ Facture NON trouvée dans la liste');
      }

      _addLog('🎉 Test factures terminé avec succès');
    } catch (e) {
      _addLog('❌ Erreur test factures: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _testProductFlow() async {
    setState(() {
      _isRunning = true;
      _logs.clear();
    });

    try {
      _addLog('🚀 Test du flux complet produits');

      // 1. Charger les catégories
      final inventoryService = InventoryService.instance;
      final categories = await inventoryService.getCategories();
      _addLog('📂 ${categories.length} catégories chargées');

      if (categories.isEmpty) {
        _addLog('❌ Aucune catégorie trouvée');
        return;
      }

      // 2. Créer un produit
      final testProduct = Product(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        name: 'Produit Test',
        price: 50.0,
        quantity: 10,
        categoryId: categories.first.id,
        description: 'Description test',
      );

      _addLog('📝 Produit créé');

      // 3. Sauvegarder
      final savedProduct = await inventoryService.addProduct(testProduct);
      _addLog('✅ Produit sauvegardé: ${savedProduct.id}');

      // 4. Charger tous les produits
      final products = await inventoryService.getProducts();
      _addLog('📦 ${products.length} produits chargés');

      // 5. Vérifier que notre produit est présent
      final found = products.any((prod) => prod.id == savedProduct.id);
      if (found) {
        _addLog('🎯 Produit trouvé dans la liste !');
      } else {
        _addLog('❌ Produit NON trouvé dans la liste');
      }

      _addLog('🎉 Test produits terminé avec succès');
    } catch (e) {
      _addLog('❌ Erreur test produits: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _testCompleteFlow() async {
    setState(() {
      _isRunning = true;
      _logs.clear();
    });

    try {
      _addLog('🚀 Test complet de l\'application');

      // Test des produits d'abord
      await _testProductFlowSilent();

      // Puis test des factures
      await _testInvoiceFlowSilent();

      _addLog('🎉 Tous les tests terminés avec succès !');
    } catch (e) {
      _addLog('❌ Erreur test complet: $e');
    } finally {
      setState(() {
        _isRunning = false;
      });
    }
  }

  Future<void> _testProductFlowSilent() async {
    final inventoryService = InventoryService.instance;
    final categories = await inventoryService.getCategories();
    _addLog('📂 ${categories.length} catégories OK');

    if (categories.isNotEmpty) {
      final testProduct = Product(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        name: 'Produit Test',
        price: 50.0,
        quantity: 10,
        categoryId: categories.first.id,
        description: 'Description test',
      );

      await inventoryService.addProduct(testProduct);
      final products = await inventoryService.getProducts();
      _addLog('📦 ${products.length} produits OK');
    }
  }

  Future<void> _testInvoiceFlowSilent() async {
    final testInvoice = Invoice(
      id: 'test_${DateTime.now().millisecondsSinceEpoch}',
      clientName: 'Client Test',
      clientNumber: '123456789',
      products: 'Produit Test',
      items: [],
      deliveryLocation: 'Test Location',
      deliveryPrice: 10.0,
      advance: 0.0,
      subtotal: 100.0,
      total: 110.0,
      status: InvoiceStatus.enAttente,
      createdAt: DateTime.now(),
      type: InvoiceType.normale,
    );

    final invoiceService = InvoiceService();
    await invoiceService.addInvoice(testInvoice);
    final invoices = await InvoiceService.loadInvoices();
    _addLog('📊 ${invoices.length} factures OK');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Rapide'),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Boutons de test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tests Rapides',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isRunning ? null : _testInvoiceFlow,
                            icon: const Icon(Icons.receipt),
                            label: const Text('Test Factures'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isRunning ? null : _testProductFlow,
                            icon: const Icon(Icons.inventory),
                            label: const Text('Test Produits'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isRunning ? null : _testCompleteFlow,
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('Test Complet'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange[700],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Logs
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Résultats des Tests',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const Spacer(),
                          if (_isRunning)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child:
                              _logs.isEmpty
                                  ? const Text(
                                    'Aucun test lancé. Cliquez sur un bouton pour commencer.',
                                    style: TextStyle(
                                      fontStyle: FontStyle.italic,
                                    ),
                                  )
                                  : ListView.builder(
                                    itemCount: _logs.length,
                                    itemBuilder: (context, index) {
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 2,
                                        ),
                                        child: Text(
                                          _logs[index],
                                          style: const TextStyle(
                                            fontFamily: 'monospace',
                                            fontSize: 12,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
