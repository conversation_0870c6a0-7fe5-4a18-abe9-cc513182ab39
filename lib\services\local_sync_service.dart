import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:network_info_plus/network_info_plus.dart';
import 'backup_service.dart';

/// Classe pour représenter le résultat d'une opération de synchronisation
class SyncResult {
  final bool success;
  final String? error;
  final String? details;
  final Map<String, dynamic>? data;

  SyncResult({required this.success, this.error, this.details, this.data});

  @override
  String toString() {
    if (success) {
      return 'SyncResult(success: true${details != null ? ', details: $details' : ''})';
    } else {
      return 'SyncResult(success: false, error: $error${details != null ? ', details: $details' : ''})';
    }
  }
}

class LocalSyncService {
  static final LocalSyncService _instance = LocalSyncService._internal();
  factory LocalSyncService() => _instance;
  LocalSyncService._internal();

  static const int _serverPort = 8080;
  static const String _syncEndpoint = '/sync';
  static const String _discoveryEndpoint = '/discover';

  HttpServer? _server;
  bool _isServerRunning = false;
  String? _localIP;
  final List<String> _discoveredDevices = [];

  /// Démarre le serveur de synchronisation sur cet appareil
  Future<bool> startSyncServer() async {
    try {
      _localIP = await _getLocalIP();
      if (_localIP == null) {
        debugPrint('❌ Impossible d\'obtenir l\'adresse IP locale - Vérifiez votre connexion réseau');
        return false;
      }

      _server = await HttpServer.bind(InternetAddress.anyIPv4, _serverPort);
      _isServerRunning = true;

      debugPrint(
        'Serveur de synchronisation démarré sur $_localIP:$_serverPort',
      );

      _server!.listen((HttpRequest request) async {
        await _handleRequest(request);
      });

      return true;
    } catch (e) {
      debugPrint('❌ Erreur critique lors du démarrage du serveur de synchronisation: $e');
      debugPrint('💡 Conseil: Vérifiez que le port $_serverPort n\'est pas déjà utilisé par une autre application');
      return false;
    }
  }

  /// Arrête le serveur de synchronisation
  Future<void> stopSyncServer() async {
    if (_server != null) {
      await _server!.close();
      _server = null;
      _isServerRunning = false;
      debugPrint('🛑 Serveur de synchronisation arrêté proprement');
    }
  }

  /// Gère les requêtes HTTP entrantes
  Future<void> _handleRequest(HttpRequest request) async {
    final requestId = DateTime.now().millisecondsSinceEpoch.toString();
    debugPrint('🔄 [REQ-$requestId] Nouvelle requête: ${request.method} ${request.uri.path} depuis ${request.connectionInfo?.remoteAddress}');
    
    // Ajouter les en-têtes CORS pour permettre les requêtes cross-origin
    request.response.headers.add('Access-Control-Allow-Origin', '*');
    request.response.headers.add(
      'Access-Control-Allow-Methods',
      'GET, POST, OPTIONS',
    );
    request.response.headers.add(
      'Access-Control-Allow-Headers',
      'Content-Type',
    );

    if (request.method == 'OPTIONS') {
      debugPrint('✅ [REQ-$requestId] Réponse CORS OPTIONS');
      request.response.statusCode = 200;
      await request.response.close();
      return;
    }

    try {
      switch (request.uri.path) {
        case _discoveryEndpoint:
          debugPrint('🔍 [REQ-$requestId] Traitement découverte');
          await _handleDiscovery(request);
          break;
        case _syncEndpoint:
          if (request.method == 'GET') {
            debugPrint('📤 [REQ-$requestId] Traitement demande de synchronisation (GET)');
            await _handleSyncRequest(request);
          } else if (request.method == 'POST') {
            debugPrint('📥 [REQ-$requestId] Traitement réception de synchronisation (POST)');
            await _handleSyncReceive(request);
          } else {
            debugPrint('❌ [REQ-$requestId] Méthode HTTP non supportée: ${request.method}');
            request.response.statusCode = 405;
            request.response.headers.contentType = ContentType.json;
            request.response.write(
              jsonEncode({
                'status': 'error',
                'message': 'Méthode non autorisée',
                'details': 'Seules les méthodes GET et POST sont supportées pour cet endpoint.',
                'error_code': 'METHOD_NOT_ALLOWED',
              }),
            );
          }
          break;
        default:
          debugPrint('❌ [REQ-$requestId] Endpoint non trouvé: ${request.uri.path}');
          request.response.statusCode = 404;
          request.response.headers.contentType = ContentType.json;
          request.response.write(
            jsonEncode({
              'status': 'error',
              'message': 'Endpoint non trouvé',
              'details': 'L\'URL demandée n\'existe pas sur ce serveur de synchronisation.',
              'available_endpoints': [_discoveryEndpoint, _syncEndpoint],
              'error_code': 'ENDPOINT_NOT_FOUND',
            }),
          );
      }
      debugPrint('✅ [REQ-$requestId] Requête traitée avec succès');
    } catch (e, stackTrace) {
      debugPrint('❌ [REQ-$requestId] Erreur critique lors du traitement de la requête: $e');
      debugPrint('📍 [REQ-$requestId] Stack trace: $stackTrace');
      
      // Analyser le type d'erreur pour un diagnostic plus précis
      String errorType = 'UNKNOWN_ERROR';
      String userMessage = 'Une erreur inattendue s\'est produite. Veuillez réessayer.';
      
      if (e.toString().contains('SocketException')) {
        errorType = 'NETWORK_ERROR';
        userMessage = 'Erreur de connexion réseau. Vérifiez votre connexion.';
      } else if (e.toString().contains('TimeoutException')) {
        errorType = 'TIMEOUT_ERROR';
        userMessage = 'L\'opération a pris trop de temps. Réessayez plus tard.';
      } else if (e.toString().contains('FormatException')) {
        errorType = 'DATA_FORMAT_ERROR';
        userMessage = 'Format de données invalide. Vérifiez les données envoyées.';
      } else if (e.toString().contains('Permission')) {
        errorType = 'PERMISSION_ERROR';
        userMessage = 'Permissions insuffisantes pour effectuer cette opération.';
      }
      
      request.response.statusCode = 500;
      request.response.headers.contentType = ContentType.json;
      request.response.write(
        jsonEncode({
          'status': 'error',
          'message': 'Erreur serveur lors du traitement de la requête',
          'details': userMessage,
          'error_code': errorType,
          'error_type': e.runtimeType.toString(),
          'timestamp': DateTime.now().toIso8601String(),
          'request_id': requestId,
        }),
      );
    }

    await request.response.close();
    debugPrint('🏁 [REQ-$requestId] Connexion fermée');
  }

  /// Gère la découverte d'appareils
  Future<void> _handleDiscovery(HttpRequest request) async {
    try {
      debugPrint('🔍 Demande de découverte reçue depuis ${request.connectionInfo?.remoteAddress}');
      
      // Récupérer les informations de l'appareil avec gestion d'erreur
      String deviceName;
      try {
        deviceName = await _getDeviceName().timeout(const Duration(seconds: 5));
      } catch (e) {
           debugPrint('⚠️ Erreur lors de la récupération du nom d\'appareil: $e');
        deviceName = 'Appareil inconnu';
      }

      final deviceInfo = {
        'device_name': deviceName,
        'ip': _localIP ?? 'IP inconnue',
        'port': _serverPort,
        'timestamp': DateTime.now().toIso8601String(),
        'app_version': '1.0.0',
        'server_status': _isServerRunning ? 'active' : 'inactive',
        'sync_endpoint': _syncEndpoint,
      };

      // Encoder en JSON avec gestion d'erreur
      String jsonResponse;
      try {
        jsonResponse = jsonEncode(deviceInfo);
      } catch (jsonError) {
        debugPrint('❌ Erreur d\'encodage JSON pour la découverte: $jsonError');
        request.response.statusCode = 500;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Erreur d\'encodage des informations d\'appareil',
            'details': jsonError.toString(),
          }),
        );
        return;
      }

      debugPrint('✅ Informations d\'appareil envoyées: $deviceName ($_localIP)');
      
      request.response.headers.contentType = ContentType.json;
      request.response.headers.add('Content-Length', jsonResponse.length.toString());
      request.response.write(jsonResponse);
      
    } catch (e) {
      debugPrint('❌ Erreur inattendue lors de la découverte: $e');
      request.response.statusCode = 500;
      request.response.headers.contentType = ContentType.json;
      
      try {
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Erreur serveur lors de la découverte',
            'details': e.toString(),
          }),
        );
      } catch (jsonError) {
        // Si même l'encodage JSON échoue, envoyer une réponse simple
        request.response.write('Erreur serveur: impossible de fournir les informations d\'appareil');
      }
    }
  }

  /// Gère les demandes de synchronisation (envoi des données)
  Future<void> _handleSyncRequest(HttpRequest request) async {
    Map<String, dynamic>? backupData;
    
    try {
      debugPrint('📤 Demande de synchronisation reçue depuis ${request.connectionInfo?.remoteAddress}');
      
      // Créer une sauvegarde complète avec timeout et diagnostic détaillé
      try {
        debugPrint('📦 Début de la création de sauvegarde...');
        final stopwatch = Stopwatch()..start();
        
        backupData = await BackupService.createBackup()
            .timeout(const Duration(seconds: 45)); // Augmenté à 45s pour plus de robustesse
            
        stopwatch.stop();
        debugPrint('✅ Sauvegarde créée en ${stopwatch.elapsedMilliseconds}ms');
        
      } on TimeoutException {
        debugPrint('⏰ Timeout lors de la création de la sauvegarde (45 secondes dépassées)');
        request.response.statusCode = 503;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Timeout lors de la création de la sauvegarde',
            'details': 'La création de la sauvegarde a pris plus de 45 secondes. Cela peut indiquer un problème avec la base de données locale ou un volume de données trop important.',
            'error_code': 'BACKUP_TIMEOUT',
            'suggested_action': 'Essayez de redémarrer l\'application ou de vider le cache.',
          }),
        );
        return;
      } catch (backupError, stackTrace) {
        debugPrint('❌ Erreur lors de la création de la sauvegarde: $backupError');
        debugPrint('📍 Stack trace sauvegarde: $stackTrace');
        
        // Analyser le type d'erreur de sauvegarde
        String errorCode = 'BACKUP_CREATION_ERROR';
        String details = 'Impossible de créer les données de synchronisation';
        String suggestedAction = 'Vérifiez l\'espace de stockage disponible et redémarrez l\'application.';
        
        if (backupError.toString().contains('Permission')) {
          errorCode = 'BACKUP_PERMISSION_ERROR';
          details = 'Permissions insuffisantes pour accéder aux données';
          suggestedAction = 'Accordez les permissions de stockage à l\'application.';
        } else if (backupError.toString().contains('Storage') || backupError.toString().contains('space')) {
          errorCode = 'BACKUP_STORAGE_ERROR';
          details = 'Espace de stockage insuffisant';
          suggestedAction = 'Libérez de l\'espace de stockage sur votre appareil.';
        } else if (backupError.toString().contains('Database') || backupError.toString().contains('SQL')) {
          errorCode = 'BACKUP_DATABASE_ERROR';
          details = 'Erreur d\'accès à la base de données locale';
          suggestedAction = 'Redémarrez l\'application. Si le problème persiste, contactez le support.';
        }
        
        request.response.statusCode = 500;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Erreur lors de la création de la sauvegarde',
            'details': '$details: $backupError',
            'error_code': errorCode,
            'error_type': backupError.runtimeType.toString(),
            'suggested_action': suggestedAction,
          }),
        );
        return;
      }

      // Valider que les données de sauvegarde ne sont pas nulles
      if (backupData.isEmpty) {
        debugPrint('❌ Données de sauvegarde nulles');
        request.response.statusCode = 500;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Données de sauvegarde invalides',
            'details': 'La sauvegarde générée est nulle',
          }),
        );
        return;
      }

      // Valider l'intégrité des données avant envoi
      final validationResult = await _validateBackupData(backupData);
      if (!validationResult.isValid) {
        debugPrint('❌ Validation des données échouée: ${validationResult.errors.join(', ')}');
        request.response.statusCode = 500;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Données de sauvegarde corrompues',
            'details': 'Validation échouée: ${validationResult.errors.join(', ')}',
            'validation_errors': validationResult.errors,
          }),
        );
        return;
      }

      debugPrint('✅ Validation des données réussie: ${validationResult.summary}');

      // Préparer la réponse avec métadonnées
      final responseData = {
        'status': 'success',
        'data': backupData,
        'timestamp': DateTime.now().toIso8601String(),
        'device_name': await _getDeviceName(),
        'app_version': '1.0.0',
      };

      // Encoder en JSON avec gestion d'erreur
      String jsonResponse;
      try {
        jsonResponse = jsonEncode(responseData);
      } catch (jsonError) {
        debugPrint('❌ Erreur d\'encodage JSON: $jsonError');
        request.response.statusCode = 500;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Erreur d\'encodage des données',
            'details': 'Impossible de sérialiser les données de synchronisation: $jsonError',
          }),
        );
        return;
      }

      debugPrint('✅ Envoi de ${jsonResponse.length} caractères de données de synchronisation');
      
      // Envoyer la réponse
      request.response.headers.contentType = ContentType.json;
      request.response.headers.add('Content-Length', jsonResponse.length.toString());
      request.response.write(jsonResponse);
      
    } catch (e) {
      debugPrint('❌ Erreur inattendue lors de l\'envoi: $e');
      request.response.statusCode = 500;
      request.response.headers.contentType = ContentType.json;
      
      try {
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Erreur serveur inattendue',
            'details': e.toString(),
            'backup_data_available': backupData != null,
          }),
        );
      } catch (jsonError) {
        // Si même l'encodage JSON échoue, envoyer une réponse simple
        request.response.write('Erreur serveur: impossible de traiter la requête');
      }
    }
  }

  /// Gère la réception de données de synchronisation
  Future<void> _handleSyncReceive(HttpRequest request) async {
    String? rawBody;
    
    try {
      // Vérifier la taille du contenu
      final contentLength = request.contentLength;
      if (contentLength > 50 * 1024 * 1024) { // Limite à 50MB
        request.response.statusCode = 413;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Données trop volumineuses',
            'details': 'La taille maximale autorisée est de 50MB',
          }),
        );
        return;
      }

      // Lire le corps de la requête avec timeout
      try {
        rawBody = await utf8.decoder
            .bind(request)
            .timeout(const Duration(seconds: 30))
            .join();
      } on TimeoutException {
        request.response.statusCode = 408;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Timeout lors de la réception des données',
            'details': 'La réception a pris plus de 30 secondes',
          }),
        );
        return;
      }

      // Valider que le corps n'est pas vide
      if (rawBody.isEmpty) {
        request.response.statusCode = 400;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Corps de requête vide',
            'details': 'Aucune donnée reçue pour la synchronisation',
          }),
        );
        return;
      }

      debugPrint('📥 Réception de ${rawBody.length} caractères de données de synchronisation');

      // Parser le JSON avec gestion d'erreur détaillée
      Map<String, dynamic> data;
      try {
        final jsonData = jsonDecode(rawBody);
        if (jsonData is! Map<String, dynamic>) {
          throw FormatException('Les données doivent être un objet JSON');
        }
        data = jsonData;
      } on FormatException catch (e) {
        debugPrint('❌ Erreur de format JSON: $e');
        request.response.statusCode = 400;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Format JSON invalide',
            'details': 'Erreur de parsing: ${e.message}',
          }),
        );
        return;
      } catch (e) {
        debugPrint('❌ Erreur de décodage JSON: $e');
        request.response.statusCode = 400;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Données JSON corrompues',
            'details': 'Impossible de décoder les données: $e',
          }),
        );
        return;
      }

      // Valider la structure des données
      if (!data.containsKey('data')) {
        request.response.statusCode = 400;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Structure de données invalide',
            'details': 'Le champ "data" est requis pour la synchronisation',
          }),
        );
        return;
      }

      final syncData = data['data'];
      if (syncData == null) {
        request.response.statusCode = 400;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Données de synchronisation manquantes',
            'details': 'Le champ "data" ne peut pas être null',
          }),
        );
        return;
      }

      // Informations sur la source (optionnel)
      final sourceDevice = data['source_device'] ?? 'Appareil inconnu';
      final timestamp = data['timestamp'] ?? DateTime.now().toIso8601String();
      debugPrint('📱 Synchronisation depuis: $sourceDevice à $timestamp');

      // Valider l'intégrité des données reçues avant restauration
      final validationResult = await _validateReceivedData(syncData);
      if (!validationResult.isValid) {
        debugPrint('❌ Validation des données reçues échouée: ${validationResult.errors.join(', ')}');
        request.response.statusCode = 400;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Données reçues invalides',
            'details': 'Validation échouée: ${validationResult.errors.join(', ')}',
            'validation_errors': validationResult.errors,
          }),
        );
        return;
      }

      debugPrint('✅ Validation des données reçues réussie: ${validationResult.summary}');

      // Restaurer les données reçues avec gestion d'erreur détaillée
      try {
        debugPrint('🔄 Début de la restauration des données...');
        final stopwatch = Stopwatch()..start();
        
        await BackupService.restoreFromBackup(syncData);
        
        stopwatch.stop();
        debugPrint('✅ Données restaurées avec succès en ${stopwatch.elapsedMilliseconds}ms');
        
      } catch (restoreError, stackTrace) {
        debugPrint('❌ Erreur lors de la restauration: $restoreError');
        debugPrint('📍 Stack trace restauration: $stackTrace');
        
        // Analyser le type d'erreur de restauration
        String errorCode = 'RESTORE_ERROR';
        String details = 'Impossible de restaurer les données reçues';
        String suggestedAction = 'Vérifiez l\'intégrité des données et réessayez.';
        
        if (restoreError.toString().contains('Version')) {
          errorCode = 'RESTORE_VERSION_ERROR';
          details = 'Version de sauvegarde incompatible';
          suggestedAction = 'Assurez-vous que les deux appareils utilisent la même version de l\'application.';
        } else if (restoreError.toString().contains('Permission')) {
          errorCode = 'RESTORE_PERMISSION_ERROR';
          details = 'Permissions insuffisantes pour écrire les données';
          suggestedAction = 'Accordez les permissions de stockage à l\'application.';
        } else if (restoreError.toString().contains('Storage') || restoreError.toString().contains('space')) {
          errorCode = 'RESTORE_STORAGE_ERROR';
          details = 'Espace de stockage insuffisant pour la restauration';
          suggestedAction = 'Libérez de l\'espace de stockage sur votre appareil.';
        } else if (restoreError.toString().contains('Database') || restoreError.toString().contains('SQL')) {
          errorCode = 'RESTORE_DATABASE_ERROR';
          details = 'Erreur d\'écriture dans la base de données locale';
          suggestedAction = 'Redémarrez l\'application et réessayez la synchronisation.';
        } else if (restoreError.toString().contains('Format') || restoreError.toString().contains('JSON')) {
          errorCode = 'RESTORE_FORMAT_ERROR';
          details = 'Format de données invalide ou corrompu';
          suggestedAction = 'Les données reçues sont corrompues. Réessayez depuis l\'appareil source.';
        }
        
        request.response.statusCode = 500;
        request.response.headers.contentType = ContentType.json;
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Erreur lors de la restauration des données',
            'details': '$details: $restoreError',
            'error_code': errorCode,
            'error_type': restoreError.runtimeType.toString(),
            'suggested_action': suggestedAction,
          }),
        );
        return;
      }

      // Réponse de succès
      request.response.headers.contentType = ContentType.json;
      request.response.write(
        jsonEncode({
          'status': 'success',
          'message': 'Synchronisation réussie',
          'timestamp': DateTime.now().toIso8601String(),
          'source_device': sourceDevice,
          'data_size': rawBody.length,
        }),
      );
    } catch (e) {
      debugPrint('❌ Erreur inattendue lors de la réception: $e');
      request.response.statusCode = 500;
      request.response.headers.contentType = ContentType.json;
      
      try {
        request.response.write(
          jsonEncode({
            'status': 'error',
            'message': 'Erreur serveur inattendue',
            'details': e.toString(),
            'data_received': rawBody?.length ?? 0,
          }),
        );
      } catch (jsonError) {
        // Si même l'encodage JSON échoue, envoyer une réponse simple
        request.response.write('Erreur serveur: impossible de traiter la requête');
      }
    }
  }

  /// Découvre les appareils disponibles sur le réseau local
  Future<List<Map<String, dynamic>>> discoverDevices() async {
    final devices = <Map<String, dynamic>>[];
    _discoveredDevices.clear();

    try {
      final localIP = await _getLocalIP();
      if (localIP == null) {
        debugPrint('❌ Impossible d\'obtenir l\'adresse IP locale');
        throw Exception('Adresse IP locale non disponible. Vérifiez votre connexion réseau.');
      }

      // Valider le format de l'IP
      final ipParts = localIP.split('.');
      if (ipParts.length != 4) {
        debugPrint('❌ Format d\'IP invalide: $localIP');
        throw Exception('Format d\'adresse IP invalide: $localIP');
      }

      final subnet = localIP.substring(0, localIP.lastIndexOf('.'));
      debugPrint('🔍 Scan du sous-réseau: $subnet.x');

      // Scanner les adresses IP du sous-réseau (de 1 à 254)
      final futures = <Future<void>>[];
      final semaphore = <Completer<void>>[];
      const maxConcurrent = 20; // Limiter les connexions simultanées
      
      for (int i = 1; i <= 254; i++) {
        final targetIP = '$subnet.$i';
        if (targetIP == localIP) continue; // Ignorer sa propre IP

        // Gérer la concurrence pour éviter de surcharger le réseau
        if (semaphore.length >= maxConcurrent) {
          await Future.any(semaphore.map((c) => c.future));
          semaphore.removeWhere((completer) => completer.isCompleted);
        }

        final completer = Completer<void>();
        final future = _checkDevice(targetIP, devices).whenComplete(() {
          if (!completer.isCompleted) completer.complete();
        });
        futures.add(future);
        semaphore.add(completer);
      }

      // Attendre toutes les vérifications avec un timeout global
      try {
        await Future.wait(futures).timeout(
          const Duration(seconds: 45), // Augmenter le timeout
          onTimeout: () {
            debugPrint('⏰ Timeout lors de la découverte d\'appareils (45s)');
            return <void>[];
          },
        );
      } catch (e) {
        debugPrint('⚠️ Erreur partielle lors de la découverte: $e');
        // Continuer même si certaines vérifications échouent
      }

      if (devices.isEmpty) {
        debugPrint('🔍 Aucun appareil trouvé sur le réseau');
        debugPrint('💡 Conseil: Assurez-vous que les autres appareils ont le service de synchronisation activé et sont connectés au même réseau WiFi');
      } else {
        debugPrint('✅ Découverte terminée: ${devices.length} appareil(s) trouvé(s)');
        for (var device in devices) {
          debugPrint('  📱 ${device['device_name']} (${device['ip']})');
        }
      }
      
      // Trier les appareils par IP pour un affichage cohérent
      devices.sort((a, b) {
        final ipA = a['ip'] as String? ?? '';
        final ipB = b['ip'] as String? ?? '';
        return ipA.compareTo(ipB);
      });
      
      return devices;
    } catch (e) {
      debugPrint('❌ Erreur critique lors de la découverte d\'appareils: $e');
      debugPrint('💡 Conseil: Vérifiez votre connexion réseau et réessayez la découverte');
      // Retourner la liste partielle des appareils trouvés
      return devices;
    }
  }

  /// Vérifie si un appareil répond sur l'IP donnée
  Future<void> _checkDevice(
    String targetIP,
    List<Map<String, dynamic>> devices,
  ) async {
    try {
      final response = await http
          .get(
            Uri.parse('http://$targetIP:$_serverPort$_discoveryEndpoint'),
            headers: {
              'Connection': 'close',
              'User-Agent': 'HCP-CRM-LocalSync/1.0',
              'Accept': 'application/json',
            },
          )
          .timeout(const Duration(seconds: 5)); // Augmenter le timeout

      if (response.statusCode == 200) {
        try {
          final deviceInfo = jsonDecode(response.body) as Map<String, dynamic>;
          
          // Valider les données reçues
          if (deviceInfo['device_name'] == null || deviceInfo['device_name'].toString().isEmpty) {
            debugPrint('⚠️ Appareil trouvé mais nom manquant: $targetIP');
            deviceInfo['device_name'] = 'Appareil inconnu';
          }
          
          deviceInfo['ip'] = targetIP; // S'assurer que l'IP est correcte
          deviceInfo['last_seen'] = DateTime.now().toIso8601String();
          deviceInfo['response_time'] = DateTime.now().millisecondsSinceEpoch;
          deviceInfo['status'] = 'online'; // Marquer l'appareil comme en ligne
          
          devices.add(deviceInfo);
          _discoveredDevices.add(targetIP);
          debugPrint('📱 Appareil trouvé: $targetIP - ${deviceInfo['device_name']} (En ligne)');
        } catch (jsonError) {
          debugPrint('⚠️ Erreur de parsing JSON pour $targetIP: $jsonError');
          // Ajouter l'appareil avec des informations minimales mais en ligne
          devices.add({
            'ip': targetIP,
            'device_name': 'Appareil (erreur de données)',
            'last_seen': DateTime.now().toIso8601String(),
            'status': 'online', // Même avec erreur de données, l'appareil répond
            'error': 'Données invalides',
          });
        }
      } else {
        debugPrint('🔍 Réponse non-200 de $targetIP: ${response.statusCode}');
      }
    } on TimeoutException {
      // Timeout silencieux - normal pour les IPs non utilisées
      debugPrint('⏰ Timeout pour $targetIP');
    } on SocketException {
      // Erreur réseau silencieuse - normal pour les IPs non utilisées
      debugPrint('🌐 Erreur réseau pour $targetIP');
    } catch (e) {
      // Autres erreurs - les logger pour le debug
      debugPrint('❌ Erreur inattendue pour $targetIP: $e');
    }
  }

  /// Récupère les informations d'un appareil spécifique
  Future<Map<String, dynamic>?> getDeviceInfo(String targetIP) async {
    if (targetIP.isEmpty) {
      debugPrint('❌ IP cible vide');
      return null;
    }

    try {
      debugPrint('🔍 Récupération des infos pour $targetIP');
      
      final response = await http
          .get(
            Uri.parse('http://$targetIP:$_serverPort$_discoveryEndpoint'),
            headers: {
              'Connection': 'close',
              'User-Agent': 'HCP-CRM-LocalSync/1.0',
              'Accept': 'application/json',
            },
          )
          .timeout(const Duration(seconds: 8));

      if (response.statusCode == 200) {
        try {
          final deviceInfo = jsonDecode(response.body) as Map<String, dynamic>;
          
          // Valider et enrichir les données
          deviceInfo['ip'] = targetIP;
          deviceInfo['last_contacted'] = DateTime.now().toIso8601String();
          deviceInfo['status'] = 'online';
          
          // Valider les champs requis
          if (deviceInfo['device_name'] == null) {
            deviceInfo['device_name'] = 'Appareil sans nom';
          }
          
          debugPrint('✅ Infos récupérées pour $targetIP: ${deviceInfo['device_name']}');
          return deviceInfo;
        } catch (jsonError) {
          debugPrint('❌ Erreur de parsing JSON pour $targetIP: $jsonError');
          return {
            'ip': targetIP,
            'device_name': 'Appareil (données corrompues)',
            'status': 'error',
            'error': 'Données JSON invalides',
            'last_contacted': DateTime.now().toIso8601String(),
          };
        }
      } else {
        debugPrint('❌ Code de statut non-200 pour $targetIP: ${response.statusCode}');
        return {
          'ip': targetIP,
          'device_name': 'Appareil inaccessible',
          'status': 'error',
          'error': 'HTTP ${response.statusCode}',
          'last_contacted': DateTime.now().toIso8601String(),
        };
      }
    } on TimeoutException {
      debugPrint('⏰ Timeout lors de la récupération des infos de $targetIP');
      return {
        'ip': targetIP,
        'device_name': 'Appareil (timeout)',
        'status': 'timeout',
        'error': 'Délai d\'attente dépassé',
        'last_contacted': DateTime.now().toIso8601String(),
      };
    } on SocketException catch (e) {
      debugPrint('🌐 Erreur réseau pour $targetIP');
      return {
        'ip': targetIP,
        'device_name': 'Appareil (erreur réseau)',
        'status': 'network_error',
        'error': e.message,
        'last_contacted': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ Erreur lors de la récupération des infos de $targetIP: $e');
      return {
        'ip': targetIP,
        'device_name': 'Appareil (erreur)',
        'status': 'error',
        'error': e.toString(),
        'last_contacted': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Synchronise avec un appareil distant avec retry et diagnostics améliorés
  Future<SyncResult> syncWithDevice(String targetIP) async {
    debugPrint('Début de la synchronisation avec $targetIP');

    // Test de connectivité d'abord
    final connectivityTest = await _testConnectivity(targetIP);
    if (!connectivityTest.success) {
      return SyncResult(
        success: false,
        error: connectivityTest.error,
        details: 'Test de connectivité échoué',
      );
    }

    try {
      // 1. Récupérer les données de l'appareil distant avec retry
      debugPrint('Récupération des données de $targetIP...');
      final getResult = await _getRemoteDataWithRetry(targetIP);
      if (!getResult.success) {
        return getResult;
      }

      final remoteData = getResult.data;

      // 2. Restaurer les données localement
      debugPrint('Restauration des données locales...');
      try {
        await BackupService.restoreFromBackup(remoteData!);
      } catch (e) {
        return SyncResult(
          success: false,
          error: 'Erreur de restauration: $e',
          details: 'Échec lors de la restauration des données reçues',
        );
      }

      // 3. Envoyer nos données à l'appareil distant
      debugPrint('Envoi de nos données à $targetIP...');
      final sendResult = await _sendLocalDataWithRetry(targetIP);
      if (!sendResult.success) {
        return sendResult;
      }

      debugPrint('✅ Synchronisation réussie avec $targetIP');
      return SyncResult(
        success: true,
        details: 'Synchronisation bidirectionnelle terminée avec succès. Toutes les données sont maintenant à jour sur les deux appareils.',
      );
    } catch (e) {
      debugPrint('❌ Erreur critique lors de la synchronisation: $e');
      
      // Analyser le type d'erreur pour fournir un message plus utile
      String userFriendlyError;
      String detailedMessage;
      
      if (e.toString().contains('SocketException')) {
        userFriendlyError = 'Problème de connexion réseau';
        detailedMessage = 'Impossible de joindre l\'appareil cible. Vérifiez que les deux appareils sont sur le même réseau WiFi et que le service de synchronisation est actif.';
      } else if (e.toString().contains('TimeoutException')) {
        userFriendlyError = 'Délai d\'attente dépassé';
        detailedMessage = 'La synchronisation prend trop de temps. Cela peut être dû à une connexion lente ou à un volume de données important.';
      } else if (e.toString().contains('FormatException')) {
        userFriendlyError = 'Données corrompues';
        detailedMessage = 'Les données reçues sont dans un format invalide. Une nouvelle tentative pourrait résoudre le problème.';
      } else {
        userFriendlyError = 'Erreur de synchronisation';
        detailedMessage = 'Une erreur inattendue s\'est produite pendant la synchronisation. Veuillez réessayer dans quelques instants.';
      }
      
      return SyncResult(
        success: false,
        error: userFriendlyError,
        details: detailedMessage,
      );
    }
  }

  /// Test de connectivité avec un appareil avec diagnostic avancé
  Future<SyncResult> _testConnectivity(String targetIP) async {
    if (targetIP.isEmpty) {
      return SyncResult(
        success: false,
        error: 'IP invalide',
        details: 'L\'adresse IP ne peut pas être vide',
      );
    }

    final stopwatch = Stopwatch()..start();
    
    try {
      debugPrint('🔍 Test de connectivité avec $targetIP...');
      
      // Étape 1: Test de ping basique
      final pingResult = await _performBasicConnectivityTest(targetIP);
      if (!pingResult.success) {
        return pingResult;
      }
      
      // Étape 2: Test du service de découverte
      final response = await http
          .get(
            Uri.parse('http://$targetIP:$_serverPort$_discoveryEndpoint'),
            headers: {
              'Connection': 'close',
              'User-Agent': 'HCP-CRM-LocalSync/1.0',
              'Accept': 'application/json',
              'Cache-Control': 'no-cache',
            },
          )
          .timeout(const Duration(seconds: 12)); // Timeout plus généreux pour diagnostic

      stopwatch.stop();
      final responseTime = stopwatch.elapsedMilliseconds;
      
      if (response.statusCode == 200) {
        try {
          // Valider que la réponse contient des données JSON valides
          final responseBody = response.body;
          if (responseBody.isNotEmpty) {
            final deviceInfo = jsonDecode(responseBody);
            if (deviceInfo is Map<String, dynamic>) {
              final deviceName = deviceInfo['device_name'] ?? 'Appareil inconnu';
              final serverStatus = deviceInfo['server_status'] ?? 'unknown';
              final appVersion = deviceInfo['app_version'] ?? 'Version inconnue';
              
              debugPrint('✅ Connectivité OK avec $targetIP - $deviceName ($appVersion) en ${responseTime}ms');
              
              // Vérifier le statut du serveur
              if (serverStatus == 'active') {
                return SyncResult(
                  success: true,
                  details: 'Appareil accessible: $deviceName. Serveur actif. Temps de réponse: ${responseTime}ms',
                );
              } else {
                return SyncResult(
                  success: true,
                  details: 'Appareil accessible: $deviceName. Attention: serveur $serverStatus. Temps de réponse: ${responseTime}ms',
                );
              }
            }
          }
          
          // Réponse vide ou invalide mais code 200
          debugPrint('⚠️ Connectivité partielle avec $targetIP (réponse invalide)');
          return SyncResult(
            success: true,
            details: 'Appareil accessible mais réponse incomplète. Temps de réponse: ${responseTime}ms',
          );
        } catch (jsonError) {
          debugPrint('⚠️ Connectivité partielle avec $targetIP (JSON invalide): $jsonError');
          return SyncResult(
            success: true,
            details: 'Appareil accessible mais données corrompues. Réponse: ${response.body.substring(0, math.min(100, response.body.length))}...',
          );
        }
      } else if (response.statusCode == 404) {
        return SyncResult(
          success: false,
          error: 'Service non disponible',
          details: 'L\'appareil répond mais le service de synchronisation n\'est pas actif. Vérifiez que l\'application HCP-CRM est lancée.',
        );
      } else if (response.statusCode == 500) {
        // Analyser l'erreur 500 en détail
        String errorDetails = 'L\'appareil rencontre des problèmes internes';
        try {
          final errorData = jsonDecode(response.body) as Map<String, dynamic>;
          final errorCode = errorData['error_code'] as String? ?? 'UNKNOWN';
          final errorMessage = errorData['message'] as String? ?? 'Erreur inconnue';
          final suggestedAction = errorData['suggested_action'] as String? ?? 'Redémarrez l\'application';
          
          errorDetails = 'Erreur $errorCode: $errorMessage. Action suggérée: $suggestedAction';
        } catch (_) {
          errorDetails += '. Réponse: ${response.body.substring(0, math.min(200, response.body.length))}';
        }
        
        return SyncResult(
          success: false,
          error: 'Erreur serveur (500)',
          details: errorDetails,
        );
      } else if (response.statusCode >= 500) {
        return SyncResult(
          success: false,
          error: 'Erreur serveur (${response.statusCode})',
          details: 'L\'appareil rencontre des problèmes internes. Code: ${response.statusCode}',
        );
      } else {
        return SyncResult(
          success: false,
          error: 'Serveur non disponible (HTTP ${response.statusCode})',
          details: 'L\'appareil répond mais avec un code d\'erreur inattendu. Réponse: ${response.body.substring(0, math.min(100, response.body.length))}',
        );
      }
    } on TimeoutException {
      stopwatch.stop();
      debugPrint('⏰ Timeout de connectivité avec $targetIP après ${stopwatch.elapsedMilliseconds}ms');
      return SyncResult(
        success: false,
        error: 'Timeout de connexion',
        details: 'L\'appareil ne répond pas dans les 12 secondes. Vérifiez qu\'il est allumé, connecté au même réseau WiFi, et que l\'application HCP-CRM est lancée.',
      );
    } on SocketException catch (e) {
      stopwatch.stop();
      debugPrint('🌐 Erreur réseau avec $targetIP: ${e.message}');
      String details = 'Impossible de joindre l\'appareil sur le réseau.';
      
      if (e.message.contains('Network is unreachable')) {
        details += ' Réseau inaccessible - vérifiez votre connexion WiFi et que les appareils sont sur le même réseau.';
      } else if (e.message.contains('Connection refused')) {
        details += ' Connexion refusée - l\'application HCP-CRM n\'est probablement pas lancée sur l\'appareil cible.';
      } else if (e.message.contains('No route to host')) {
        details += ' Aucune route vers l\'hôte - vérifiez que les appareils sont sur le même réseau WiFi.';
      } else if (e.message.contains('Connection timed out')) {
        details += ' Timeout de connexion - l\'appareil est peut-être éteint ou occupé.';
      } else {
        details += ' Erreur réseau: ${e.message}';
      }
      
      return SyncResult(
        success: false,
        error: 'Erreur réseau',
        details: details,
      );
    } on HttpException catch (e) {
      stopwatch.stop();
      debugPrint('🌐 Erreur HTTP avec $targetIP: ${e.message}');
      return SyncResult(
        success: false,
        error: 'Erreur HTTP',
        details: 'Erreur de protocole HTTP: ${e.message}',
      );
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ Erreur inattendue lors du test de connectivité avec $targetIP: $e');
      return SyncResult(
        success: false,
        error: 'Erreur inattendue',
        details: 'Une erreur inattendue s\'est produite: $e',
      );
    }
  }
  
  /// Effectue un test de connectivité basique pour vérifier la disponibilité réseau
  Future<SyncResult> _performBasicConnectivityTest(String targetIP) async {
    try {
      debugPrint('🏓 Test de connectivité basique vers $targetIP...');
      
      // Utiliser une requête HEAD simple comme "ping" HTTP
      await http
          .head(Uri.parse('http://$targetIP:$_serverPort'))
          .timeout(const Duration(seconds: 5));
          
      debugPrint('✅ Test de connectivité basique réussi vers $targetIP');
      return SyncResult(success: true, details: 'Connectivité réseau OK');
      
    } on TimeoutException {
      debugPrint('⏰ Timeout du test de connectivité basique vers $targetIP');
      return SyncResult(
        success: false,
        error: 'Timeout de connectivité',
        details: 'L\'appareil ne répond pas au test de connectivité basique. Il pourrait être éteint ou sur un autre réseau.',
      );
    } on SocketException catch (e) {
      debugPrint('🔌 Test de connectivité basique échoué vers $targetIP: $e');
      return SyncResult(
        success: false,
        error: 'Connectivité réseau échouée',
        details: 'Impossible de joindre l\'appareil sur le réseau. Vérifiez que les appareils sont connectés au même réseau WiFi.',
      );
    } catch (e) {
      // Si le test basique échoue mais que ce n'est pas un problème réseau critique, continuer
      debugPrint('⚠️ Test de connectivité basique avec avertissement vers $targetIP: $e (continuons)');
      return SyncResult(success: true, details: 'Connectivité avec avertissement');
    }
  }

  /// Récupère les données distantes avec retry
  Future<SyncResult> _getRemoteDataWithRetry(String targetIP) async {
    const maxRetries = 4; // Augmenter le nombre de tentatives
    
    if (targetIP.isEmpty) {
      return SyncResult(
        success: false,
        error: 'IP cible invalide',
        details: 'L\'adresse IP ne peut pas être vide',
      );
    }

    String? lastError;
    String? lastDetails;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint(
          '🔄 Tentative $attempt/$maxRetries de récupération des données depuis $targetIP...',
        );

        // Timeout progressif : plus de temps pour les tentatives ultérieures
        final timeoutDuration = Duration(seconds: 30 + (attempt * 10));
        
        final response = await http
            .get(
              Uri.parse('http://$targetIP:$_serverPort$_syncEndpoint'),
              headers: {
                'Connection': 'close',
                'User-Agent': 'HCP-CRM-LocalSync/1.0',
                'Accept': 'application/json',
                'Cache-Control': 'no-cache',
              },
            )
            .timeout(timeoutDuration);

        if (response.statusCode == 200) {
          try {
            final responseBody = response.body;
            if (responseBody.isEmpty) {
              throw Exception('Réponse vide du serveur');
            }
            
            final remoteData = jsonDecode(responseBody);
            if (remoteData is! Map<String, dynamic>) {
              throw Exception('Format de données invalide');
            }
            
            // Valider la structure des données
            if (remoteData.containsKey('status') && remoteData['status'] == 'success') {
              if (remoteData.containsKey('data')) {
                debugPrint('✅ Données récupérées avec succès depuis $targetIP');
                return SyncResult(success: true, data: remoteData['data']);
              } else {
                throw Exception('Données manquantes dans la réponse');
              }
            } else {
              // Traiter comme des données directes si pas de wrapper status
              debugPrint('✅ Données récupérées avec succès depuis $targetIP (format direct)');
              return SyncResult(success: true, data: remoteData);
            }
          } catch (jsonError) {
            lastError = 'Erreur de parsing JSON';
            lastDetails = 'Données corrompues reçues: $jsonError';
            debugPrint('❌ $lastError: $lastDetails');
          }
        } else if (response.statusCode == 404) {
          lastError = 'Service non disponible';
          lastDetails = 'Le service de synchronisation n\'est pas actif sur $targetIP';
          debugPrint('❌ $lastError (404): $lastDetails');
        } else if (response.statusCode >= 500) {
          lastError = 'Erreur serveur';
          lastDetails = 'Le serveur distant rencontre des problèmes (${response.statusCode})';
          debugPrint('❌ $lastError (${response.statusCode}): $lastDetails');
        } else {
          lastError = 'Erreur HTTP ${response.statusCode}';
          lastDetails = 'Réponse inattendue du serveur: ${response.body.length > 100 ? "${response.body.substring(0, 100)}..." : response.body}';
          debugPrint('❌ $lastError: $lastDetails');
        }
      } on TimeoutException {
        lastError = 'Timeout de connexion';
        lastDetails = 'Délai d\'attente dépassé après ${30 + (attempt * 10)}s (tentative $attempt)';
        debugPrint('⏰ $lastError: $lastDetails');
      } on SocketException catch (e) {
         lastError = 'Erreur réseau';
         lastDetails = 'Impossible de joindre $targetIP: ${e.message} (tentative $attempt)';
        debugPrint('🌐 $lastError: $lastDetails');
      } on HttpException catch (e) {
        lastError = 'Erreur HTTP';
        lastDetails = 'Problème de communication: ${e.message} (tentative $attempt)';
        debugPrint('🌐 $lastError: $lastDetails');
      } catch (e) {
        lastError = 'Erreur inattendue';
        lastDetails = 'Erreur non gérée: $e (tentative $attempt)';
        debugPrint('❌ $lastError: $lastDetails');
      }

      // Attendre avant la prochaine tentative (sauf pour la dernière)
      if (attempt < maxRetries) {
        // Backoff exponentiel avec jitter
        final baseDelay = Duration(seconds: attempt * 3);
        final jitter = Duration(milliseconds: (attempt * 500) + (DateTime.now().millisecond % 1000));
        final delay = baseDelay + jitter;
        
        debugPrint('⏳ Attente de ${delay.inSeconds}s avant la prochaine tentative...');
        await Future.delayed(delay);
      }
    }

    return SyncResult(
      success: false,
      error: lastError ?? 'Échec définitif de la synchronisation',
      details: lastDetails ?? '❌ Impossible de récupérer les données distantes après $maxRetries tentatives. Vérifiez la connexion réseau et l\'état du service sur l\'appareil distant.',
    );
  }

  /// Envoie les données locales avec retry
  Future<SyncResult> _sendLocalDataWithRetry(String targetIP) async {
    const maxRetries = 4; // Augmenter le nombre de tentatives
    
    if (targetIP.isEmpty) {
      return SyncResult(
        success: false,
        error: 'IP cible invalide',
        details: 'L\'adresse IP ne peut pas être vide',
      );
    }

    String? lastError;
    String? lastDetails;
    Map<String, dynamic>? localData;
    String? jsonData;

    try {
      debugPrint('📦 Création de la sauvegarde locale...');
      localData = await BackupService.createBackup();
      
      // Pré-sérialiser les données pour éviter de le faire à chaque tentative
      final dataToSend = {
        'data': localData,
        'timestamp': DateTime.now().toIso8601String(),
        'source_device': await _getDeviceName(),
      };
      
      jsonData = jsonEncode(dataToSend);
      debugPrint('📊 Taille des données à envoyer: ${jsonData.length} caractères');
      
    } catch (e) {
      return SyncResult(
        success: false,
        error: 'Erreur lors de la création de la sauvegarde',
        details: 'Impossible de préparer les données: $e',
      );
    }

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        debugPrint('📤 Tentative $attempt/$maxRetries d\'envoi des données vers $targetIP...');

        // Timeout progressif : plus de temps pour les tentatives ultérieures
        final timeoutDuration = Duration(seconds: 60 + (attempt * 20));
        
        final sendResponse = await http
            .post(
              Uri.parse('http://$targetIP:$_serverPort$_syncEndpoint'),
              headers: {
                'Content-Type': 'application/json; charset=utf-8',
                'Connection': 'close',
                'User-Agent': 'HCP-CRM-LocalSync/1.0',
                'Accept': 'application/json',
                'Content-Length': jsonData.length.toString(),
              },
              body: jsonData,
            )
            .timeout(timeoutDuration);

        if (sendResponse.statusCode == 200) {
          try {
            final responseBody = sendResponse.body;
            if (responseBody.isNotEmpty) {
              final responseData = jsonDecode(responseBody);
              if (responseData is Map<String, dynamic>) {
                if (responseData['status'] == 'success') {
                  debugPrint('✅ Données envoyées avec succès vers $targetIP');
                  return SyncResult(success: true, details: 'Synchronisation envoyée avec succès');
                } else {
                  final errorMsg = responseData['message'] ?? 'Erreur serveur inconnue';
                  lastError = 'Erreur de traitement';
                  lastDetails = 'Le serveur a rejeté les données: $errorMsg';
                }
              } else {
                lastError = 'Réponse invalide';
                lastDetails = 'Format de réponse inattendu du serveur';
              }
            } else {
              // Réponse vide mais code 200 - considérer comme succès
              debugPrint('✅ Données envoyées avec succès vers $targetIP (réponse vide)');
              return SyncResult(success: true, details: 'Synchronisation envoyée avec succès');
            }
          } catch (jsonError) {
            // Si on ne peut pas parser la réponse mais le code est 200, considérer comme succès
            debugPrint('✅ Données envoyées avec succès vers $targetIP (réponse non-JSON)');
            return SyncResult(success: true, details: 'Synchronisation envoyée avec succès');
          }
        } else if (sendResponse.statusCode == 413) {
          lastError = 'Données trop volumineuses';
          lastDetails = 'Le serveur ne peut pas traiter des données de cette taille (${jsonData.length} caractères)';
          debugPrint('❌ $lastError (413): $lastDetails');
          // Ne pas retry pour cette erreur spécifique
          break;
        } else if (sendResponse.statusCode == 404) {
          lastError = 'Service non disponible';
          lastDetails = 'Le service de synchronisation n\'est pas actif sur $targetIP';
          debugPrint('❌ $lastError (404): $lastDetails');
        } else if (sendResponse.statusCode >= 500) {
          lastError = 'Erreur serveur';
          lastDetails = 'Le serveur distant rencontre des problèmes (${sendResponse.statusCode})';
          debugPrint('❌ $lastError (${sendResponse.statusCode}): $lastDetails');
        } else {
          lastError = 'Erreur HTTP ${sendResponse.statusCode}';
          lastDetails = 'Réponse inattendue: ${sendResponse.body.length > 100 ? "${sendResponse.body.substring(0, 100)}..." : sendResponse.body}';
          debugPrint('❌ $lastError: $lastDetails');
        }
      } on TimeoutException {
        lastError = 'Timeout d\'envoi';
        lastDetails = 'Délai d\'attente dépassé après ${60 + (attempt * 20)}s (tentative $attempt)';
        debugPrint('⏰ $lastError: $lastDetails');
      } on SocketException catch (e) {
        lastError = 'Erreur réseau';
        lastDetails = 'Impossible de joindre $targetIP: ${e.message} (tentative $attempt)';
        debugPrint('🌐 $lastError: $lastDetails');
      } on HttpException catch (e) {
        lastError = 'Erreur HTTP';
        lastDetails = 'Problème de communication: ${e.message} (tentative $attempt)';
        debugPrint('🌐 $lastError: $lastDetails');
      } catch (e) {
        lastError = 'Erreur inattendue';
        lastDetails = 'Erreur non gérée: $e (tentative $attempt)';
        debugPrint('❌ $lastError: $lastDetails');
      }

      // Attendre avant la prochaine tentative (sauf pour la dernière)
      if (attempt < maxRetries) {
        // Backoff exponentiel avec jitter plus long pour l'envoi
        final baseDelay = Duration(seconds: attempt * 4);
        final jitter = Duration(milliseconds: (attempt * 750) + (DateTime.now().millisecond % 1500));
        final delay = baseDelay + jitter;
        
        debugPrint('⏳ Attente de ${delay.inSeconds}s avant la prochaine tentative d\'envoi...');
        await Future.delayed(delay);
      }
    }

    return SyncResult(
      success: false,
      error: lastError ?? 'Échec définitif de l\'envoi',
      details: lastDetails ?? '❌ Impossible d\'envoyer les données locales après $maxRetries tentatives. Vérifiez que l\'appareil distant est accessible et que son service de synchronisation fonctionne correctement.',
    );
  }

  /// Obtient l'adresse IP locale
  Future<String?> _getLocalIP() async {
    try {
      final info = NetworkInfo();
      return await info.getWifiIP();
    } catch (e) {
      debugPrint('Erreur lors de l\'obtention de l\'IP: $e');
      return null;
    }
  }

  /// Obtient le nom de l'appareil
  Future<String> _getDeviceName() async {
    try {
      final info = NetworkInfo();
      final wifiName = await info.getWifiName();
      return 'HCP-DESIGN-${wifiName ?? 'Device'}';
    } catch (e) {
      return 'HCP-DESIGN-Device';
    }
  }

  /// Diagnostic réseau pour identifier les problèmes de connectivité
  Future<Map<String, dynamic>> networkDiagnostic() async {
    final diagnostic = <String, dynamic>{};

    try {
      // Test de l'IP locale
      final localIP = await _getLocalIP();
      diagnostic['local_ip'] = localIP;
      diagnostic['local_ip_status'] = localIP != null ? 'OK' : 'ERREUR';

      // Test du serveur local
      diagnostic['server_running'] = _isServerRunning;
      diagnostic['server_port'] = _serverPort;

      // Test de connectivité WiFi
      final info = NetworkInfo();
      final wifiName = await info.getWifiName();
      diagnostic['wifi_name'] = wifiName;
      diagnostic['wifi_status'] = wifiName != null ? 'Connecté' : 'Déconnecté';

      // Test de connectivité locale (ping vers la passerelle)
      if (localIP != null) {
        final subnet = localIP.substring(0, localIP.lastIndexOf('.'));
        final gateway = '$subnet.1'; // Généralement la passerelle

        try {
          await http
              .get(Uri.parse('http://$gateway'))
              .timeout(const Duration(seconds: 3));
          diagnostic['gateway_reachable'] = true;
        } catch (e) {
          diagnostic['gateway_reachable'] = false;
          diagnostic['gateway_error'] = e.toString();
        }
      }

      // Nombre d'appareils découverts
      diagnostic['discovered_devices_count'] = _discoveredDevices.length;
      diagnostic['discovered_devices'] = List.from(_discoveredDevices);
    } catch (e) {
      diagnostic['diagnostic_error'] = e.toString();
    }

    return diagnostic;
  }

  /// Test de connectivité spécifique avec un appareil
  Future<Map<String, dynamic>> testDeviceConnectivity(String targetIP) async {
    final test = <String, dynamic>{};
    test['target_ip'] = targetIP;
    test['timestamp'] = DateTime.now().toIso8601String();

    if (targetIP.isEmpty) {
      test['error'] = 'IP invalide';
      return test;
    }

    try {
      // Test 1: Ping simple avec timeout adaptatif
      final pingStart = DateTime.now();
      try {
        final response = await http
            .get(
              Uri.parse('http://$targetIP:$_serverPort$_discoveryEndpoint'),
              headers: {
                'Connection': 'close',
                'User-Agent': 'HCP-CRM-LocalSync/1.0',
                'Accept': 'application/json',
              },
            )
            .timeout(const Duration(seconds: 8)); // Timeout plus généreux

        final pingDuration = DateTime.now().difference(pingStart);
        test['ping_success'] = true;
        test['ping_duration_ms'] = pingDuration.inMilliseconds;
        test['ping_status_code'] = response.statusCode;
        
        // Analyser la qualité de la réponse
        if (response.statusCode == 200) {
          try {
            final deviceInfo = jsonDecode(response.body);
            test['device_info_valid'] = deviceInfo is Map<String, dynamic>;
            test['device_name'] = deviceInfo['device_name'] ?? 'Inconnu';
          } catch (e) {
            test['device_info_valid'] = false;
            test['json_parse_error'] = e.toString();
          }
        }
        
        // Évaluer la qualité de la connexion
        if (pingDuration.inMilliseconds < 100) {
          test['connection_quality'] = 'Excellente';
        } else if (pingDuration.inMilliseconds < 500) {
          test['connection_quality'] = 'Bonne';
        } else if (pingDuration.inMilliseconds < 2000) {
          test['connection_quality'] = 'Moyenne';
        } else {
          test['connection_quality'] = 'Lente';
        }
      } on TimeoutException {
        test['ping_success'] = false;
        test['ping_error'] = 'Timeout après 8s';
        test['connection_quality'] = 'Très lente ou inaccessible';
      } on SocketException catch (e) {
        test['ping_success'] = false;
        test['ping_error'] = 'Erreur réseau: ${e.message}';
        test['connection_quality'] = 'Inaccessible';
      } catch (e) {
        test['ping_success'] = false;
        test['ping_error'] = e.toString();
      }

      // Test 2: Test du service de synchronisation avec timeout plus long
      try {
        final syncStart = DateTime.now();
        final response = await http
            .get(
              Uri.parse('http://$targetIP:$_serverPort$_syncEndpoint'),
              headers: {
                'Connection': 'close',
                'User-Agent': 'HCP-CRM-LocalSync/1.0',
                'Accept': 'application/json',
              },
            )
            .timeout(const Duration(seconds: 15)); // Timeout plus long pour le service

        final syncDuration = DateTime.now().difference(syncStart);
        test['sync_service_available'] = response.statusCode == 200;
        test['sync_duration_ms'] = syncDuration.inMilliseconds;
        test['sync_status_code'] = response.statusCode;
        
        // Analyser la réponse du service de sync
        if (response.statusCode == 200) {
          try {
            final syncData = jsonDecode(response.body);
            test['sync_data_valid'] = syncData is Map<String, dynamic>;
            if (syncData is Map<String, dynamic>) {
              test['sync_has_data'] = syncData.containsKey('data');
              test['sync_status'] = syncData['status'] ?? 'unknown';
            }
          } catch (e) {
            test['sync_data_valid'] = false;
            test['sync_json_error'] = e.toString();
          }
        }
      } on TimeoutException {
        test['sync_service_available'] = false;
        test['sync_error'] = 'Timeout du service après 15s';
      } on SocketException catch (e) {
        test['sync_service_available'] = false;
        test['sync_error'] = 'Erreur réseau: ${e.message}';
      } catch (e) {
        test['sync_service_available'] = false;
        test['sync_error'] = e.toString();
      }
      
      // Évaluation globale de la connectivité
      if (test['ping_success'] == true && test['sync_service_available'] == true) {
        test['overall_status'] = 'Excellent';
        test['recommendation'] = 'Appareil prêt pour la synchronisation';
      } else if (test['ping_success'] == true) {
        test['overall_status'] = 'Partiel';
        test['recommendation'] = 'Appareil accessible mais service de sync indisponible';
      } else {
        test['overall_status'] = 'Échec';
        test['recommendation'] = 'Vérifiez que l\'appareil est allumé et sur le même réseau';
      }
    } catch (e) {
      test['test_error'] = e.toString();
      test['overall_status'] = 'Erreur';
    }

    return test;
  }

  /// Connecte manuellement à un appareil via son adresse IP
  Future<Map<String, dynamic>?> connectToDevice(String ipAddress) async {
    if (ipAddress.isEmpty) {
      debugPrint('❌ Adresse IP vide');
      return null;
    }

    // Valider le format de l'IP
    final ipRegex = RegExp(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$');
    if (!ipRegex.hasMatch(ipAddress)) {
      debugPrint('❌ Format d\'adresse IP invalide: $ipAddress');
      return {
        'success': false,
        'error': 'Format d\'adresse IP invalide',
        'ip': ipAddress,
      };
    }

    try {
      debugPrint('🔗 Tentative de connexion manuelle à $ipAddress');
      
      final response = await http
          .get(
            Uri.parse('http://$ipAddress:$_serverPort$_discoveryEndpoint'),
            headers: {
              'Connection': 'close',
              'User-Agent': 'HCP-CRM-LocalSync/1.0',
              'Accept': 'application/json',
            },
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        try {
          final deviceInfo = jsonDecode(response.body) as Map<String, dynamic>;
          
          // Enrichir les informations de l'appareil
          deviceInfo['ip'] = ipAddress;
          deviceInfo['status'] = 'online';
          deviceInfo['last_seen'] = DateTime.now().toIso8601String();
          deviceInfo['connection_method'] = 'manual';
          
          // Ajouter à la liste des appareils découverts s'il n'y est pas déjà
          if (!_discoveredDevices.contains(ipAddress)) {
            _discoveredDevices.add(ipAddress);
          }
          
          debugPrint('✅ Connexion manuelle réussie à $ipAddress: ${deviceInfo['device_name']}');
          
          return {
            'success': true,
            'device_info': deviceInfo,
            'ip': ipAddress,
          };
        } catch (jsonError) {
          debugPrint('❌ Erreur de parsing JSON pour $ipAddress: $jsonError');
          return {
            'success': false,
            'error': 'Données de l\'appareil invalides',
            'ip': ipAddress,
          };
        }
      } else {
        debugPrint('❌ Code de statut non-200 pour $ipAddress: ${response.statusCode}');
        return {
          'success': false,
          'error': 'Appareil non accessible (code ${response.statusCode})',
          'ip': ipAddress,
        };
      }
    } on TimeoutException {
      debugPrint('⏰ Timeout lors de la connexion à $ipAddress');
      return {
        'success': false,
        'error': 'Timeout de connexion (10s)',
        'ip': ipAddress,
      };
    } on SocketException catch (e) {
      debugPrint('🌐 Erreur réseau pour $ipAddress: ${e.message}');
      return {
        'success': false,
        'error': 'Erreur réseau: ${e.message}',
        'ip': ipAddress,
      };
    } catch (e) {
      debugPrint('❌ Erreur inattendue pour $ipAddress: $e');
      return {
        'success': false,
        'error': 'Erreur inattendue: $e',
        'ip': ipAddress,
      };
    }
  }

  // Getters
  bool get isServerRunning => _isServerRunning;
  String? get localIP => _localIP;
  List<String> get discoveredDevices => List.from(_discoveredDevices);

  /// Valide les données de sauvegarde avant envoi
  Future<DataValidationResult> _validateBackupData(Map<String, dynamic> backupData) async {
    final errors = <String>[];
    final warnings = <String>[];
    int totalItems = 0;

    try {
      // Vérifier la structure de base
      if (!backupData.containsKey('version')) {
        errors.add('Version de sauvegarde manquante');
      }

      if (!backupData.containsKey('timestamp')) {
        errors.add('Timestamp de sauvegarde manquant');
      }

      if (!backupData.containsKey('data')) {
        errors.add('Section de données manquante');
        return DataValidationResult(
          isValid: false,
          errors: errors,
          warnings: warnings,
          summary: 'Structure de sauvegarde invalide',
        );
      }

      final data = backupData['data'] as Map<String, dynamic>;

      // Valider les produits
      if (data.containsKey('products')) {
        final products = data['products'] as List<dynamic>;
        final productValidation = _validateProductsList(products);
        errors.addAll(productValidation.errors);
        warnings.addAll(productValidation.warnings);
        totalItems += products.length;
      }

      // Valider les catégories
      if (data.containsKey('categories')) {
        final categories = data['categories'] as List<dynamic>;
        final categoryValidation = _validateCategoriesList(categories);
        errors.addAll(categoryValidation.errors);
        warnings.addAll(categoryValidation.warnings);
        totalItems += categories.length;
      }

      // Valider les factures
      if (data.containsKey('invoices')) {
        final invoices = data['invoices'] as List<dynamic>;
        final invoiceValidation = _validateInvoicesList(invoices);
        errors.addAll(invoiceValidation.errors);
        warnings.addAll(invoiceValidation.warnings);
        totalItems += invoices.length;
      }

      // Valider les tâches
      if (data.containsKey('tasks')) {
        final tasks = data['tasks'] as List<dynamic>;
        final taskValidation = _validateTasksList(tasks);
        errors.addAll(taskValidation.errors);
        warnings.addAll(taskValidation.warnings);
        totalItems += tasks.length;
      }

      // Valider les colis
      if (data.containsKey('colis')) {
        final colis = data['colis'] as List<dynamic>;
        final colisValidation = _validateColisList(colis);
        errors.addAll(colisValidation.errors);
        warnings.addAll(colisValidation.warnings);
        totalItems += colis.length;
      }

      // Vérifications globales
      if (totalItems == 0) {
        warnings.add('Aucune donnée à synchroniser');
      }

      final summary = 'Validation: $totalItems éléments, ${errors.length} erreurs, ${warnings.length} avertissements';

      return DataValidationResult(
        isValid: errors.isEmpty,
        errors: errors,
        warnings: warnings,
        summary: summary,
      );
    } catch (e) {
      errors.add('Erreur lors de la validation: $e');
      return DataValidationResult(
        isValid: false,
        errors: errors,
        warnings: warnings,
        summary: 'Échec de la validation',
      );
    }
  }

  /// Valide les données reçues avant restauration
  Future<DataValidationResult> _validateReceivedData(dynamic receivedData) async {
    final errors = <String>[];
    final warnings = <String>[];
    int totalItems = 0;

    try {
      // Vérifier que les données sont un Map
      if (receivedData is! Map<String, dynamic>) {
        errors.add('Les données reçues ne sont pas au format attendu');
        return DataValidationResult(
          isValid: false,
          errors: errors,
          warnings: warnings,
          summary: 'Format de données invalide',
        );
      }

      final data = receivedData;

      // Si les données sont encapsulées dans une structure de sauvegarde
      Map<String, dynamic> actualData;
      if (data.containsKey('data')) {
        actualData = data['data'] as Map<String, dynamic>;
      } else {
        actualData = data;
      }

      // Valider chaque type de données présent
      if (actualData.containsKey('products')) {
        final products = actualData['products'] as List<dynamic>;
        final productValidation = _validateProductsList(products);
        errors.addAll(productValidation.errors.map((e) => 'Produits: $e'));
        warnings.addAll(productValidation.warnings.map((w) => 'Produits: $w'));
        totalItems += products.length;
      }

      if (actualData.containsKey('categories')) {
        final categories = actualData['categories'] as List<dynamic>;
        final categoryValidation = _validateCategoriesList(categories);
        errors.addAll(categoryValidation.errors.map((e) => 'Catégories: $e'));
        warnings.addAll(categoryValidation.warnings.map((w) => 'Catégories: $w'));
        totalItems += categories.length;
      }

      if (actualData.containsKey('invoices')) {
        final invoices = actualData['invoices'] as List<dynamic>;
        final invoiceValidation = _validateInvoicesList(invoices);
        errors.addAll(invoiceValidation.errors.map((e) => 'Factures: $e'));
        warnings.addAll(invoiceValidation.warnings.map((w) => 'Factures: $w'));
        totalItems += invoices.length;
      }

      if (actualData.containsKey('tasks')) {
        final tasks = actualData['tasks'] as List<dynamic>;
        final taskValidation = _validateTasksList(tasks);
        errors.addAll(taskValidation.errors.map((e) => 'Tâches: $e'));
        warnings.addAll(taskValidation.warnings.map((w) => 'Tâches: $w'));
        totalItems += tasks.length;
      }

      if (actualData.containsKey('colis')) {
        final colis = actualData['colis'] as List<dynamic>;
        final colisValidation = _validateColisList(colis);
        errors.addAll(colisValidation.errors.map((e) => 'Colis: $e'));
        warnings.addAll(colisValidation.warnings.map((w) => 'Colis: $w'));
        totalItems += colis.length;
      }

      final summary = 'Validation reçue: $totalItems éléments, ${errors.length} erreurs, ${warnings.length} avertissements';

      return DataValidationResult(
        isValid: errors.isEmpty,
        errors: errors,
        warnings: warnings,
        summary: summary,
      );
    } catch (e) {
      errors.add('Erreur lors de la validation des données reçues: $e');
      return DataValidationResult(
        isValid: false,
        errors: errors,
        warnings: warnings,
        summary: 'Échec de la validation des données reçues',
      );
    }
  }

  /// Valide une liste de produits
  ValidationResult _validateProductsList(List<dynamic> products) {
    final errors = <String>[];
    final warnings = <String>[];

    for (int i = 0; i < products.length; i++) {
      final product = products[i];
      if (product is! Map<String, dynamic>) {
        errors.add('Produit $i: format invalide');
        continue;
      }

      final productMap = product;
      
      // Vérifications obligatoires
      if (!productMap.containsKey('id') || productMap['id'] == null || productMap['id'].toString().isEmpty) {
        errors.add('Produit $i: ID manquant ou vide');
      }
      
      if (!productMap.containsKey('name') || productMap['name'] == null || productMap['name'].toString().isEmpty) {
        errors.add('Produit $i: nom manquant ou vide');
      }
      
      // Vérifications optionnelles avec avertissements
      if (!productMap.containsKey('price') || productMap['price'] == null) {
        warnings.add('Produit $i: prix manquant');
      } else {
        final price = productMap['price'];
        if (price is! num || price < 0) {
          errors.add('Produit $i: prix invalide');
        }
      }
      
      if (!productMap.containsKey('stock') || productMap['stock'] == null) {
        warnings.add('Produit $i: stock manquant');
      } else {
        final stock = productMap['stock'];
        if (stock is! num || stock < 0) {
          errors.add('Produit $i: stock invalide');
        }
      }
    }

    return ValidationResult(errors: errors, warnings: warnings);
  }

  /// Valide une liste de catégories
  ValidationResult _validateCategoriesList(List<dynamic> categories) {
    final errors = <String>[];
    final warnings = <String>[];
    final seenIds = <String>{};

    for (int i = 0; i < categories.length; i++) {
      final category = categories[i];
      if (category is! Map<String, dynamic>) {
        errors.add('Catégorie $i: format invalide');
        continue;
      }

      final categoryMap = category;
      
      // Vérifications obligatoires
      if (!categoryMap.containsKey('id') || categoryMap['id'] == null || categoryMap['id'].toString().isEmpty) {
        errors.add('Catégorie $i: ID manquant ou vide');
      } else {
        final id = categoryMap['id'].toString();
        if (seenIds.contains(id)) {
          errors.add('Catégorie $i: ID dupliqué ($id)');
        } else {
          seenIds.add(id);
        }
      }
      
      if (!categoryMap.containsKey('name') || categoryMap['name'] == null || categoryMap['name'].toString().isEmpty) {
        errors.add('Catégorie $i: nom manquant ou vide');
      }
    }

    return ValidationResult(errors: errors, warnings: warnings);
  }

  /// Valide une liste de factures
  ValidationResult _validateInvoicesList(List<dynamic> invoices) {
    final errors = <String>[];
    final warnings = <String>[];

    for (int i = 0; i < invoices.length; i++) {
      final invoice = invoices[i];
      if (invoice is! Map<String, dynamic>) {
        errors.add('Facture $i: format invalide');
        continue;
      }

      final invoiceMap = invoice;
      
      // Vérifications obligatoires
      if (!invoiceMap.containsKey('id') || invoiceMap['id'] == null || invoiceMap['id'].toString().isEmpty) {
        errors.add('Facture $i: ID manquant ou vide');
      }
      
      if (!invoiceMap.containsKey('customerName') || invoiceMap['customerName'] == null || invoiceMap['customerName'].toString().isEmpty) {
        errors.add('Facture $i: nom client manquant ou vide');
      }
      
      if (!invoiceMap.containsKey('totalAmount') || invoiceMap['totalAmount'] == null) {
        errors.add('Facture $i: montant total manquant');
      } else {
        final amount = invoiceMap['totalAmount'];
        if (amount is! num || amount < 0) {
          errors.add('Facture $i: montant total invalide');
        }
      }
      
      // Vérifier la date
      if (!invoiceMap.containsKey('date') || invoiceMap['date'] == null) {
        warnings.add('Facture $i: date manquante');
      } else {
        try {
          DateTime.parse(invoiceMap['date'].toString());
        } catch (e) {
          errors.add('Facture $i: format de date invalide');
        }
      }
    }

    return ValidationResult(errors: errors, warnings: warnings);
  }

  /// Valide une liste de tâches
  ValidationResult _validateTasksList(List<dynamic> tasks) {
    final errors = <String>[];
    final warnings = <String>[];

    for (int i = 0; i < tasks.length; i++) {
      final task = tasks[i];
      if (task is! Map<String, dynamic>) {
        errors.add('Tâche $i: format invalide');
        continue;
      }

      final taskMap = task;
      
      // Vérifications obligatoires
      if (!taskMap.containsKey('id') || taskMap['id'] == null || taskMap['id'].toString().isEmpty) {
        errors.add('Tâche $i: ID manquant ou vide');
      }
      
      if (!taskMap.containsKey('title') || taskMap['title'] == null || taskMap['title'].toString().isEmpty) {
        errors.add('Tâche $i: titre manquant ou vide');
      }
      
      // Vérifier le statut
      if (taskMap.containsKey('isCompleted') && taskMap['isCompleted'] is! bool) {
        errors.add('Tâche $i: statut de completion invalide');
      }
      
      // Vérifier les dates
      if (taskMap.containsKey('createdAt') && taskMap['createdAt'] != null) {
        try {
          DateTime.parse(taskMap['createdAt'].toString());
        } catch (e) {
          errors.add('Tâche $i: format de date de création invalide');
        }
      }
    }

    return ValidationResult(errors: errors, warnings: warnings);
  }

  /// Valide une liste de colis
  ValidationResult _validateColisList(List<dynamic> colis) {
    final errors = <String>[];
    final warnings = <String>[];

    for (int i = 0; i < colis.length; i++) {
      final colisItem = colis[i];
      if (colisItem is! Map<String, dynamic>) {
        errors.add('Colis $i: format invalide');
        continue;
      }

      final colisMap = colisItem;
      
      // Vérifications obligatoires
      if (!colisMap.containsKey('id') || colisMap['id'] == null || colisMap['id'].toString().isEmpty) {
        errors.add('Colis $i: ID manquant ou vide');
      }
      
      if (!colisMap.containsKey('trackingNumber') || colisMap['trackingNumber'] == null || colisMap['trackingNumber'].toString().isEmpty) {
        errors.add('Colis $i: numéro de suivi manquant ou vide');
      }
      
      // Vérifier le statut
      if (colisMap.containsKey('status') && colisMap['status'] != null) {
        final status = colisMap['status'].toString();
        final validStatuses = ['pending', 'shipped', 'delivered', 'cancelled'];
        if (!validStatuses.contains(status.toLowerCase())) {
          warnings.add('Colis $i: statut non reconnu ($status)');
        }
      }
      
      // Vérifier les dates
      if (colisMap.containsKey('createdAt') && colisMap['createdAt'] != null) {
        try {
          DateTime.parse(colisMap['createdAt'].toString());
        } catch (e) {
          errors.add('Colis $i: format de date de création invalide');
        }
      }
    }

    return ValidationResult(errors: errors, warnings: warnings);
  }
}

/// Résultat de validation des données
class DataValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final String summary;

  DataValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.summary,
  });
}

/// Résultat de validation simple
class ValidationResult {
  final List<String> errors;
  final List<String> warnings;

  ValidationResult({
    required this.errors,
    required this.warnings,
  });
}
