import 'package:shared_preferences/shared_preferences.dart';

/// Service pour gérer la configuration offline de l'application
class OfflineConfigService {
  static final OfflineConfigService _instance = OfflineConfigService._internal();
  factory OfflineConfigService() => _instance;
  OfflineConfigService._internal();

  static OfflineConfigService get instance => _instance;

  // Clés de configuration
  static const String _offlineModeKey = 'offline_mode_enabled';
  static const String _autoSyncDisabledKey = 'auto_sync_disabled';
  static const String _firebaseDisabledKey = 'firebase_disabled';
  static const String _networkOperationsDisabledKey = 'network_operations_disabled';
  static const String _manualSyncOnlyKey = 'manual_sync_only';

  /// Vérifier si le mode offline est activé
  Future<bool> isOfflineModeEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_offlineModeKey) ?? true; // Par défaut: mode offline activé
  }

  /// Activer/désactiver le mode offline
  Future<void> setOfflineModeEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_offlineModeKey, enabled);
    
    // Si le mode offline est activé, désactiver toutes les synchronisations automatiques
    if (enabled) {
      await _disableAllAutoSync();
    }
  }

  /// Vérifier si la synchronisation automatique est désactivée
  Future<bool> isAutoSyncDisabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_autoSyncDisabledKey) ?? true; // Par défaut: auto-sync désactivé
  }

  /// Vérifier si Firebase est désactivé
  Future<bool> isFirebaseDisabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_firebaseDisabledKey) ?? true; // Par défaut: Firebase désactivé
  }

  /// Vérifier si les opérations réseau sont désactivées
  Future<bool> areNetworkOperationsDisabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_networkOperationsDisabledKey) ?? true; // Par défaut: réseau désactivé
  }

  /// Vérifier si seule la synchronisation manuelle est autorisée
  Future<bool> isManualSyncOnly() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_manualSyncOnlyKey) ?? true; // Par défaut: sync manuelle uniquement
  }

  /// Désactiver toutes les synchronisations automatiques
  Future<void> _disableAllAutoSync() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoSyncDisabledKey, true);
    await prefs.setBool(_firebaseDisabledKey, true);
    await prefs.setBool(_networkOperationsDisabledKey, true);
    await prefs.setBool(_manualSyncOnlyKey, true);
  }

  /// Activer la synchronisation (mode online)
  Future<void> enableSync() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_offlineModeKey, false);
    await prefs.setBool(_autoSyncDisabledKey, false);
    await prefs.setBool(_firebaseDisabledKey, false);
    await prefs.setBool(_networkOperationsDisabledKey, false);
    await prefs.setBool(_manualSyncOnlyKey, false);
  }

  /// Obtenir le statut de configuration
  Future<Map<String, bool>> getConfigStatus() async {
    return {
      'offlineModeEnabled': await isOfflineModeEnabled(),
      'autoSyncDisabled': await isAutoSyncDisabled(),
      'firebaseDisabled': await isFirebaseDisabled(),
      'networkOperationsDisabled': await areNetworkOperationsDisabled(),
      'manualSyncOnly': await isManualSyncOnly(),
    };
  }

  /// Réinitialiser la configuration (mode offline par défaut)
  Future<void> resetToOfflineMode() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_offlineModeKey, true);
    await _disableAllAutoSync();
  }

  /// Vérifier si une opération réseau peut être effectuée
  Future<bool> canPerformNetworkOperation() async {
    final offlineMode = await isOfflineModeEnabled();
    final networkDisabled = await areNetworkOperationsDisabled();
    return !offlineMode && !networkDisabled;
  }

  /// Vérifier si Firebase peut être utilisé
  Future<bool> canUseFirebase() async {
    final offlineMode = await isOfflineModeEnabled();
    final firebaseDisabled = await isFirebaseDisabled();
    return !offlineMode && !firebaseDisabled;
  }

  /// Vérifier si la synchronisation automatique peut être effectuée
  Future<bool> canPerformAutoSync() async {
    final offlineMode = await isOfflineModeEnabled();
    final autoSyncDisabled = await isAutoSyncDisabled();
    return !offlineMode && !autoSyncDisabled;
  }

  /// Initialiser la configuration par défaut (mode offline)
  Future<void> initializeDefaultConfig() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Si c'est la première fois, configurer en mode offline
    final isFirstTime = !prefs.containsKey(_offlineModeKey);
    if (isFirstTime) {
      await resetToOfflineMode();
      print('🔒 Configuration par défaut: Mode offline activé');
    }
  }
}
