import 'dart:async';
import 'package:flutter/material.dart';

/// Service pour notifier les changements de données dans l'application
class DataChangeNotifier {
  static DataChangeNotifier? _instance;
  static DataChangeNotifier get instance {
    _instance ??= DataChangeNotifier._internal();
    return _instance!;
  }

  DataChangeNotifier._internal();

  // Contrôleurs de stream pour différents types de données
  final StreamController<bool> _productsChangedController =
      StreamController<bool>.broadcast();
  final StreamController<bool> _categoriesChangedController =
      StreamController<bool>.broadcast();
  final StreamController<bool> _invoicesChangedController =
      StreamController<bool>.broadcast();
  final StreamController<bool> _tasksChangedController =
      StreamController<bool>.broadcast();
  final StreamController<bool> _colisChangedController =
      StreamController<bool>.broadcast();
  final StreamController<bool> _allDataChangedController =
      StreamController<bool>.broadcast();

  // Streams publics
  Stream<bool> get productsChanged => _productsChangedController.stream;
  Stream<bool> get categoriesChanged => _categoriesChangedController.stream;
  Stream<bool> get invoicesChanged => _invoicesChangedController.stream;
  Stream<bool> get tasksChanged => _tasksChangedController.stream;
  Stream<bool> get colisChanged => _colisChangedController.stream;
  Stream<bool> get allDataChanged => _allDataChangedController.stream;

  /// Notifie que les produits ont changé
  void notifyProductsChanged() {
    debugPrint('🔄 Notification: Produits modifiés');
    _productsChangedController.add(true);
    _allDataChangedController.add(true);
  }

  /// Notifie que les catégories ont changé
  void notifyCategoriesChanged() {
    debugPrint('🔄 Notification: Catégories modifiées');
    _categoriesChangedController.add(true);
    _allDataChangedController.add(true);
  }

  /// Notifie que les factures ont changé
  void notifyInvoicesChanged() {
    debugPrint('🔄 Notification: Factures modifiées');
    _invoicesChangedController.add(true);
    _allDataChangedController.add(true);
  }

  /// Notifie que les tâches ont changé
  void notifyTasksChanged() {
    debugPrint('🔄 Notification: Tâches modifiées');
    _tasksChangedController.add(true);
    _allDataChangedController.add(true);
  }

  /// Notifie que les colis ont changé
  void notifyColisChanged() {
    debugPrint('🔄 Notification: Colis modifiés');
    _colisChangedController.add(true);
    _allDataChangedController.add(true);
  }

  /// Notifie que toutes les données ont changé (utilisé après une restauration)
  void notifyAllDataChanged() {
    debugPrint('🔄 Notification: Toutes les données modifiées');
    _productsChangedController.add(true);
    _categoriesChangedController.add(true);
    _invoicesChangedController.add(true);
    _tasksChangedController.add(true);
    _colisChangedController.add(true);
    _allDataChangedController.add(true);
  }

  /// Nettoie les ressources
  void dispose() {
    _productsChangedController.close();
    _categoriesChangedController.close();
    _invoicesChangedController.close();
    _tasksChangedController.close();
    _colisChangedController.close();
    _allDataChangedController.close();
  }
}

/// Mixin pour faciliter l'écoute des changements de données dans les widgets
mixin DataChangeListener<T extends StatefulWidget> on State<T> {
  late StreamSubscription<bool> _dataChangeSubscription;

  @override
  void initState() {
    super.initState();
    _setupDataChangeListener();
  }

  void _setupDataChangeListener() {
    _dataChangeSubscription = DataChangeNotifier.instance.allDataChanged.listen(
      (_) {
        if (mounted) {
          onDataChanged();
        }
      },
    );
  }

  /// Méthode à implémenter dans les widgets pour réagir aux changements
  void onDataChanged();

  @override
  void dispose() {
    _dataChangeSubscription.cancel();
    super.dispose();
  }
}
