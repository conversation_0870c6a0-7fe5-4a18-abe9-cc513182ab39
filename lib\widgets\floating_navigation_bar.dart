import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/badge_service.dart';
import 'notification_badge.dart';

class FloatingNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const FloatingNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final iconSize = 85.0; // Taille fixe de 85px pour toutes les icônes
    final containerHeight = isSmallScreen ? 60.0 : 70.0;
    
    return Consumer<BadgeService>(
      builder: (context, badgeService, child) {
        return Container(
          height: containerHeight,
          decoration: const BoxDecoration(
            color: Colors.transparent,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildNavItem(
                context: context,
                iconPath: 'assets/icons/navigation/dashboard.png',
                index: 0,
                isCenter: false,
                iconSize: iconSize,
                badgeCount: badgeService.pendingReturnsCount,
                badgeType: BadgeType.dashboard,
              ),
              _buildNavItem(
                context: context,
                iconPath: 'assets/icons/navigation/receipt.png',
                index: 1,
                isCenter: false,
                iconSize: iconSize,
                badgeCount: 0, // Pas de badge pour les factures pour l'instant
              ),
              _buildNavItem(
                context: context,
                iconPath: 'assets/icons/navigation/inventory.png',
                index: 2,
                isCenter: false,
                iconSize: iconSize,
                badgeCount: badgeService.outOfStockCount,
                badgeType: BadgeType.inventory,
              ),
              _buildNavItem(
                context: context,
                iconPath: 'assets/icons/navigation/tasks.png',
                index: 3,
                isCenter: false,
                iconSize: iconSize,
                badgeCount: badgeService.pendingTasksCount,
                badgeType: BadgeType.tasks,
              ),
              _buildNavItem(
                context: context,
                iconPath: 'assets/icons/navigation/whatsapp.png',
                index: 4,
                isCenter: false,
                iconSize: iconSize,
                color: const Color(0xFF25D366),
                badgeCount: badgeService.unreadWhatsAppCount,
                badgeType: BadgeType.whatsapp,
              ),
            ],
          ),
         );
       },
     );
   }

  Widget _buildNavItem({
    required BuildContext context,
    required String iconPath,
    required int index,
    required bool isCenter,
    required double iconSize,
    Color? color,
    int badgeCount = 0,
    BadgeType? badgeType,
  }) {
    final bool isSelected = currentIndex == index;

    Widget navItem = GestureDetector(
      onTap: () => onTap(index),
      child: Padding(
         padding: const EdgeInsets.all(
           0.5,
         ), // Padding minimal pour rapprocher au maximum les icônes
         child: TweenAnimationBuilder<double>(
           duration: const Duration(milliseconds: 600),
           tween: Tween<double>(
             begin: 1.0,
                    end: isSelected ? 1.6 : 1.0,
           ),
           curve: isSelected ? Curves.elasticOut : Curves.easeInOut,
           builder: (context, scale, child) {
             return Transform.scale(
               scale: scale,
               child: AnimatedContainer(
                 duration: const Duration(milliseconds: 200),
                 child: _buildIconWidget(iconPath, index, isSelected, color, iconSize),
               ),
             );
           },
         ),
      ),
    );

    // Ajouter le badge si nécessaire
    if (badgeCount > 0 && badgeType != null) {
      return NavigationBadge(
        count: badgeCount,
        type: badgeType,
        child: navItem,
      );
    }

    return navItem;
  }

  Widget _buildIconWidget(
    String iconPath,
    int index,
    bool isSelected,
    Color? color,
    double iconSize,
  ) {
    // Utilisation exclusive de vos icônes PNG avec coloration pour l'état sélectionné
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      child: Image.asset(
        iconPath,
        width: iconSize,
        height: iconSize,
        fit: BoxFit.contain,
        // Temporairement sans coloration pour tester
        // color: isSelected 
        //     ? Colors.white 
        //     : (color ?? Colors.white.withValues(alpha: 0.7)),
        // colorBlendMode: BlendMode.srcIn,
        filterQuality: FilterQuality.high,
        errorBuilder: (context, error, stackTrace) {
          // Debug: Afficher l'erreur dans la console
          print('Erreur chargement icône $iconPath: $error');
          // En cas d'erreur, afficher un container coloré au lieu d'une icône Material
          return Container(
              width: iconSize,
              height: iconSize,
            decoration: BoxDecoration(
              // ignore: deprecated_member_use
              color: Colors.red.withOpacity(0.8),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      color: Colors.black26,
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
