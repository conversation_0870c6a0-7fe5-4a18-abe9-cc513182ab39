# Guide de Récupération des Données

## Vue d'ensemble

La fonctionnalité de récupération des données a été ajoutée pour résoudre les problèmes de chargement des anciennes données dans l'application. Cette fonctionnalité permet de diagnostiquer et corriger automatiquement les problèmes de synchronisation entre les données locales et Firebase.

## Accès à la fonctionnalité

### Depuis le Dashboard
1. Ouvrez l'application
2. Sur la page d'accueil (Dashboard)
3. Cliquez sur l'icône de récupération (🩹) dans la barre d'outils en haut
4. La page "Récupération des Données" s'ouvrira

## Fonctionnalités disponibles

### 1. Diagnostic automatique
- **Vérification des données locales** : Compte et état des factures, colis, produits et tâches stockés localement
- **Vérification des données Firebase** : État de la connexion et données synchronisées dans le cloud
- **Statut de synchronisation** : État de la migration et dernière synchronisation
- **Détection des problèmes** : Identification automatique des problèmes courants

### 2. Solutions recommandées
Le système propose automatiquement des solutions basées sur les problèmes détectés :
- Migration incomplète
- Données obsolètes
- Problèmes de connectivité
- Opérations en attente

### 3. Actions de récupération

#### Récupération des anciennes données
- **Fonction** : Migre les données de l'ancien format vers le nouveau
- **Utilisation** : Cliquez sur "Récupérer les Anciennes Données"
- **Résultat** : Les données au format obsolète sont converties et synchronisées

#### Synchronisation forcée
- **Fonction** : Force une synchronisation complète avec Firebase
- **Utilisation** : Cliquez sur "Forcer la Synchronisation"
- **Résultat** : Toutes les données sont re-synchronisées entre local et cloud

## Problèmes courants et solutions

### Problème : "Données locales non synchronisées"
**Cause** : Les données locales n'ont pas été migrées vers Firebase
**Solution** : Utiliser "Forcer la Synchronisation"

### Problème : "Migration incomplète"
**Cause** : Le processus de migration initial n'a pas été terminé
**Solution** : Utiliser "Récupérer les Anciennes Données"

### Problème : "Données obsolètes détectées"
**Cause** : Présence de données dans l'ancien format
**Solution** : Utiliser "Récupérer les Anciennes Données"

### Problème : "Opérations en attente"
**Cause** : Des opérations n'ont pas pu être synchronisées (hors ligne)
**Solution** : Vérifier la connexion internet et utiliser "Forcer la Synchronisation"

## Interprétation des indicateurs

### État du système
- ✅ **Vert** : Aucun problème détecté
- ⚠️ **Orange** : Problèmes mineurs détectés
- ❌ **Rouge** : Problèmes critiques nécessitant une action

### Données locales/Firebase
- **Nombre** : Quantité d'éléments de chaque type
- ✅ **Icône verte** : Données présentes et valides
- ❌ **Icône grise** : Aucune donnée ou données corrompues

### Statut de synchronisation
- **Migration terminée** : Indique si la migration initiale est complète
- **Dernière sync** : Horodatage de la dernière synchronisation réussie
- **Opérations en attente** : Nombre d'opérations non synchronisées

## Bonnes pratiques

### Utilisation régulière
1. **Vérification hebdomadaire** : Accédez à la page de récupération une fois par semaine
2. **Après des problèmes** : Utilisez la fonctionnalité après avoir remarqué des données manquantes
3. **Après une panne** : Vérifiez l'état après une coupure internet ou un crash

### Ordre des actions recommandé
1. **Diagnostic** : Laissez le système analyser automatiquement
2. **Récupération** : Si des données obsolètes sont détectées
3. **Synchronisation** : Si des problèmes de sync sont identifiés
4. **Vérification** : Relancez le diagnostic pour confirmer la résolution

## Dépannage avancé

### Si la récupération échoue
1. Vérifiez votre connexion internet
2. Redémarrez l'application
3. Essayez la synchronisation forcée
4. Contactez le support si le problème persiste

### Si les données restent manquantes
1. Vérifiez les sauvegardes dans "Sauvegarde et restauration"
2. Utilisez la fonctionnalité de restauration si disponible
3. Recréez les données manuellement si nécessaire

## Sécurité et confidentialité

- Toutes les opérations sont effectuées localement et via Firebase sécurisé
- Aucune donnée n'est envoyée à des tiers
- Les logs de diagnostic ne contiennent pas de données sensibles
- La récupération préserve l'intégrité des données existantes

## Support

Si vous rencontrez des problèmes persistants :
1. Notez les messages d'erreur affichés
2. Prenez une capture d'écran de la page de diagnostic
3. Contactez l'équipe de support avec ces informations

---

*Cette fonctionnalité a été développée pour améliorer la fiabilité et la récupération des données dans l'application CRM.*