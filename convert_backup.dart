import 'dart:convert';
import 'dart:io';

void main() async {
  try {
    // Chemin du fichier de sauvegarde segmentée
    final inputFile = File('hcp_backup_invoices_Juillet_2025_1755625135430.json');
    
    if (!await inputFile.exists()) {
      print('❌ Fichier d\'entrée non trouvé: ${inputFile.path}');
      return;
    }
    
    print('📖 Lecture du fichier de sauvegarde segmentée...');
    final jsonString = await inputFile.readAsString();
    final segmentedBackup = jsonDecode(jsonString) as Map<String, dynamic>;
    
    print('🔄 Conversion vers le format standard...');
    
    // Extraire les données
    final data = segmentedBackup['data'] as Map<String, dynamic>? ?? {};
    final invoices = data['invoices'] as List<dynamic>? ?? [];
    
    // Créer la structure standard
    final standardBackup = {
      'version': segmentedBackup['version'] ?? '2.2.0',
      'timestamp': segmentedBackup['timestamp'] ?? DateTime.now().toIso8601String(),
      'data': {
        'products': <Map<String, dynamic>>[],
        'categories': <Map<String, dynamic>>[],
        'invoices': invoices.map((invoice) => Map<String, dynamic>.from(invoice as Map<String, dynamic>)).toList(),
        'tasks': <Map<String, dynamic>>[],
        'colis': <Map<String, dynamic>>[],
      },
      'images': {
        'invoices': <String, dynamic>{},
        'colis': <String, dynamic>{},
      },
    };
    
    // Écrire le fichier converti
    final outputFile = File('hcp_backup_standard_format_complete.json');
    await outputFile.writeAsString(
      const JsonEncoder.withIndent('  ').convert(standardBackup),
    );
    
    print('✅ Conversion terminée!');
    print('📄 Fichier de sortie: ${outputFile.path}');
    print('📊 ${invoices.length} factures converties');
    
    // Afficher un résumé
    final stats = segmentedBackup['stats'] as Map<String, dynamic>? ?? {};
    if (stats.isNotEmpty) {
      print('\n📈 Statistiques:');
      stats.forEach((key, value) => print('  - $key: $value'));
    }
    
    // Afficher la configuration si présente
    final config = segmentedBackup['config'] as Map<String, dynamic>? ?? {};
    if (config.isNotEmpty) {
      print('\n⚙️ Configuration de la sauvegarde segmentée:');
      print('  - Types de données: ${config['dataTypes']}');
      if (config.containsKey('period')) {
        final period = config['period'] as Map<String, dynamic>;
        print('  - Période: ${period['displayName']}');
        print('  - Du: ${period['startDate']}');
        print('  - Au: ${period['endDate']}');
      }
      print('  - Images incluses: ${config['includeImages'] ?? false}');
      print('  - Données compressées: ${config['compressData'] ?? false}');
    }
    
  } catch (e) {
    print('❌ Erreur lors de la conversion: $e');
  }
}