import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Service pour gérer le responsive design de manière uniforme
/// entre les versions web et mobile
class ResponsiveService {
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;

  /// Détermine si l'écran est mobile
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }

  /// Détermine si l'écran est tablette
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }

  /// Détermine si l'écran est desktop
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Retourne le nombre de colonnes optimal pour les grilles
  static int getGridCrossAxisCount(BuildContext context) {
    if (kIsWeb) {
      // Sur web, on adapte selon la taille d'écran
      if (isDesktop(context)) return 4;
      if (isTablet(context)) return 3;
      return 2;
    } else {
      // Sur mobile, on garde 2 colonnes pour une expérience identique
      return 2;
    }
  }

  /// Retourne le padding adaptatif
  static EdgeInsets getAdaptivePadding(BuildContext context) {
    if (kIsWeb && isDesktop(context)) {
      return const EdgeInsets.all(24.0);
    }
    return const EdgeInsets.all(16.0);
  }

  /// Retourne la largeur maximale pour le contenu
  static double getMaxContentWidth(BuildContext context) {
    if (kIsWeb && isDesktop(context)) {
      return 1200.0;
    }
    return double.infinity;
  }

  /// Retourne l'espacement adaptatif pour les grilles
  static double getGridSpacing(BuildContext context) {
    if (kIsWeb && isDesktop(context)) {
      return 16.0;
    }
    return 12.0;
  }

  /// Retourne le ratio d'aspect adaptatif pour les cartes
  static double getCardAspectRatio(BuildContext context) {
    return 1.0; // Garde le même ratio pour une expérience identique
  }

  /// Détermine si on doit utiliser la navigation drawer sur web
  static bool shouldUseDrawer(BuildContext context) {
    return kIsWeb && isDesktop(context);
  }

  /// Retourne la taille de police adaptative
  static double getAdaptiveFontSize(BuildContext context, double baseFontSize) {
    if (kIsWeb && isDesktop(context)) {
      return baseFontSize * 1.1;
    }
    return baseFontSize;
  }

  /// Retourne la hauteur d'AppBar adaptative
  static double getAppBarHeight(BuildContext context) {
    return kToolbarHeight; // Garde la même hauteur partout
  }

  /// Détermine si on doit afficher la navigation bottom sur web
  static bool shouldShowBottomNavigation(BuildContext context) {
    // Toujours afficher la navigation bottom pour une expérience identique
    return true;
  }

  /// Retourne les contraintes adaptatives pour les dialogs
  static BoxConstraints getDialogConstraints(BuildContext context) {
    if (kIsWeb && isDesktop(context)) {
      return const BoxConstraints(
        maxWidth: 600,
        maxHeight: 800,
      );
    }
    return BoxConstraints(
      maxWidth: MediaQuery.of(context).size.width * 0.9,
      maxHeight: MediaQuery.of(context).size.height * 0.8,
    );
  }

  /// Retourne la configuration de navigation adaptative
  static NavigationConfig getNavigationConfig(BuildContext context) {
    return NavigationConfig(
      useBottomNavigation: shouldShowBottomNavigation(context),
      useDrawer: shouldUseDrawer(context),
      showFloatingNavigation: true, // Toujours afficher pour l'uniformité
    );
  }
}

/// Configuration de navigation
class NavigationConfig {
  final bool useBottomNavigation;
  final bool useDrawer;
  final bool showFloatingNavigation;

  const NavigationConfig({
    required this.useBottomNavigation,
    required this.useDrawer,
    required this.showFloatingNavigation,
  });
}
