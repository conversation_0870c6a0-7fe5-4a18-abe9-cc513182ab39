import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/printer_service.dart';
import '../../services/receipt_builder.dart';

class ThermalDiagnosticPage extends StatefulWidget {
  const ThermalDiagnosticPage({super.key});

  @override
  State<ThermalDiagnosticPage> createState() => _ThermalDiagnosticPageState();
}

class _ThermalDiagnosticPageState extends State<ThermalDiagnosticPage> {
  late PrinterService _printerService;
  Map<String, dynamic>? _diagnosticResults;
  bool _isRunningDiagnostic = false;
  bool _isTestingPrint = false;
  String? _lastError;

  @override
  void initState() {
    super.initState();
    _printerService = context.read<PrinterService>();
    _runInitialDiagnostic();
  }

  Future<void> _runInitialDiagnostic() async {
    await _runDiagnostic();
  }

  Future<void> _runDiagnostic() async {
    setState(() {
      _isRunningDiagnostic = true;
      _lastError = null;
    });

    try {
      final results = await _printerService.runDiagnostic();
      setState(() {
        _diagnosticResults = results;
      });
    } catch (e) {
      setState(() {
        _lastError = e.toString();
      });
    } finally {
      setState(() {
        _isRunningDiagnostic = false;
      });
    }
  }

  Future<void> _testSimplePrint() async {
    setState(() {
      _isTestingPrint = true;
      _lastError = null;
    });

    try {
      // Vérifier d'abord la connexion
      final isConnected = await _printerService.isConnected();
      if (!isConnected) {
        throw Exception('Aucune imprimante connectée');
      }

      // Générer un reçu de test ultra-simple pour A70pro
      final testBytes = await ReceiptBuilder.generateSimpleTestReceipt();

      // Imprimer
      final success = await _printerService.printBytes(testBytes);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Test d\'impression réussi !'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('L\'impression a échoué');
      }
    } catch (e) {
      setState(() {
        _lastError = 'Erreur test impression: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Test échoué: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isTestingPrint = false;
      });
    }
  }

  Future<void> _testStandardPrint() async {
    setState(() {
      _isTestingPrint = true;
      _lastError = null;
    });

    try {
      // Vérifier d'abord la connexion
      final isConnected = await _printerService.isConnected();
      if (!isConnected) {
        throw Exception('Aucune imprimante connectée');
      }

      // Générer un reçu de test standard (complet)
      final testBytes = await ReceiptBuilder.generateTestReceipt();

      // Imprimer
      final success = await _printerService.printBytes(testBytes);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Test d\'impression standard réussi !'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('L\'impression standard a échoué');
      }
    } catch (e) {
      setState(() {
        _lastError = 'Erreur test standard: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Test standard échoué: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isTestingPrint = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Diagnostic Impression Thermique'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _isRunningDiagnostic ? null : _runDiagnostic,
            icon: const Icon(Icons.refresh),
            tooltip: 'Actualiser diagnostic',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Icon(Icons.bug_report, size: 48, color: Colors.blue[700]),
                    const SizedBox(height: 8),
                    Text(
                      'Diagnostic Complet',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Cette page permet de diagnostiquer les problèmes d\'impression thermique et de tester la connexion avec votre imprimante.',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Résultats du diagnostic
            if (_isRunningDiagnostic)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Diagnostic en cours...'),
                    ],
                  ),
                ),
              )
            else if (_diagnosticResults != null)
              _buildDiagnosticResults(),

            const SizedBox(height: 24),

            // Erreurs
            if (_lastError != null) _buildErrorSection(),

            const SizedBox(height: 24),

            // Actions de test
            _buildTestActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticResults() {
    final results = _diagnosticResults!;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assessment, color: Colors.blue[700]),
                const SizedBox(width: 8),
                const Text(
                  'Résultats du Diagnostic',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // État Bluetooth
            _buildDiagnosticItem(
              'Bluetooth activé',
              results['bluetooth_enabled'] ?? false,
              Icons.bluetooth,
            ),

            // Connexion Bluetooth
            _buildDiagnosticItem(
              'Bluetooth connecté',
              results['bluetooth_connected'] ?? false,
              Icons.bluetooth_connected,
            ),

            // Imprimante en mémoire
            _buildDiagnosticItem(
              'Imprimante en mémoire',
              results['printer_in_memory'] ?? false,
              Icons.memory,
            ),

            // Test de communication
            _buildDiagnosticItem(
              'Test de communication',
              results['communication_test'] ?? false,
              Icons.chat,
            ),

            // Informations de l'imprimante
            if (results['printer_name'] != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'Informations Imprimante',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
              const SizedBox(height: 8),
              _buildInfoRow('Nom', results['printer_name']),
              _buildInfoRow('Adresse', results['printer_address']),
              _buildInfoRow('Type', results['printer_type']),
            ],

            // Erreurs
            if (results['errors'] != null && results['errors'].isNotEmpty) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Text(
                'Erreurs Détectées',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.red[700],
                ),
              ),
              const SizedBox(height: 8),
              ...results['errors'].map<Widget>(
                (error) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.error, size: 16, color: Colors.red[600]),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          error.toString(),
                          style: TextStyle(color: Colors.red[700]),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            // Timestamp
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Text(
              'Dernière vérification: ${DateTime.parse(results['timestamp']).toString().substring(0, 19)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiagnosticItem(String label, bool status, IconData icon) {
    final color = status ? Colors.green : Colors.red;
    final statusIcon = status ? Icons.check_circle : Icons.cancel;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ),
          Icon(statusIcon, size: 20, color: color),
          const SizedBox(width: 8),
          Text(
            status ? 'OK' : 'Erreur',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildErrorSection() {
    return Card(
      color: Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red[700]),
                const SizedBox(width: 8),
                Text(
                  'Dernière Erreur',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(_lastError!, style: TextStyle(color: Colors.red[700])),
          ],
        ),
      ),
    );
  }

  Widget _buildTestActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.play_arrow, color: Colors.green[700]),
                const SizedBox(width: 8),
                const Text(
                  'Tests Disponibles',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Test d'impression simple
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isTestingPrint ? null : _testSimplePrint,
                icon:
                    _isTestingPrint
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.print),
                label: Text(
                  _isTestingPrint
                      ? 'Test en cours...'
                      : 'Test A70pro (Ultra-simple)',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Test d'impression standard
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isTestingPrint ? null : _testStandardPrint,
                icon:
                    _isTestingPrint
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.print_outlined),
                label: Text(
                  _isTestingPrint
                      ? 'Test en cours...'
                      : 'Test Standard (Complet)',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 12),

            // Actualiser diagnostic
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _isRunningDiagnostic ? null : _runDiagnostic,
                icon:
                    _isRunningDiagnostic
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.refresh),
                label: Text(
                  _isRunningDiagnostic
                      ? 'Diagnostic en cours...'
                      : 'Actualiser le diagnostic',
                ),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.blue[600],
                  side: BorderSide(color: Colors.blue[600]!),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
