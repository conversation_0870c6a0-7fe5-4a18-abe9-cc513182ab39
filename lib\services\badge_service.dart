import 'dart:async';
import 'package:flutter/foundation.dart';
import 'colis_service.dart';
import 'whatsapp_service.dart';
import 'task_service.dart';
import 'inventory_service.dart';
import '../models/colis.dart';

class BadgeService extends ChangeNotifier {
  static final BadgeService _instance = BadgeService._internal();
  factory BadgeService() => _instance;
  BadgeService._internal();

  // Compteurs de badges
  int _unreadWhatsAppCount = 0;
  int _pendingTasksCount = 0;
  int _outOfStockCount = 0;
  int _pendingReturnsCount = 0;

  // Getters pour les compteurs
  int get unreadWhatsAppCount => _unreadWhatsAppCount;
  int get pendingTasksCount => _pendingTasksCount;
  int get outOfStockCount => _outOfStockCount;
  int get pendingReturnsCount => _pendingReturnsCount;

  // Timer pour la mise à jour périodique
  Timer? _updateTimer;

  void startPeriodicUpdates() {
    // Mise à jour immédiate
    updateAllCounts();
    
    // Mise à jour toutes les 30 secondes
    _updateTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      updateAllCounts();
    });
  }

  void stopPeriodicUpdates() {
    _updateTimer?.cancel();
    _updateTimer = null;
  }

  Future<void> updateAllCounts() async {
    await Future.wait([
      updateWhatsAppCount(),
      updateTasksCount(),
      updateStockCount(),
      updateReturnsCount(),
    ]);
    notifyListeners();
  }

  // Mise à jour du compteur WhatsApp
  Future<void> updateWhatsAppCount() async {
    try {
      // Utiliser les vraies données du WhatsAppService
      final whatsappService = WhatsAppService();
      _unreadWhatsAppCount = whatsappService.getTotalUnreadCount();
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la mise à jour du compteur WhatsApp: $e');
      }
      _unreadWhatsAppCount = 0;
    }
  }

  // Mise à jour du compteur des tâches
  Future<void> updateTasksCount() async {
    try {
      // Utiliser les vraies données du TaskService
      final taskService = TaskService.instance;
      final pendingTasks = await taskService.getPendingTasks();
      _pendingTasksCount = pendingTasks.length;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la mise à jour du compteur des tâches: $e');
      }
      _pendingTasksCount = 0;
    }
  }

  // Mise à jour du compteur de stock
  Future<void> updateStockCount() async {
    try {
      // Utiliser les vraies données de l'InventoryService
      final inventoryService = InventoryService.instance;
      final products = await inventoryService.getProducts();
      _outOfStockCount = products.where((product) => product.quantity == 0).length;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la mise à jour du compteur de stock: $e');
      }
      _outOfStockCount = 0;
    }
  }

  // Mise à jour du compteur des retours
  Future<void> updateReturnsCount() async {
    try {
      final colisService = ColisService();
      final colis = await colisService.getAllColis();
      _pendingReturnsCount = colis.where((c) => 
        c.statut == StatutLivraison.retour
      ).length;
    } catch (e) {
      if (kDebugMode) {
        print('Erreur lors de la mise à jour du compteur des retours: $e');
      }
      _pendingReturnsCount = 0;
    }
  }



  // Méthodes pour mettre à jour manuellement les compteurs
  void setWhatsAppCount(int count) {
    _unreadWhatsAppCount = count;
    notifyListeners();
  }

  void setTasksCount(int count) {
    _pendingTasksCount = count;
    notifyListeners();
  }

  void setStockCount(int count) {
    _outOfStockCount = count;
    notifyListeners();
  }

  void setReturnsCount(int count) {
    _pendingReturnsCount = count;
    notifyListeners();
  }

  // Méthodes pour incrémenter/décrémenter
  void incrementWhatsAppCount() {
    _unreadWhatsAppCount++;
    notifyListeners();
  }

  void decrementWhatsAppCount() {
    if (_unreadWhatsAppCount > 0) {
      _unreadWhatsAppCount--;
      notifyListeners();
    }
  }

  void incrementTasksCount() {
    _pendingTasksCount++;
    notifyListeners();
  }

  void decrementTasksCount() {
    if (_pendingTasksCount > 0) {
      _pendingTasksCount--;
      notifyListeners();
    }
  }



  void incrementStockCount() {
    _outOfStockCount++;
    notifyListeners();
  }

  void decrementStockCount() {
    if (_outOfStockCount > 0) {
      _outOfStockCount--;
      notifyListeners();
    }
  }

  void incrementReturnsCount() {
    _pendingReturnsCount++;
    notifyListeners();
  }

  void decrementReturnsCount() {
    if (_pendingReturnsCount > 0) {
      _pendingReturnsCount--;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    stopPeriodicUpdates();
    super.dispose();
  }
}