import 'package:flutter/material.dart';
import 'package:general_hcp_crm/services/task_service.dart';

class AnimatedNotificationBell extends StatefulWidget {
  final VoidCallback? onTap;
  final Color color;
  final double size;

  const AnimatedNotificationBell({
    super.key,
    this.onTap,
    this.color = Colors.white,
    this.size = 24.0,
  });

  @override
  State<AnimatedNotificationBell> createState() => _AnimatedNotificationBellState();
}

class _AnimatedNotificationBellState extends State<AnimatedNotificationBell>
    with TickerProviderStateMixin {
  late AnimationController _bellController;
  late AnimationController _pulseController;
  late Animation<double> _bellAnimation;
  late Animation<double> _pulseAnimation;
  
  int _pendingTasksCount = 0;
  bool _hasNotifications = false;

  @override
  void initState() {
    super.initState();
    
    // Animation de balancement de la cloche
    _bellController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _bellAnimation = Tween<double>(
      begin: -0.1,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _bellController,
      curve: Curves.elasticInOut,
    ));
    
    // Animation de pulsation pour l'indicateur
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _loadPendingTasks();
    _startAnimations();
  }

  Future<void> _loadPendingTasks() async {
    try {
      final taskService = TaskService.instance;
      final pendingTasks = await taskService.getPendingTasks();
      final overdueTasks = await taskService.getOverdueTasks();
      
      setState(() {
        _pendingTasksCount = pendingTasks.length;
        _hasNotifications = pendingTasks.isNotEmpty || overdueTasks.isNotEmpty;
      });
      
      if (_hasNotifications) {
        _startAnimations();
      } else {
        _stopAnimations();
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des tâches: $e');
    }
  }

  void _startAnimations() {
    if (_hasNotifications) {
      _bellController.repeat(reverse: true);
      _pulseController.repeat(reverse: true);
    }
  }

  void _stopAnimations() {
    _bellController.stop();
    _pulseController.stop();
  }

  @override
  void dispose() {
    _bellController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Cloche animée
          AnimatedBuilder(
            animation: _bellAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _bellAnimation.value,
                child: Icon(
                  Icons.notifications,
                  color: widget.color,
                  size: widget.size,
                ),
              );
            },
          ),
          
          // Indicateur de notification avec animation de pulsation
          if (_hasNotifications)
            Positioned(
              right: -2,
              top: -2,
              child: AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.red.withValues(alpha: 0.5),
                            blurRadius: 4,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        _pendingTasksCount > 99 ? '99+' : _pendingTasksCount.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}