import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:uuid/uuid.dart';
import '../models/invoice.dart';
import '../models/order_photo.dart';
import '../services/order_service.dart';

import '../widgets/photo_gallery_widget.dart';
import '../widgets/invoice_selector_widget.dart';

/// Page de création d'une nouvelle commande
class CreateOrderPage extends StatefulWidget {
  final Invoice? duplicateFrom;
  final Invoice? linkedInvoice; // Facture à lier pour pré-remplissage

  const CreateOrderPage({super.key, this.duplicateFrom, this.linkedInvoice});

  @override
  State<CreateOrderPage> createState() => _CreateOrderPageState();
}

class _CreateOrderPageState extends State<CreateOrderPage> {
  final _formKey = GlobalKey<FormState>();
  final OrderService _orderService = OrderService.instance;
  final ImagePicker _imagePicker = ImagePicker();

  // Contrôleurs de texte
  final _clientNameController = TextEditingController();
  final _clientNumberController = TextEditingController();
  final _productsController = TextEditingController();
  final _deliveryLocationController = TextEditingController();
  final _deliveryDetailsController = TextEditingController();
  final _deliveryPriceController = TextEditingController();
  final _advanceController = TextEditingController();
  final _notesController = TextEditingController();

  // État
  bool _isLoading = false;
  final List<OrderPhoto> _photos = [];
  InvoiceStatus _selectedStatus = InvoiceStatus.enAttente;

  // Variables pour la liaison de facture
  bool _showInvoiceSelector = false;
  Invoice? _linkedInvoice;
  String? _selectedInvoiceId;

  @override
  void initState() {
    super.initState();

    // Si on duplique une facture, initialiser les champs
    if (widget.duplicateFrom != null) {
      // Logique de duplication existante si nécessaire
    } else if (widget.linkedInvoice != null) {
      // Si une facture est liée, l'utiliser pour pré-remplir
      _linkedInvoice = widget.linkedInvoice;
      _selectedInvoiceId = widget.linkedInvoice!.id;
      _prefillFromInvoice(widget.linkedInvoice!);
    }
  }

  @override
  void dispose() {
    _clientNameController.dispose();
    _clientNumberController.dispose();
    _productsController.dispose();
    _deliveryLocationController.dispose();
    _deliveryDetailsController.dispose();
    _deliveryPriceController.dispose();
    _advanceController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Nouvelle Commande'),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveOrder,
            child: Text(
              'Enregistrer',
              style: TextStyle(
                color: _isLoading ? Colors.grey : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Section de liaison de facture
                    _buildInvoiceLinkSection(),

                    const SizedBox(height: 24),

                    // Informations client
                    _buildSectionTitle('Informations Client'),
                    _buildClientFields(),

                    const SizedBox(height: 24),

                    // Informations commande
                    _buildSectionTitle('Détails de la Commande'),
                    _buildOrderFields(),

                    const SizedBox(height: 24),

                    // Livraison
                    _buildSectionTitle('Livraison'),
                    _buildDeliveryFields(),

                    const SizedBox(height: 24),

                    // Photos
                    _buildSectionTitle('Photos'),
                    _buildPhotosSection(),

                    const SizedBox(height: 24),

                    // Notes
                    _buildSectionTitle('Notes (optionnel)'),
                    _buildNotesField(),
                  ],
                ),
              ),
            ),

            // Bouton de sauvegarde en bas
            if (_isLoading)
              const Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              )
            else
              Padding(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _saveOrder,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[700],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Créer la Commande',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Construire un titre de section
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  /// Construire la section de liaison de facture
  Widget _buildInvoiceLinkSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec bouton
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.link, color: Colors.blue[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'Lier à une facture',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                ElevatedButton.icon(
                  onPressed:
                      _linkedInvoice == null ? _toggleInvoiceSelector : null,
                  icon: Icon(_showInvoiceSelector ? Icons.close : Icons.search),
                  label: Text(_showInvoiceSelector ? 'Fermer' : 'Sélectionner'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Text(
              'Pré-remplissez le formulaire avec les données d\'une facture existante',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),

            // Affichage de la facture liée
            if (_linkedInvoice != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Facture liée: ${_linkedInvoice!.invoiceNumber}',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            'Client: ${_linkedInvoice!.clientName}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: _unlinkInvoice,
                      icon: const Icon(Icons.close),
                      tooltip: 'Supprimer la liaison',
                      color: Colors.red[600],
                    ),
                  ],
                ),
              ),
            ],

            // Sélecteur de facture
            if (_showInvoiceSelector && _linkedInvoice == null) ...[
              const SizedBox(height: 16),
              InvoiceSelectorWidget(
                onInvoiceSelected: _selectInvoice,
                selectedInvoiceId: _selectedInvoiceId,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Construire les champs client
  Widget _buildClientFields() {
    return Column(
      children: [
        TextFormField(
          controller: _clientNameController,
          decoration: const InputDecoration(
            labelText: 'Nom du client *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Le nom du client est requis';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _clientNumberController,
          decoration: const InputDecoration(
            labelText: 'Numéro de téléphone *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.phone),
          ),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Le numéro de téléphone est requis';
            }
            return null;
          },
        ),
      ],
    );
  }

  /// Construire les champs de commande
  Widget _buildOrderFields() {
    return Column(
      children: [
        TextFormField(
          controller: _productsController,
          decoration: const InputDecoration(
            labelText: 'Produits/Articles *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.shopping_cart),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Les produits sont requis';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<InvoiceStatus>(
          value: _selectedStatus,
          decoration: const InputDecoration(
            labelText: 'Statut',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.flag),
          ),
          items:
              InvoiceStatus.values.map((status) {
                String label;
                switch (status) {
                  case InvoiceStatus.enAttente:
                    label = 'En attente';
                    break;
                  case InvoiceStatus.enCours:
                    label = 'En cours';
                    break;
                  case InvoiceStatus.terminee:
                    label = 'Terminée';
                    break;
                  case InvoiceStatus.enRetard:
                    label = 'En retard';
                    break;
                  case InvoiceStatus.annulee:
                    label = 'Annulée';
                    break;
                }
                return DropdownMenuItem(value: status, child: Text(label));
              }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() => _selectedStatus = value);
            }
          },
        ),
      ],
    );
  }

  /// Construire les champs de livraison
  Widget _buildDeliveryFields() {
    return Column(
      children: [
        TextFormField(
          controller: _deliveryLocationController,
          decoration: const InputDecoration(
            labelText: 'Lieu de livraison *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.location_on),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Le lieu de livraison est requis';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _deliveryDetailsController,
          decoration: const InputDecoration(
            labelText: 'Détails de livraison',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.info_outline),
          ),
          maxLines: 2,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _deliveryPriceController,
                decoration: const InputDecoration(
                  labelText: 'Frais de livraison',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.local_shipping),
                  suffixText: 'FCFA',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _advanceController,
                decoration: const InputDecoration(
                  labelText: 'Avance',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.payment),
                  suffixText: 'FCFA',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Construire la section photos
  Widget _buildPhotosSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _addPhoto,
                icon: const Icon(Icons.camera_alt),
                label: const Text('Prendre une photo'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _pickFromGallery,
                icon: const Icon(Icons.photo_library),
                label: const Text('Galerie'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),

        if (_photos.isNotEmpty) ...[
          const SizedBox(height: 16),
          PhotoGalleryWidget(
            photos: _photos,
            onPhotoTap: _editPhoto,
            onPhotoDelete: _deletePhoto,
          ),
        ],
      ],
    );
  }

  /// Construire le champ notes
  Widget _buildNotesField() {
    return TextFormField(
      controller: _notesController,
      decoration: const InputDecoration(
        labelText: 'Notes additionnelles',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.note),
      ),
      maxLines: 3,
    );
  }

  /// Ajouter une photo depuis l'appareil photo
  Future<void> _addPhoto() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (image != null) {
        await _processNewPhoto(image.path);
      }
    } catch (e) {
      debugPrint('❌ Erreur prise de photo: $e');
      _showErrorSnackBar('Erreur lors de la prise de photo');
    }
  }

  /// Sélectionner une photo depuis la galerie
  Future<void> _pickFromGallery() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        await _processNewPhoto(image.path);
      }
    } catch (e) {
      debugPrint('❌ Erreur sélection photo: $e');
      _showErrorSnackBar('Erreur lors de la sélection de photo');
    }
  }

  /// Traiter une nouvelle photo avec OCR
  Future<void> _processNewPhoto(String imagePath) async {
    setState(() => _isLoading = true);

    try {
      // Créer un objet photo temporaire pour l'affichage
      final tempPhoto = OrderPhoto(
        filePath: imagePath,
        title: 'Photo ${_photos.length + 1}',
      );

      setState(() {
        _photos.add(tempPhoto);
        _isLoading = false;
      });

      _showSuccessSnackBar('Photo ajoutée avec succès');
    } catch (e) {
      setState(() => _isLoading = false);
      debugPrint('❌ Erreur traitement photo: $e');
      _showErrorSnackBar('Erreur lors du traitement de la photo');
    }
  }

  /// Éditer une photo
  void _editPhoto(OrderPhoto photo) {
    // Contrôleurs pour les champs d'édition
    final titleController = TextEditingController(text: photo.title ?? '');
    final descriptionController = TextEditingController(
      text: photo.description ?? '',
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Éditer la photo'),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Affichage de l'image
                  Container(
                    height: 200,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: _buildImageWidget(photo.filePath),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Champ titre
                  TextFormField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: 'Titre',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.title),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Champ description
                  TextFormField(
                    controller: descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 3,
                  ),

                  // Affichage des données OCR si disponibles
                  if (photo.ocrData.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'Données détectées:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children:
                            photo.ocrData.entries
                                .map(
                                  (entry) => Text(
                                    '${entry.key}: ${entry.value}',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                )
                                .toList(),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  titleController.dispose();
                  descriptionController.dispose();
                  Navigator.pop(context);
                },
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  // Sauvegarder les modifications
                  final updatedPhoto = photo.copyWith(
                    title:
                        titleController.text.trim().isEmpty
                            ? null
                            : titleController.text.trim(),
                    description:
                        descriptionController.text.trim().isEmpty
                            ? null
                            : descriptionController.text.trim(),
                    updatedAt: DateTime.now(),
                  );

                  // Mettre à jour la photo dans la liste
                  setState(() {
                    final index = _photos.indexWhere((p) => p.id == photo.id);
                    if (index != -1) {
                      _photos[index] = updatedPhoto;
                    }
                  });

                  titleController.dispose();
                  descriptionController.dispose();
                  Navigator.pop(context);
                  _showSuccessSnackBar('Photo mise à jour avec succès');
                },
                child: const Text('Sauvegarder'),
              ),
            ],
          ),
    );
  }

  /// Supprimer une photo
  void _deletePhoto(OrderPhoto photo) {
    setState(() {
      _photos.removeWhere((p) => p.id == photo.id);
    });
    _showSuccessSnackBar('Photo supprimée');
  }

  /// Sauvegarder la commande
  Future<void> _saveOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // Calculer les montants
      final deliveryPrice =
          double.tryParse(_deliveryPriceController.text) ?? 0.0;
      final advance = double.tryParse(_advanceController.text) ?? 0.0;
      final subtotal =
          deliveryPrice; // Pour l'instant, juste les frais de livraison
      final total = subtotal;

      // Créer la commande
      final order = Invoice(
        id: const Uuid().v4(),
        clientName: _clientNameController.text.trim(),
        clientNumber: _clientNumberController.text.trim(),
        products: _productsController.text.trim(),
        items: [], // Pas d'items détaillés pour l'instant
        deliveryLocation: _deliveryLocationController.text.trim(),
        deliveryDetails:
            _deliveryDetailsController.text.trim().isEmpty
                ? null
                : _deliveryDetailsController.text.trim(),
        deliveryPrice: deliveryPrice,
        advance: advance,
        subtotal: subtotal,
        total: total,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        status: _selectedStatus,
        createdAt: DateTime.now(),
        linkedInvoiceId: _linkedInvoice?.id, // Ajouter l'ID de la facture liée
      );

      // Sauvegarder la commande
      final orderId = await _orderService.createOrder(order);

      // Ajouter les photos à la commande
      for (final photo in _photos) {
        await _orderService.addPhotoToOrder(
          orderId,
          photo.filePath,
          title: photo.title,
          description: photo.description,
        );
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Commande créée avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      debugPrint('❌ Erreur sauvegarde commande: $e');
      _showErrorSnackBar('Erreur lors de la sauvegarde: $e');
    }
  }

  /// Afficher un message d'erreur
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  /// Afficher un message de succès
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  /// Afficher un message d'information
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.blue),
    );
  }

  /// Basculer l'affichage du sélecteur de facture
  void _toggleInvoiceSelector() {
    setState(() {
      _showInvoiceSelector = !_showInvoiceSelector;
    });
  }

  /// Sélectionner une facture pour pré-remplissage
  void _selectInvoice(Invoice invoice) {
    setState(() {
      _linkedInvoice = invoice;
      _selectedInvoiceId = invoice.id;
      _showInvoiceSelector = false;
    });

    // Pré-remplir le formulaire avec les données de la facture
    _prefillFromInvoice(invoice);

    _showInfoSnackBar(
      'Formulaire pré-rempli avec la facture ${invoice.invoiceNumber}',
    );
  }

  /// Pré-remplir le formulaire avec les données d'une facture
  void _prefillFromInvoice(Invoice invoice) {
    setState(() {
      // Pré-remplir les champs de base
      _clientNameController.text = invoice.clientName;
      _clientNumberController.text = invoice.clientNumber;
      _productsController.text = invoice.products;
      _deliveryDetailsController.text = invoice.deliveryDetails ?? '';
      _notesController.text = invoice.notes ?? '';

      // Pré-remplir la zone de livraison
      _deliveryLocationController.text = invoice.deliveryLocation;

      // Pré-remplir les articles (pas implémenté dans cette version simplifiée)
      // Les articles seront gérés via le champ produits pour l'instant
    });
  }

  /// Supprimer la liaison avec la facture
  void _unlinkInvoice() {
    setState(() {
      _linkedInvoice = null;
      _selectedInvoiceId = null;
    });

    _showInfoSnackBar('Liaison avec la facture supprimée');
  }

  /// Construire l'image compatible web/mobile
  Widget _buildImageWidget(String imagePath) {
    if (kIsWeb) {
      // Sur le web, afficher un placeholder
      return Container(
        color: Colors.grey[200],
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.photo, size: 32, color: Colors.grey[400]),
            const SizedBox(height: 4),
            Text(
              'Photo\n(Web)',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    } else {
      // Sur mobile, utiliser Image.file
      try {
        if (File(imagePath).existsSync()) {
          return Image.file(
            File(imagePath),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Colors.grey[200],
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.broken_image, size: 32, color: Colors.grey[400]),
                    const SizedBox(height: 4),
                    Text(
                      'Image\nindisponible',
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                    ),
                  ],
                ),
              );
            },
          );
        } else {
          return Container(
            color: Colors.grey[200],
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.photo, size: 32, color: Colors.grey[400]),
                const SizedBox(height: 4),
                Text(
                  'Fichier\nintrouvable',
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                ),
              ],
            ),
          );
        }
      } catch (e) {
        return Container(
          color: Colors.grey[200],
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 32, color: Colors.red[400]),
              const SizedBox(height: 4),
              Text(
                'Erreur\nimage',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12, color: Colors.red[500]),
              ),
            ],
          ),
        );
      }
    }
  }
}
