import 'package:flutter/material.dart';

/// Statuts de projet
enum ProjectStatus {
  active('Actif'),
  onHold('En pause'),
  completed('Terminé'),
  archived('Archivé');

  const ProjectStatus(this.displayName);
  final String displayName;
}

/// Priorités de tâche
enum TaskPriority {
  low('Faible', Colors.green),
  medium('Moyenne', Colors.orange),
  high('Élevée', Colors.red),
  urgent('Urgente', Colors.purple);

  const TaskPriority(this.displayName, this.color);
  final String displayName;
  final Color color;
}

/// Statuts de tâche
enum TaskStatus {
  todo('À faire'),
  inProgress('En cours'),
  inReview('En révision'),
  completed('Terminée'),
  blocked('Bloquée');

  const TaskStatus(this.displayName);
  final String displayName;
}

/// Règle de récurrence
class RecurrenceRule {
  final RecurrenceType type;
  final int interval;
  final List<int>? daysOfWeek; // 1-7 (lundi-dimanche)
  final int? dayOfMonth;
  final DateTime? endDate;
  final int? maxOccurrences;

  RecurrenceRule({
    required this.type,
    required this.interval,
    this.daysOfWeek,
    this.dayOfMonth,
    this.endDate,
    this.maxOccurrences,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'interval': interval,
      'daysOfWeek': daysOfWeek,
      'dayOfMonth': dayOfMonth,
      'endDate': endDate?.toIso8601String(),
      'maxOccurrences': maxOccurrences,
    };
  }

  factory RecurrenceRule.fromJson(Map<String, dynamic> json) {
    return RecurrenceRule(
      type: RecurrenceType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => RecurrenceType.daily,
      ),
      interval: json['interval'],
      daysOfWeek:
          json['daysOfWeek'] != null
              ? List<int>.from(json['daysOfWeek'])
              : null,
      dayOfMonth: json['dayOfMonth'],
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      maxOccurrences: json['maxOccurrences'],
    );
  }
}

/// Types de récurrence
enum RecurrenceType {
  daily('Quotidienne'),
  weekly('Hebdomadaire'),
  monthly('Mensuelle'),
  yearly('Annuelle');

  const RecurrenceType(this.displayName);
  final String displayName;
}

/// Modèle de projet (niveau supérieur)
class Project {
  final String id;
  final String name;
  final String description;
  final Color color;
  final String? iconData;
  final ProjectStatus status;
  final DateTime createdAt;
  final DateTime? dueDate;
  final String? ownerId;
  final List<String> memberIds;
  final List<String> followerIds;
  final Map<String, dynamic> customFields;

  Project({
    required this.id,
    required this.name,
    required this.description,
    required this.color,
    this.iconData,
    required this.status,
    required this.createdAt,
    this.dueDate,
    this.ownerId,
    this.memberIds = const [],
    this.followerIds = const [],
    this.customFields = const {},
  });

  Project copyWith({
    String? id,
    String? name,
    String? description,
    Color? color,
    String? iconData,
    ProjectStatus? status,
    DateTime? createdAt,
    DateTime? dueDate,
    String? ownerId,
    List<String>? memberIds,
    List<String>? followerIds,
    Map<String, dynamic>? customFields,
  }) {
    return Project(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      color: color ?? this.color,
      iconData: iconData ?? this.iconData,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      dueDate: dueDate ?? this.dueDate,
      ownerId: ownerId ?? this.ownerId,
      memberIds: memberIds ?? this.memberIds,
      followerIds: followerIds ?? this.followerIds,
      customFields: customFields ?? this.customFields,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'color': color.toARGB32(),
      'iconData': iconData,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'ownerId': ownerId,
      'memberIds': memberIds,
      'followerIds': followerIds,
      'customFields': customFields,
    };
  }

  factory Project.fromJson(Map<String, dynamic> json) {
    return Project(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      color: Color(json['color']),
      iconData: json['iconData'],
      status: ProjectStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => ProjectStatus.active,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      ownerId: json['ownerId'],
      memberIds: List<String>.from(json['memberIds'] ?? []),
      followerIds: List<String>.from(json['followerIds'] ?? []),
      customFields: Map<String, dynamic>.from(json['customFields'] ?? {}),
    );
  }
}

/// Modèle de section (groupement de tâches dans un projet)
class TaskSection {
  final String id;
  final String projectId;
  final String name;
  final String description;
  final int order;
  final DateTime createdAt;

  TaskSection({
    required this.id,
    required this.projectId,
    required this.name,
    required this.description,
    required this.order,
    required this.createdAt,
  });

  TaskSection copyWith({
    String? id,
    String? projectId,
    String? name,
    String? description,
    int? order,
    DateTime? createdAt,
  }) {
    return TaskSection(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      name: name ?? this.name,
      description: description ?? this.description,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'name': name,
      'description': description,
      'order': order,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TaskSection.fromJson(Map<String, dynamic> json) {
    return TaskSection(
      id: json['id'],
      projectId: json['projectId'],
      name: json['name'],
      description: json['description'],
      order: json['order'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Modèle de tâche avancée
class AdvancedTask {
  final String id;
  final String projectId;
  final String? sectionId;
  final String? parentTaskId; // Pour les sous-tâches
  final String title;
  final String description;
  final TaskPriority priority;
  final TaskStatus status;
  final DateTime createdAt;
  final DateTime? startDate;
  final DateTime? dueDate;
  final String? assigneeId;
  final List<String> followerIds;
  final List<String> tags;
  final List<String> dependencyIds; // Tâches dont celle-ci dépend
  final RecurrenceRule? recurrence;
  final Map<String, dynamic> customFields;
  final double? estimatedHours;
  final double? actualHours;
  final int order;

  AdvancedTask({
    required this.id,
    required this.projectId,
    this.sectionId,
    this.parentTaskId,
    required this.title,
    required this.description,
    required this.priority,
    required this.status,
    required this.createdAt,
    this.startDate,
    this.dueDate,
    this.assigneeId,
    this.followerIds = const [],
    this.tags = const [],
    this.dependencyIds = const [],
    this.recurrence,
    this.customFields = const {},
    this.estimatedHours,
    this.actualHours,
    required this.order,
  });

  bool get isCompleted => status == TaskStatus.completed;
  bool get isOverdue =>
      dueDate != null && dueDate!.isBefore(DateTime.now()) && !isCompleted;

  AdvancedTask copyWith({
    String? id,
    String? projectId,
    String? sectionId,
    String? parentTaskId,
    String? title,
    String? description,
    TaskPriority? priority,
    TaskStatus? status,
    DateTime? createdAt,
    DateTime? startDate,
    DateTime? dueDate,
    String? assigneeId,
    List<String>? followerIds,
    List<String>? tags,
    List<String>? dependencyIds,
    RecurrenceRule? recurrence,
    Map<String, dynamic>? customFields,
    double? estimatedHours,
    double? actualHours,
    int? order,
  }) {
    return AdvancedTask(
      id: id ?? this.id,
      projectId: projectId ?? this.projectId,
      sectionId: sectionId ?? this.sectionId,
      parentTaskId: parentTaskId ?? this.parentTaskId,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      startDate: startDate ?? this.startDate,
      dueDate: dueDate ?? this.dueDate,
      assigneeId: assigneeId ?? this.assigneeId,
      followerIds: followerIds ?? this.followerIds,
      tags: tags ?? this.tags,
      dependencyIds: dependencyIds ?? this.dependencyIds,
      recurrence: recurrence ?? this.recurrence,
      customFields: customFields ?? this.customFields,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      actualHours: actualHours ?? this.actualHours,
      order: order ?? this.order,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectId': projectId,
      'sectionId': sectionId,
      'parentTaskId': parentTaskId,
      'title': title,
      'description': description,
      'priority': priority.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'startDate': startDate?.toIso8601String(),
      'dueDate': dueDate?.toIso8601String(),
      'assigneeId': assigneeId,
      'followerIds': followerIds,
      'tags': tags,
      'dependencyIds': dependencyIds,
      'recurrence': recurrence?.toJson(),
      'customFields': customFields,
      'estimatedHours': estimatedHours,
      'actualHours': actualHours,
      'order': order,
    };
  }

  factory AdvancedTask.fromJson(Map<String, dynamic> json) {
    return AdvancedTask(
      id: json['id'],
      projectId: json['projectId'],
      sectionId: json['sectionId'],
      parentTaskId: json['parentTaskId'],
      title: json['title'],
      description: json['description'],
      priority: TaskPriority.values.firstWhere(
        (p) => p.name == json['priority'],
        orElse: () => TaskPriority.medium,
      ),
      status: TaskStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => TaskStatus.todo,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      startDate:
          json['startDate'] != null ? DateTime.parse(json['startDate']) : null,
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      assigneeId: json['assigneeId'],
      followerIds: List<String>.from(json['followerIds'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
      dependencyIds: List<String>.from(json['dependencyIds'] ?? []),
      recurrence:
          json['recurrence'] != null
              ? RecurrenceRule.fromJson(json['recurrence'])
              : null,
      customFields: Map<String, dynamic>.from(json['customFields'] ?? {}),
      estimatedHours: json['estimatedHours']?.toDouble(),
      actualHours: json['actualHours']?.toDouble(),
      order: json['order'] ?? 0,
    );
  }
}
