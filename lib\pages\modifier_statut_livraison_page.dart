import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/colis.dart';
import '../services/colis_service.dart';
import 'package:image_picker/image_picker.dart';
import 'marquer_livraison_page.dart';

class ModifierStatutLivraisonPage extends StatefulWidget {
  final Colis colis;

  const ModifierStatutLivraisonPage({super.key, required this.colis});

  @override
  State<ModifierStatutLivraisonPage> createState() =>
      _ModifierStatutLivraisonPageState();
}

class _ModifierStatutLivraisonPageState
    extends State<ModifierStatutLivraisonPage> {
  final ColisService _colisService = ColisService.instance;

  /// Méthode helper pour afficher les images de manière compatible web/mobile
  Widget _buildImageWidget(String imagePath) {
    if (kIsWeb) {
      // Sur le web, utiliser Image.network ou un placeholder
      if (imagePath.isNotEmpty &&
          (imagePath.startsWith('http') || imagePath.startsWith('assets/'))) {
        return imagePath.startsWith('assets/')
            ? Image.asset(imagePath, fit: BoxFit.cover)
            : Image.network(
              imagePath,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return const Icon(
                  Icons.inventory_2,
                  color: Colors.grey,
                  size: 40,
                );
              },
            );
      } else {
        return const Icon(Icons.inventory_2, color: Colors.grey, size: 40);
      }
    } else {
      // Sur mobile, utiliser Image.file pour les fichiers locaux
      if (imagePath.isNotEmpty &&
          !imagePath.startsWith('http') &&
          !imagePath.startsWith('assets/')) {
        try {
          return Image.file(
            File(imagePath),
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return const Icon(
                Icons.broken_image,
                color: Colors.grey,
                size: 40,
              );
            },
          );
        } catch (e) {
          return const Icon(Icons.broken_image, color: Colors.grey, size: 40);
        }
      } else if (imagePath.startsWith('assets/')) {
        return Image.asset(imagePath, fit: BoxFit.cover);
      } else {
        return const Icon(Icons.inventory_2, color: Colors.grey, size: 40);
      }
    }
  }

  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  final DateFormat _dateFormat = DateFormat('dd/MM/yyyy à HH:mm');

  StatutLivraison? _nouveauStatut;
  bool _isLoading = false;
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nouveauStatut = widget.colis.statut;
    _notesController.text = widget.colis.notes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _sauvegarderModifications() async {
    if (_nouveauStatut == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Veuillez sélectionner un statut')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Mettre à jour le statut
      await _colisService.updateStatus(widget.colis.id, _nouveauStatut!);

      // Mettre à jour les notes si elles ont changé
      if (_notesController.text.trim() != (widget.colis.notes ?? '')) {
        final colisModifie = widget.colis.copyWith(
          notes:
              _notesController.text.trim().isEmpty
                  ? null
                  : _notesController.text.trim(),
        );
        await _colisService.updateColis(colisModifie);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Statut modifié avec succès'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(_nouveauStatut);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la modification: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _marquerAvecDatePersonnalisee() async {
    final result = await Navigator.of(context).push<StatutLivraison>(
      MaterialPageRoute(
        builder: (context) => MarquerLivraisonPage(colis: widget.colis),
      ),
    );

    if (result != null && mounted) {
      Navigator.of(context).pop(result);
    }
  }

  Widget _buildStatutOption(StatutLivraison statut) {
    final isSelected = _nouveauStatut == statut;
    final isCurrentStatus = widget.colis.statut == statut;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              _nouveauStatut = statut;
            });
          },
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color:
                  isSelected
                      ? Color(statut.colorValue).withValues(alpha: 0.1)
                      : Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color:
                    isSelected ? Color(statut.colorValue) : Colors.grey[300]!,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                // Emoji et couleur
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Color(statut.colorValue).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Center(
                    child: Text(
                      statut.emoji,
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Libellé et description
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            statut.libelle,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color:
                                  isSelected
                                      ? Color(statut.colorValue)
                                      : Colors.black87,
                            ),
                          ),
                          if (isCurrentStatus) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue[100],
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                'Actuel',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue[800],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getStatutDescription(statut),
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),

                // Indicateur de sélection
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Color(statut.colorValue),
                    size: 24,
                  )
                else
                  Icon(
                    Icons.radio_button_unchecked,
                    color: Colors.grey[400],
                    size: 24,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getStatutDescription(StatutLivraison statut) {
    switch (statut) {
      case StatutLivraison.livree:
        return 'Le colis a été livré avec succès au client';
      case StatutLivraison.enRetard:
        return 'La livraison a pris du retard par rapport au planning';
      case StatutLivraison.annulee:
        return 'La livraison a été annulée (client absent, refus, etc.)';
      case StatutLivraison.retour:
        return 'Le colis est retourné (refus client, adresse incorrecte, etc.)';
      case StatutLivraison.enCours:
        return 'La livraison est actuellement en cours';
      case StatutLivraison.brouillon:
        return 'Le colis est en brouillon et nécessite des informations complémentaires';
    }
  }

  Widget _buildColisInfo() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.inventory_2, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  'Informations du colis',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Photo
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: _buildImageWidget(widget.colis.photoPath),
                  ),
                ),

                const SizedBox(width: 16),

                // Informations
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.colis.libelle,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      _buildInfoRow(
                        'Client',
                        widget.colis.nomClient ?? 'Non renseigné',
                      ),
                      _buildInfoRow('Téléphone', widget.colis.numeroClient),
                      _buildInfoRow('Zone', widget.colis.zoneLivraison),
                      _buildInfoRow(
                        'Reste à payer',
                        '${_currencyFormat.format(widget.colis.resteAPayer)} FCFA',
                      ),
                      _buildInfoRow(
                        'Frais livraison',
                        '${_currencyFormat.format(widget.colis.fraisLivraison)} FCFA',
                      ),
                      _buildInfoRow(
                        'Ajouté le',
                        _dateFormat.format(widget.colis.dateAjout),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool isBrouillon = _nouveauStatut == StatutLivraison.brouillon;
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Modifier le statut',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Modification en cours...'),
                  ],
                ),
              )
              : isBrouillon
              ? _buildBrouillonForm(context)
              : Column(
                children: [
                  Expanded(
                    child: ListView(
                      padding: const EdgeInsets.all(16),
                      children: [
                        // Informations du colis
                        _buildColisInfo(),
                        const SizedBox(height: 16),
                        // Sélection du statut
                        Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(Icons.flag, color: Colors.blue[600]),
                                    const SizedBox(width: 8),
                                    const Text(
                                      'Nouveau statut',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                // Options de statut
                                ...StatutLivraison.values.map(
                                  (statut) => _buildStatutOption(statut),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Notes
                        Card(
                          elevation: 2,
                          child: Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Icon(Icons.note, color: Colors.blue[600]),
                                    const SizedBox(width: 8),
                                    const Text(
                                      'Notes',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                TextFormField(
                                  controller: _notesController,
                                  maxLines: 3,
                                  decoration: InputDecoration(
                                    hintText:
                                        'Ajouter des notes sur cette livraison...',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    filled: true,
                                    fillColor: Colors.grey[50],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                  // Boutons d'action
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.2),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: const Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Bouton spécial pour marquer comme livré avec date personnalisée
                        if (widget.colis.statut != StatutLivraison.livree)
                          Container(
                            width: double.infinity,
                            margin: const EdgeInsets.only(bottom: 12),
                            child: ElevatedButton.icon(
                              onPressed: _marquerAvecDatePersonnalisee,
                              icon: const Icon(Icons.schedule),
                              label: const Text(
                                'Marquer comme livré (date personnalisée)',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green[600],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                ),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                          ),

                        // Boutons normaux
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => Navigator.of(context).pop(),
                                style: OutlinedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: const Text(
                                  'Annuler',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              flex: 2,
                              child: ElevatedButton(
                                onPressed:
                                    _nouveauStatut != null
                                        ? _sauvegarderModifications
                                        : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      _nouveauStatut != null
                                          ? Color(_nouveauStatut!.colorValue)
                                          : Colors.grey,
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    if (_nouveauStatut != null) ...[
                                      Text(_nouveauStatut!.emoji),
                                      const SizedBox(width: 8),
                                    ],
                                    const Text(
                                      'Confirmer',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  // Ajoute ce widget pour le mode Brouillon
  Widget _buildBrouillonForm(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    final libelleController = TextEditingController(text: widget.colis.libelle);
    final numeroClientController = TextEditingController(
      text: widget.colis.numeroClient,
    );
    final nomClientController = TextEditingController(
      text: widget.colis.nomClient ?? '',
    );
    final resteAPayerController = TextEditingController(
      text: widget.colis.resteAPayer.toString(),
    );
    final adresseLivraisonController = TextEditingController(
      text: widget.colis.adresseLivraison ?? '',
    );
    final notesController = TextEditingController(
      text: widget.colis.notes ?? '',
    );
    final fraisLivraisonController = TextEditingController(
      text: widget.colis.fraisLivraison.toString(),
    );
    String? selectedZone = widget.colis.zoneLivraison;
    String? newImagePath = widget.colis.photoPath;
    bool isSaving = false;
    final ImagePicker picker = ImagePicker();

    Future<void> prendrePhoto() async {
      try {
        final XFile? image = await picker.pickImage(
          source: ImageSource.camera,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 85,
        );
        if (image != null) {
          newImagePath = image.path;
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur lors de la prise de photo: $e')),
          );
        }
      }
    }

    Future<void> choisirImage() async {
      try {
        final XFile? image = await picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 85,
        );
        if (image != null) {
          newImagePath = image.path;
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur lors de la sélection d\'image: $e')),
          );
        }
      }
    }

    void supprimerImage() {
      newImagePath = '';
    }

    void onZoneChanged(String? zone) {
      if (zone != null) {
        selectedZone = zone;
        fraisLivraisonController.text =
            ZoneLivraison.getPrixLivraison(zone).toString();
      }
    }

    Future<void> sauvegarderEdition() async {
      if (!formKey.currentState!.validate()) return;
      if (newImagePath == null || newImagePath!.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Veuillez ajouter une photo du colis')),
        );
        return;
      }
      if (selectedZone == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Veuillez sélectionner une zone de livraison'),
          ),
        );
        return;
      }
      isSaving = true;
      try {
        final colisModifie = widget.colis.copyWith(
          libelle: libelleController.text.trim(),
          photoPath: newImagePath!,
          zoneLivraison: selectedZone!,
          numeroClient: numeroClientController.text.trim(),
          resteAPayer:
              double.tryParse(resteAPayerController.text.replaceAll(',', '')) ??
              0.0,
          fraisLivraison:
              double.tryParse(fraisLivraisonController.text.trim()) ?? 0.0,
          nomClient:
              nomClientController.text.trim().isEmpty
                  ? null
                  : nomClientController.text.trim(),
          adresseLivraison:
              adresseLivraisonController.text.trim().isEmpty
                  ? null
                  : adresseLivraisonController.text.trim(),
          notes:
              notesController.text.trim().isEmpty
                  ? null
                  : notesController.text.trim(),
        );
        await ColisService.instance.mettreAJourColis(colisModifie);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Brouillon modifié avec succès !'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(colisModifie);
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Erreur lors de la modification : $e')),
          );
        }
      } finally {
        isSaving = false;
      }
    }

    return Form(
      key: formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Section photo
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.camera_alt, color: Colors.blue[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'Photo du colis',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Text(' *', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                  const SizedBox(height: 16),
                  if (newImagePath != null && newImagePath!.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      height: 200,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: _buildImageWidget(newImagePath!),
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton.icon(
                          onPressed: prendrePhoto,
                          icon: const Icon(Icons.camera_alt),
                          label: const Text('Reprendre'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[600],
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: supprimerImage,
                          icon: const Icon(Icons.delete),
                          label: const Text('Supprimer'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red[600],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ] else ...[
                    Container(
                      width: double.infinity,
                      height: 150,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                        color: Colors.grey[50],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_a_photo,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Aucune photo sélectionnée',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton.icon(
                          onPressed: prendrePhoto,
                          icon: const Icon(Icons.camera_alt),
                          label: const Text('Prendre photo'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue[600],
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: choisirImage,
                          icon: const Icon(Icons.photo_library),
                          label: const Text('Galerie'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green[600],
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Informations du colis
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.inventory_2, color: Colors.blue[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'Informations du colis',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: libelleController,
                    decoration: InputDecoration(
                      labelText: 'Libellé du colis *',
                      prefixIcon: const Icon(Icons.label),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    validator:
                        (value) =>
                            value == null || value.trim().isEmpty
                                ? 'Ce champ est obligatoire'
                                : null,
                  ),
                  const SizedBox(height: 16),
                  DropdownButtonFormField<String>(
                    value: selectedZone,
                    decoration: InputDecoration(
                      labelText: 'Zone de livraison *',
                      prefixIcon: const Icon(Icons.location_on),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    items:
                        ZoneLivraison.getZonesDisponibles()
                            .map(
                              (zone) => DropdownMenuItem(
                                value: zone,
                                child: Text(zone),
                              ),
                            )
                            .toList(),
                    onChanged: onZoneChanged,
                    validator:
                        (value) =>
                            value == null || value.isEmpty
                                ? 'Veuillez sélectionner une zone'
                                : null,
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: fraisLivraisonController,
                    decoration: InputDecoration(
                      labelText: 'Frais de livraison *',
                      prefixIcon: const Icon(Icons.local_shipping),
                      suffixText: 'FCFA',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final amount = double.tryParse(value);
                        if (amount == null || amount < 0) {
                          return 'Veuillez entrer un montant valide';
                        }
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: adresseLivraisonController,
                    decoration: InputDecoration(
                      labelText: 'Adresse de livraison',
                      prefixIcon: const Icon(Icons.home),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    maxLines: 2,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Informations client
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.person, color: Colors.blue[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'Informations client',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: numeroClientController,
                    decoration: InputDecoration(
                      labelText: 'Numéro du client *',
                      prefixIcon: const Icon(Icons.phone),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        if (value.length < 8) {
                          return 'Le numéro doit contenir au moins 8 chiffres';
                        }
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: nomClientController,
                    decoration: InputDecoration(
                      labelText: 'Nom du client',
                      prefixIcon: const Icon(Icons.person_outline),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Informations financières
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.monetization_on, color: Colors.blue[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'Informations financières',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: resteAPayerController,
                    decoration: InputDecoration(
                      labelText: 'Reste à payer *',
                      prefixIcon: const Icon(Icons.account_balance_wallet),
                      suffixText: 'FCFA',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final amount = double.tryParse(
                          value.replaceAll(',', ''),
                        );
                        if (amount == null || amount < 0) {
                          return 'Veuillez entrer un montant valide';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          // Notes
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.note, color: Colors.blue[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'Notes supplémentaires',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: notesController,
                    decoration: InputDecoration(
                      labelText: 'Notes (optionnel)',
                      prefixIcon: const Icon(Icons.note_outlined),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),
          // Boutons d'action
          Row(
            children: [
              Expanded(
                child: SizedBox(
                  height: 50,
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text(
                      'Annuler',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: SizedBox(
                  height: 50,
                  child: ElevatedButton.icon(
                    onPressed: isSaving ? null : sauvegarderEdition,
                    icon: const Icon(Icons.save),
                    label:
                        isSaving
                            ? const SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Text(
                              'Enregistrer',
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
