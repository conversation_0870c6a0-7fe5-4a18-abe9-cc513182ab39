#!/bin/bash

echo "========================================"
echo "   TEST DE SYNCHRONISATION COMPLÈTE"
echo "========================================"
echo "Ce script vérifie que vos données mobiles"
echo "sont bien synchronisées avec la version web"
echo "========================================"
echo

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Vérifier que Flutter est installé
if ! command -v flutter &> /dev/null; then
    print_error "Flutter n'est pas installé ou pas dans le PATH"
    exit 1
fi

print_success "Flutter détecté"

# Étape 1: Analyse du code
echo
print_info "[1/6] Analyse du code..."
flutter analyze
if [ $? -ne 0 ]; then
    print_error "Problèmes d'analyse détectés - Veuillez corriger avant de continuer"
    exit 1
fi
print_success "Analyse réussie"

# Étape 2: Nettoyage
echo
print_info "[2/6] Nettoyage des builds précédents..."
flutter clean
flutter pub get
print_success "Nettoyage terminé"

# Étape 3: Test de compilation mobile
echo
print_info "[3/6] Test de compilation mobile..."
flutter build apk --debug --target-platform android-arm64
if [ $? -ne 0 ]; then
    print_error "Échec de la compilation mobile"
    exit 1
fi
print_success "Compilation mobile réussie"

# Étape 4: Test de compilation web
echo
print_info "[4/6] Test de compilation web..."
flutter build web --release
if [ $? -ne 0 ]; then
    print_error "Échec de la compilation web"
    exit 1
fi
print_success "Compilation web réussie"

# Étape 5: Vérification des fichiers de synchronisation
echo
print_info "[5/6] Vérification des services de synchronisation..."

# Vérifier que les nouveaux services existent
if [ ! -f "lib/services/unified_sync_service.dart" ]; then
    print_error "Service de synchronisation unifié manquant"
    exit 1
fi

if [ ! -f "lib/services/sync_verification_service.dart" ]; then
    print_error "Service de vérification manquant"
    exit 1
fi

if [ ! -f "lib/pages/sync_diagnostic_page.dart" ]; then
    print_error "Page de diagnostic manquant"
    exit 1
fi

print_success "Tous les services de synchronisation sont présents"

# Étape 6: Démarrage du serveur web pour test
echo
print_info "[6/6] Démarrage du serveur web de test..."
echo
print_info "🌐 INSTRUCTIONS POUR TESTER LA SYNCHRONISATION:"
echo
print_warning "1. MOBILE:"
echo "   • Installez l'APK: build/app/outputs/flutter-apk/app-debug.apk"
echo "   • Ouvrez l'application mobile"
echo "   • Vérifiez que vos données existantes sont présentes"
echo "   • Appuyez sur l'icône 🔄 dans le dashboard pour voir le diagnostic"
echo
print_warning "2. WEB:"
echo "   • Ouvrez votre navigateur sur: http://localhost:8000"
echo "   • Vérifiez que les mêmes données apparaissent"
echo "   • Appuyez sur l'icône 🔄 dans le dashboard pour voir le diagnostic"
echo
print_warning "3. TEST DE SYNCHRONISATION:"
echo "   • Ajoutez une facture sur mobile"
echo "   • Vérifiez qu'elle apparaît sur web (peut prendre quelques secondes)"
echo "   • Ajoutez un produit sur web"
echo "   • Vérifiez qu'il apparaît sur mobile"
echo
print_success "IMPORTANT: Vos données mobiles existantes seront automatiquement"
print_success "synchronisées vers le web lors du premier lancement!"
echo
print_info "Serveur web démarré sur http://localhost:8000"
print_info "Appuyez sur Ctrl+C pour arrêter le serveur"
echo

# Démarrer le serveur web
cd build/web
if command -v python3 &> /dev/null; then
    python3 -m http.server 8000
elif command -v python &> /dev/null; then
    python -m http.server 8000
else
    print_error "Python n'est pas installé - Impossible de démarrer le serveur web"
    print_info "Vous pouvez utiliser n'importe quel serveur web pour servir le dossier build/web"
    exit 1
fi
