import 'package:flutter/foundation.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/services.dart';

/// Service pour gérer les notifications audio
class AudioNotificationService {
  static final AudioNotificationService _instance =
      AudioNotificationService._internal();
  factory AudioNotificationService() => _instance;
  AudioNotificationService._internal();

  late AudioPlayer _audioPlayer;
  bool _isInitialized = false;

  /// Initialise le service audio
  Future<void> initialize() async {
    try {
      _audioPlayer = AudioPlayer();
      _isInitialized = true;
      debugPrint('🔊 Service audio initialisé');
    } catch (e) {
      debugPrint('❌ Erreur initialisation service audio: $e');
      _isInitialized = false;
    }
  }

  /// Joue un son de connexion réussie
  Future<void> playConnectionSuccess() async {
    await _playNotificationSound(NotificationSound.connectionSuccess);
  }

  /// Joue un son de déconnexion
  Future<void> playDisconnection() async {
    await _playNotificationSound(NotificationSound.disconnection);
  }

  /// Joue un son d'erreur
  Future<void> playError() async {
    await _playNotificationSound(NotificationSound.error);
  }

  /// Joue un son de succès d'impression
  Future<void> playPrintSuccess() async {
    await _playNotificationSound(NotificationSound.printSuccess);
  }

  /// Joue un son de test
  Future<void> playTestSound() async {
    await _playNotificationSound(NotificationSound.test);
  }

  /// Méthode privée pour jouer un son spécifique
  Future<void> _playNotificationSound(NotificationSound sound) async {
    if (!_isInitialized) {
      debugPrint('⚠️ Service audio non initialisé');
      return;
    }

    try {
      // Ajouter un timeout pour éviter les blocages
      await Future.any([
        _playSound(sound),
        Future.delayed(const Duration(seconds: 3)),
      ]);
    } catch (e) {
      debugPrint('❌ Erreur lecture son ${sound.name}: $e');
    }
  }

  /// Joue le son selon la plateforme
  Future<void> _playSound(NotificationSound sound) async {
    // Sur le web, on utilise des sons système ou des bips générés
    if (kIsWeb) {
      await _playWebSound(sound);
    } else {
      // Sur mobile/desktop, on peut utiliser des fichiers audio
      await _playAssetSound(sound);
    }
  }

  /// Joue un son sur le web (bips système)
  Future<void> _playWebSound(NotificationSound sound) async {
    try {
      switch (sound) {
        case NotificationSound.connectionSuccess:
          // Double bip pour connexion réussie
          await _playSystemBeep();
          await Future.delayed(const Duration(milliseconds: 200));
          await _playSystemBeep();
          break;
        case NotificationSound.disconnection:
          // Bip long pour déconnexion
          await _playSystemBeep();
          break;
        case NotificationSound.error:
          // Triple bip rapide pour erreur
          for (int i = 0; i < 3; i++) {
            await _playSystemBeep();
            await Future.delayed(const Duration(milliseconds: 100));
          }
          break;
        case NotificationSound.printSuccess:
          // Bip mélodieux pour succès d'impression
          await _playSystemBeep();
          await Future.delayed(const Duration(milliseconds: 150));
          await _playSystemBeep();
          break;
        case NotificationSound.test:
          // Bip simple pour test
          await _playSystemBeep();
          break;
      }
    } catch (e) {
      debugPrint('❌ Erreur son web: $e');
    }
  }

  /// Joue un bip système
  Future<void> _playSystemBeep() async {
    try {
      // Utiliser SystemSound pour les bips système avec timeout
      await SystemSound.play(SystemSoundType.click).timeout(
        const Duration(seconds: 1),
        onTimeout: () {
          debugPrint('⏰ Timeout bip système');
        },
      );
    } catch (e) {
      debugPrint('❌ Erreur bip système: $e');
    }
  }

  /// Joue un son depuis les assets (pour mobile/desktop)
  Future<void> _playAssetSound(NotificationSound sound) async {
    try {
      // Pour l'instant, on utilise aussi les sons système sur mobile
      // Plus tard, on pourra ajouter des fichiers audio personnalisés
      await _playWebSound(sound);
    } catch (e) {
      debugPrint('❌ Erreur son asset: $e');
    }
  }

  /// Nettoie les ressources
  void dispose() {
    if (_isInitialized) {
      _audioPlayer.dispose();
      _isInitialized = false;
    }
  }
}

/// Types de sons de notification
enum NotificationSound {
  connectionSuccess('connection_success'),
  disconnection('disconnection'),
  error('error'),
  printSuccess('print_success'),
  test('test');

  const NotificationSound(this.fileName);
  final String fileName;
}
