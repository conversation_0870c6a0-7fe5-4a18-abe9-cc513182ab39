import 'package:flutter/material.dart';
import '../services/pc_sync_service.dart';
import '../constants/app_colors.dart';

class PCSyncPage extends StatefulWidget {
  const PCSyncPage({super.key});

  @override
  State<PCSyncPage> createState() => _PCSyncPageState();
}

class _PCSyncPageState extends State<PCSyncPage> {
  final PCSyncService _syncService = PCSyncService();
  final TextEditingController _serverController = TextEditingController();
  bool _isScanning = false;
  bool _isConnecting = false;
  bool _isSyncing = false;
  List<String> _foundServers = [];
  Map<String, dynamic>? _serverStats;
  String _syncMessage = '';
  DateTime? _lastSyncTime;

  @override
  void initState() {
    super.initState();
    _serverController.text = _syncService.serverUrl;
    _checkConnection();
  }

  @override
  void dispose() {
    _serverController.dispose();
    super.dispose();
  }

  Future<void> _checkConnection() async {
    setState(() => _isConnecting = true);

    final isConnected = await _syncService.testConnection();

    if (isConnected) {
      _serverStats = await _syncService.getServerStats();
    }

    setState(() => _isConnecting = false);
  }

  Future<void> _scanForServers() async {
    setState(() {
      _isScanning = true;
      _foundServers.clear();
    });

    _foundServers = await _syncService.scanForServers();

    setState(() => _isScanning = false);

    if (_foundServers.isEmpty) {
      _showSnackBar(
        'Aucun serveur CommandTaker trouvé sur le réseau',
        isError: true,
      );
    } else {
      _showSnackBar('${_foundServers.length} serveur(s) trouvé(s)');
    }
  }

  Future<void> _connectToServer(String serverUrl) async {
    _syncService.setServerUrl(serverUrl);
    _serverController.text = serverUrl;
    await _checkConnection();

    if (_syncService.isConnected) {
      _showSnackBar('Connexion établie avec succès');
    } else {
      _showSnackBar('Impossible de se connecter au serveur', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
      ),
    );
  }

  /// Synchroniser les commandes avec le serveur PC
  Future<void> _syncOrders() async {
    setState(() {
      _isSyncing = true;
      _syncMessage = 'Synchronisation en cours...';
    });

    try {
      final result = await _syncService.syncAllOrders();

      if (result['success'] == true) {
        setState(() {
          _syncMessage =
              '✅ ${result['message']}\n'
              'Créées: ${result['created']}, '
              'Mises à jour: ${result['updated']}, '
              'Total: ${result['total']}';
          _lastSyncTime = DateTime.now();
        });

        // Afficher les erreurs s'il y en a
        if (result['errors'] != null && result['errors'].isNotEmpty) {
          final errors = result['errors'] as List;
          setState(() {
            _syncMessage += '\n⚠️ ${errors.length} erreur(s) détectée(s)';
          });
        }

        _showSnackBar('Synchronisation réussie');
      } else {
        setState(() {
          _syncMessage = '❌ Erreur de synchronisation: ${result['error']}';
        });
        _showSnackBar('Erreur de synchronisation', isError: true);
      }
    } catch (e) {
      setState(() {
        _syncMessage = '❌ Erreur de synchronisation: $e';
      });
      _showSnackBar('Erreur de synchronisation', isError: true);
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Synchronisation PC'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildConnectionStatus(),
            const SizedBox(height: 24),
            _buildServerConfiguration(),
            const SizedBox(height: 24),
            _buildNetworkScanner(),
            const SizedBox(height: 24),
            if (_syncService.isConnected && _serverStats != null) ...[
              _buildServerStats(),
              const SizedBox(height: 24),
              _buildSyncActions(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConnectionStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _syncService.isConnected ? Icons.check_circle : Icons.error,
                  color:
                      _syncService.isConnected
                          ? AppColors.success
                          : AppColors.error,
                ),
                const SizedBox(width: 8),
                Text(
                  _syncService.isConnected ? 'Connecté' : 'Déconnecté',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Serveur: ${_syncService.serverUrl}',
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isConnecting ? null : _checkConnection,
                icon:
                    _isConnecting
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.refresh),
                label: Text(
                  _isConnecting ? 'Test en cours...' : 'Tester la connexion',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServerConfiguration() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Configuration du serveur',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _serverController,
              decoration: const InputDecoration(
                labelText: 'URL du serveur',
                hintText: 'http://192.168.1.16:8080',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.computer),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _connectToServer(_serverController.text),
                icon: const Icon(Icons.link),
                label: const Text('Se connecter'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryLight,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkScanner() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Scanner le réseau',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                ElevatedButton.icon(
                  onPressed: _isScanning ? null : _scanForServers,
                  icon:
                      _isScanning
                          ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Icon(Icons.search),
                  label: Text(_isScanning ? 'Scan...' : 'Scanner'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.info,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
            if (_foundServers.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                'Serveurs trouvés:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              ...(_foundServers.map(
                (server) => ListTile(
                  leading: Icon(Icons.computer, color: AppColors.primary),
                  title: Text(server),
                  trailing: IconButton(
                    icon: const Icon(Icons.link),
                    onPressed: () => _connectToServer(server),
                  ),
                  contentPadding: EdgeInsets.zero,
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildServerStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Statistiques du serveur',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total',
                    _serverStats!['total']?.toString() ?? '0',
                    Icons.receipt,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'Terminées',
                    _serverStats!['completed']?.toString() ?? '0',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'En cours',
                    _serverStats!['in_progress']?.toString() ?? '0',
                    Icons.hourglass_empty,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'En retard',
                    _serverStats!['delayed']?.toString() ?? '0',
                    Icons.warning,
                    Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(title, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildSyncActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Actions de Synchronisation',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Bouton de synchronisation des commandes
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isSyncing ? null : _syncOrders,
                icon:
                    _isSyncing
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.sync),
                label: Text(
                  _isSyncing
                      ? 'Synchronisation...'
                      : 'Synchroniser les commandes',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Message de synchronisation
            if (_syncMessage.isNotEmpty) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color:
                      _syncMessage.startsWith('✅')
                          ? AppColors.success.withValues(alpha: 0.1)
                          : AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color:
                        _syncMessage.startsWith('✅')
                            ? AppColors.success.withValues(alpha: 0.3)
                            : AppColors.error.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  _syncMessage,
                  style: TextStyle(
                    color:
                        _syncMessage.startsWith('✅')
                            ? AppColors.success
                            : AppColors.error,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(height: 8),
            ],

            // Dernière synchronisation
            if (_lastSyncTime != null) ...[
              Text(
                'Dernière synchronisation: ${_formatDateTime(_lastSyncTime!)}',
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/'
        '${dateTime.month.toString().padLeft(2, '0')}/'
        '${dateTime.year} à '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
