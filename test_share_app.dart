import 'package:flutter/material.dart';
import 'lib/pages/thermal_printer/print_preview_page.dart';
import 'lib/services/receipt_builder.dart';

void main() {
  runApp(const TestShareApp());
}

class TestShareApp extends StatelessWidget {
  const TestShareApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Test Share App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: const TestPage(),
    );
  }
}

class TestPage extends StatelessWidget {
  const TestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Sélection App de Partage'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Test de la fonctionnalité de sélection d\'application de partage',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Simuler un chemin PDF pour tester
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PrintPreviewPage.fromPdf(
                      pdfPath: '/path/to/test.pdf',
                      config: ReceiptConfig(),
                    ),
                  ),
                );
              },
              child: const Text('Tester avec PDF'),
            ),
            const SizedBox(height: 10),
            ElevatedButton(
              onPressed: () {
                // Tester sans PDF (mode impression normale)
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PrintPreviewPage.fromInvoice(
                      invoice: null, // Test sans facture
                      config: ReceiptConfig(),
                    ),
                  ),
                );
              },
              child: const Text('Tester sans PDF'),
            ),
          ],
        ),
      ),
    );
  }
}
