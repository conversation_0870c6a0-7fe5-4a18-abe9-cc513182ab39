import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Service de cache pour optimiser les performances de l'application
class CacheService {
  static CacheService? _instance;
  static CacheService get instance => _instance ??= CacheService._();
  
  CacheService._();
  
  SharedPreferences? _prefs;
  final Map<String, dynamic> _memoryCache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  
  // Durées de cache par défaut
  static const Duration _defaultCacheDuration = Duration(minutes: 5);
  static const Duration _longCacheDuration = Duration(hours: 1);
  static const Duration _shortCacheDuration = Duration(minutes: 1);
  
  /// Initialise le service de cache
  Future<void> initialize() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
  
  /// Met en cache une valeur avec une clé et une durée
  Future<void> set(
    String key, 
    dynamic value, {
    Duration? duration,
    bool persistToDisk = false,
  }) async {
    final cacheDuration = duration ?? _defaultCacheDuration;
    
    // Cache en mémoire
    _memoryCache[key] = value;
    _cacheTimestamps[key] = DateTime.now();
    
    // Cache sur disque si demandé
    if (persistToDisk) {
      await _ensurePrefsInitialized();
      final jsonValue = jsonEncode({
        'value': value,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'duration': cacheDuration.inMilliseconds,
      });
      await _prefs!.setString('cache_$key', jsonValue);
    }
  }
  
  /// Récupère une valeur du cache
  Future<T?> get<T>(String key, {bool checkDisk = false}) async {
    // Vérifier le cache mémoire d'abord
    if (_memoryCache.containsKey(key)) {
      final timestamp = _cacheTimestamps[key];
      if (timestamp != null && 
          DateTime.now().difference(timestamp) < _defaultCacheDuration) {
        return _memoryCache[key] as T?;
      } else {
        // Expirer le cache mémoire
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
    
    // Vérifier le cache disque si demandé
    if (checkDisk) {
      await _ensurePrefsInitialized();
      final jsonValue = _prefs!.getString('cache_$key');
      if (jsonValue != null) {
        try {
          final data = jsonDecode(jsonValue);
          final timestamp = DateTime.fromMillisecondsSinceEpoch(data['timestamp']);
          final duration = Duration(milliseconds: data['duration']);
          
          if (DateTime.now().difference(timestamp) < duration) {
            // Remettre en cache mémoire
            _memoryCache[key] = data['value'];
            _cacheTimestamps[key] = timestamp;
            return data['value'] as T?;
          } else {
            // Supprimer le cache expiré
            await _prefs!.remove('cache_$key');
          }
        } catch (e) {
          // Erreur de désérialisation, supprimer
          await _prefs!.remove('cache_$key');
        }
      }
    }
    
    return null;
  }
  
  /// Vérifie si une clé existe dans le cache et n'est pas expirée
  bool has(String key) {
    if (_memoryCache.containsKey(key)) {
      final timestamp = _cacheTimestamps[key];
      if (timestamp != null && 
          DateTime.now().difference(timestamp) < _defaultCacheDuration) {
        return true;
      } else {
        _memoryCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
    return false;
  }
  
  /// Supprime une entrée du cache
  Future<void> remove(String key) async {
    _memoryCache.remove(key);
    _cacheTimestamps.remove(key);
    
    await _ensurePrefsInitialized();
    await _prefs!.remove('cache_$key');
  }
  
  /// Nettoie le cache expiré
  Future<void> cleanExpiredCache() async {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    // Nettoyer le cache mémoire
    _cacheTimestamps.forEach((key, timestamp) {
      if (now.difference(timestamp) > _defaultCacheDuration) {
        expiredKeys.add(key);
      }
    });
    
    for (final key in expiredKeys) {
      _memoryCache.remove(key);
      _cacheTimestamps.remove(key);
    }
    
    // Nettoyer le cache disque
    await _ensurePrefsInitialized();
    final keys = _prefs!.getKeys().where((k) => k.startsWith('cache_'));
    for (final key in keys) {
      final jsonValue = _prefs!.getString(key);
      if (jsonValue != null) {
        try {
          final data = jsonDecode(jsonValue);
          final timestamp = DateTime.fromMillisecondsSinceEpoch(data['timestamp']);
          final duration = Duration(milliseconds: data['duration']);
          
          if (now.difference(timestamp) > duration) {
            await _prefs!.remove(key);
          }
        } catch (e) {
          await _prefs!.remove(key);
        }
      }
    }
  }
  
  /// Vide tout le cache
  Future<void> clear() async {
    _memoryCache.clear();
    _cacheTimestamps.clear();
    
    await _ensurePrefsInitialized();
    final keys = _prefs!.getKeys().where((k) => k.startsWith('cache_'));
    for (final key in keys) {
      await _prefs!.remove(key);
    }
  }
  
  /// Obtient les statistiques du cache
  Map<String, dynamic> getStats() {
    return {
      'memoryEntries': _memoryCache.length,
      'oldestEntry': _cacheTimestamps.values.isNotEmpty 
          ? _cacheTimestamps.values.reduce((a, b) => a.isBefore(b) ? a : b)
          : null,
      'newestEntry': _cacheTimestamps.values.isNotEmpty
          ? _cacheTimestamps.values.reduce((a, b) => a.isAfter(b) ? a : b)
          : null,
    };
  }
  
  /// Cache spécialisé pour les listes de produits
  Future<void> cacheProductList(List<dynamic> products) async {
    await set('products_list', products, 
        duration: _longCacheDuration, persistToDisk: true);
  }
  
  Future<List<dynamic>?> getCachedProductList() async {
    return await get<List<dynamic>>('products_list', checkDisk: true);
  }
  
  /// Cache spécialisé pour les statistiques du dashboard
  Future<void> cacheDashboardStats(Map<String, dynamic> stats) async {
    await set('dashboard_stats', stats, 
        duration: _shortCacheDuration, persistToDisk: false);
  }
  
  Future<Map<String, dynamic>?> getCachedDashboardStats() async {
    return await get<Map<String, dynamic>>('dashboard_stats');
  }
  
  /// Cache spécialisé pour les catégories
  Future<void> cacheCategories(List<dynamic> categories) async {
    await set('categories_list', categories, 
        duration: _longCacheDuration, persistToDisk: true);
  }
  
  Future<List<dynamic>?> getCachedCategories() async {
    return await get<List<dynamic>>('categories_list', checkDisk: true);
  }
  
  Future<void> _ensurePrefsInitialized() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
}
