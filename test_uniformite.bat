@echo off
echo ========================================
echo    TEST D'UNIFORMITÉ WEB-MOBILE
echo ========================================
echo.

echo [1/5] Analyse du code...
flutter analyze
if %errorlevel% neq 0 (
    echo ❌ ERREUR: Problèmes d'analyse détectés
    pause
    exit /b 1
)
echo ✅ Analyse réussie
echo.

echo [2/5] Construction version mobile (debug)...
flutter build apk --debug
if %errorlevel% neq 0 (
    echo ❌ ERREUR: Échec de la construction mobile
    pause
    exit /b 1
)
echo ✅ Construction mobile réussie
echo.

echo [3/5] Construction version web...
flutter build web --release
if %errorlevel% neq 0 (
    echo ❌ ERREUR: Échec de la construction web
    pause
    exit /b 1
)
echo ✅ Construction web réussie
echo.

echo [4/5] Vérification des fichiers générés...
if not exist "build\web" (
    echo ❌ ERREUR: Dossier build\web manquant
    pause
    exit /b 1
)

if not exist "build\web\index.html" (
    echo ❌ ERREUR: Fichier index.html manquant
    pause
    exit /b 1
)

if not exist "build\app\outputs\flutter-apk" (
    echo ❌ ERREUR: Dossier APK manquant
    pause
    exit /b 1
)
echo ✅ Fichiers générés présents
echo.

echo [5/5] Test de démarrage local web...
echo Démarrage du serveur web local sur le port 8000...
echo Vous pouvez tester l'application web à l'adresse:
echo 👉 http://localhost:8000
echo.
echo Pour arrêter le serveur, appuyez sur Ctrl+C
echo.
cd build\web
python -m http.server 8000
