import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../services/legacy_data_migration_service.dart';
import '../constants/app_colors.dart';

class LegacyMigrationPage extends StatefulWidget {
  const LegacyMigrationPage({super.key});

  @override
  State<LegacyMigrationPage> createState() => _LegacyMigrationPageState();
}

class _LegacyMigrationPageState extends State<LegacyMigrationPage> {
  final _migrationService = LegacyDataMigrationService.instance;
  bool _isLoading = false;
  String? _selectedFilePath;
  MigrationResult? _lastResult;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Migration des Données Legacy'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildInfoCard(),
            const SizedBox(height: 20),
            _buildFileSelectionCard(),
            const SizedBox(height: 20),
            _buildMigrationButton(),
            const SizedBox(height: 20),
            if (_lastResult != null) _buildResultCard(),
          ],
        ),
      ),
    );
  }
  
  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Information sur la Migration',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Cette fonctionnalité permet de migrer vos anciennes données depuis un fichier JSON de sauvegarde vers le nouveau format de l\'application.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 8),
            const Text(
              'Types de données supportés:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            _buildDataTypeChip('Produits'),
            _buildDataTypeChip('Catégories'),
            _buildDataTypeChip('Factures'),
            _buildDataTypeChip('Tâches'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDataTypeChip(String label) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green, size: 16),
          const SizedBox(width: 8),
          Text(label, style: const TextStyle(fontSize: 13)),
        ],
      ),
    );
  }
  
  Widget _buildFileSelectionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.folder_open, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Sélection du Fichier',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_selectedFilePath != null)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Fichier sélectionné:\n${_selectedFilePath!.split('/').last}',
                        style: const TextStyle(fontSize: 13),
                      ),
                    ),
                  ],
                ),
              )
            else
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.upload_file, color: Colors.grey),
                    SizedBox(width: 8),
                    Text(
                      'Aucun fichier sélectionné',
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _selectFile,
                icon: const Icon(Icons.file_upload),
                label: const Text('Choisir un fichier JSON'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMigrationButton() {
    return SizedBox(
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _canStartMigration() ? _startMigration : null,
        icon: _isLoading 
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.sync),
        label: Text(_isLoading ? 'Migration en cours...' : 'Démarrer la Migration'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
  
  Widget _buildResultCard() {
    final result = _lastResult!;
    final isSuccess = result.success;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  color: isSuccess ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  isSuccess ? 'Migration Réussie' : 'Erreur de Migration',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isSuccess ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              result.message,
              style: const TextStyle(fontSize: 14),
            ),
            if (isSuccess && result.totalMigrated > 0) ...[
              const SizedBox(height: 16),
              const Text(
                'Détails de la migration:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              _buildMigrationStat('Produits', result.productsMigrated),
              _buildMigrationStat('Catégories', result.categoriesMigrated),
              _buildMigrationStat('Factures', result.invoicesMigrated),
              _buildMigrationStat('Tâches', result.tasksMigrated),
              const Divider(),
              _buildMigrationStat('Total', result.totalMigrated, isTotal: true),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildMigrationStat(String label, int count, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 14 : 13,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: isTotal ? 14 : 13,
              fontWeight: FontWeight.w600,
              color: isTotal ? AppColors.primary : null,
            ),
          ),
        ],
      ),
    );
  }
  
  bool _canStartMigration() {
    return !_isLoading && _selectedFilePath != null;
  }
  
  Future<void> _selectFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['json'],
        allowMultiple: false,
      );
      
      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFilePath = result.files.first.path;
          _lastResult = null; // Reset previous result
        });
      }
    } catch (e) {
      _showErrorDialog('Erreur lors de la sélection du fichier: $e');
    }
  }
  
  Future<void> _startMigration() async {
    if (_selectedFilePath == null) return;
    
    setState(() {
      _isLoading = true;
      _lastResult = null;
    });
    
    try {
      final result = await _migrationService.migrateFromJsonFile(_selectedFilePath!);
      
      setState(() {
        _lastResult = result;
      });
      
      if (result.success) {
        _showSuccessDialog(result);
      } else {
        _showErrorDialog(result.message);
      }
    } catch (e) {
      setState(() {
        _lastResult = MigrationResult()
          ..success = false
          ..message = 'Erreur inattendue: $e';
      });
      _showErrorDialog('Erreur inattendue: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  void _showSuccessDialog(MigrationResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('Migration Réussie'),
          ],
        ),
        content: Text(
          'La migration a été effectuée avec succès!\n\n'
          '${result.totalMigrated} éléments ont été migrés:\n'
          '• ${result.productsMigrated} produits\n'
          '• ${result.categoriesMigrated} catégories\n'
          '• ${result.invoicesMigrated} factures\n'
          '• ${result.tasksMigrated} tâches',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to previous page
            },
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
  
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('Erreur'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}