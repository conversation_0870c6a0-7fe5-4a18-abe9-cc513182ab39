import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/invoice.dart';
import '../services/invoice_service.dart';

/// Widget pour sélectionner une facture existante
class InvoiceSelectorWidget extends StatefulWidget {
  final Function(Invoice) onInvoiceSelected;
  final String? selectedInvoiceId;

  const InvoiceSelectorWidget({
    super.key,
    required this.onInvoiceSelected,
    this.selectedInvoiceId,
  });

  @override
  State<InvoiceSelectorWidget> createState() => _InvoiceSelectorWidgetState();
}

class _InvoiceSelectorWidgetState extends State<InvoiceSelectorWidget> {
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  List<Invoice> _invoices = [];
  List<Invoice> _filteredInvoices = [];
  bool _isLoading = true;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadInvoices();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadInvoices() async {
    setState(() => _isLoading = true);
    try {
      final invoices = await InvoiceService.loadInvoices();
      // Afficher toutes les factures (pas seulement les terminées)
      final availableInvoices = invoices.toList();

      // Trier par date de création (plus récent en premier)
      availableInvoices.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      setState(() {
        _invoices = availableInvoices;
        _filteredInvoices = availableInvoices;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des factures: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterInvoices(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredInvoices = _invoices;
      } else {
        _filteredInvoices =
            _invoices.where((invoice) {
              return invoice.clientName.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  invoice.clientNumber.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  invoice.invoiceNumber.toLowerCase().contains(
                    query.toLowerCase(),
                  ) ||
                  invoice.products.toLowerCase().contains(query.toLowerCase());
            }).toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // En-tête
        Row(
          children: [
            Icon(Icons.link, color: Colors.blue[600]),
            const SizedBox(width: 8),
            const Text(
              'Lier à une facture existante',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ],
        ),

        const SizedBox(height: 8),

        Text(
          'Sélectionnez une facture pour pré-remplir le formulaire de commande',
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),

        const SizedBox(height: 16),

        // Barre de recherche
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Rechercher par client, numéro, produit...',
            prefixIcon: const Icon(Icons.search),
            suffixIcon:
                _searchQuery.isNotEmpty
                    ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _filterInvoices('');
                      },
                    )
                    : null,
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          onChanged: _filterInvoices,
        ),

        const SizedBox(height: 16),

        // Liste des factures
        if (_isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          )
        else if (_filteredInvoices.isEmpty)
          _buildEmptyState()
        else
          Container(
            height: 300,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListView.builder(
              itemCount: _filteredInvoices.length,
              itemBuilder: (context, index) {
                final invoice = _filteredInvoices[index];
                return _buildInvoiceCard(invoice);
              },
            ),
          ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long_outlined,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty
                  ? 'Aucune facture terminée trouvée'
                  : 'Aucune facture ne correspond à votre recherche',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _searchQuery.isEmpty
                  ? 'Toutes les factures apparaîtront ici'
                  : 'Essayez avec d\'autres mots-clés',
              style: TextStyle(fontSize: 12, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceCard(Invoice invoice) {
    final isSelected = widget.selectedInvoiceId == invoice.id;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      elevation: isSelected ? 3 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side:
            isSelected
                ? BorderSide(color: Colors.blue, width: 2)
                : BorderSide.none,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => widget.onInvoiceSelected(invoice),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // En-tête avec numéro, statut et date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          invoice.invoiceNumber,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 8),
                        _buildStatusChip(invoice.status),
                      ],
                    ),
                  ),
                  Text(
                    DateFormat('dd/MM/yyyy').format(invoice.createdAt),
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Client
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      invoice.clientName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 13,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 4),

              // Téléphone
              Row(
                children: [
                  Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    invoice.clientNumber,
                    style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Produits (tronqués)
              Text(
                invoice.products,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 8),

              // Total et zone de livraison
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${_currencyFormat.format(invoice.total)} FCFA',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      color: Colors.green,
                    ),
                  ),
                  if (invoice.deliveryLocation.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        invoice.deliveryLocation,
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.blue[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Construire le chip de statut
  Widget _buildStatusChip(InvoiceStatus status) {
    Color color;
    String label;

    switch (status) {
      case InvoiceStatus.enAttente:
        color = Colors.orange;
        label = 'En attente';
        break;
      case InvoiceStatus.enCours:
        color = Colors.blue;
        label = 'En cours';
        break;
      case InvoiceStatus.terminee:
        color = Colors.green;
        label = 'Terminée';
        break;
      case InvoiceStatus.enRetard:
        color = Colors.red;
        label = 'En retard';
        break;
      case InvoiceStatus.annulee:
        color = Colors.grey;
        label = 'Annulée';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
