import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'main_navigation_page.dart';
import '../services/user_service.dart';

class BiometricAuthPage extends StatefulWidget {
  const BiometricAuthPage({super.key});

  @override
  State<BiometricAuthPage> createState() => _BiometricAuthPageState();
}

class _BiometricAuthPageState extends State<BiometricAuthPage> {
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  bool _isAuthenticating = false;
  bool _showAdminLogin = false;
  bool _rememberMe = false;
  String? _errorMessage;
  final UserService _userService = UserService();

  void _checkCode() {
    setState(() {
      _isAuthenticating = true;
      _errorMessage = null;
    });

    // Validation du code
    final code = _codeController.text.trim();

    if (code.isEmpty) {
      setState(() {
        _isAuthenticating = false;
        _errorMessage = 'Veuillez entrer un code d\'authentification';
      });
      return;
    }

    if (code.length != 6) {
      setState(() {
        _isAuthenticating = false;
        _errorMessage = 'Le code doit contenir 6 chiffres';
      });
      return;
    }

    if (code == '094958') {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainNavigationPage()),
      );
    } else {
      setState(() {
        _isAuthenticating = false;
        _errorMessage =
            'Code d\'authentification invalide. Veuillez réessayer.';
      });
    }
  }

  Future<void> _loginAdmin() async {
    setState(() {
      _isAuthenticating = true;
      _errorMessage = null;
    });

    try {
      await _userService.signInWithEmailAndPassword(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (_rememberMe) {
        // Sauvegarder les informations de connexion si "Rester connecté" est coché
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('saved_email', _emailController.text.trim());
        // Ne jamais stocker le mot de passe en clair, ceci est juste pour l'exemple
        // Dans une application réelle, utilisez un token sécurisé
      }

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainNavigationPage()),
        );
      }
    } catch (e) {
      setState(() {
        _isAuthenticating = false;
        // Améliorer le message d'erreur pour être plus spécifique
        if (e.toString().contains('invalid-email')) {
          _errorMessage = 'Adresse email invalide';
        } else if (e.toString().contains('user-not-found')) {
          _errorMessage = 'Utilisateur non trouvé';
        } else if (e.toString().contains('wrong-password')) {
          _errorMessage = 'Mot de passe incorrect';
        } else if (e.toString().contains('too-many-requests')) {
          _errorMessage = 'Trop de tentatives. Réessayez plus tard';
        } else {
          _errorMessage =
              'Erreur d\'authentification. Vérifiez vos identifiants';
        }
      });
    }
  }

  void _toggleAdminLogin() {
    setState(() {
      _showAdminLogin = !_showAdminLogin;
      _errorMessage = null;
    });
  }

  @override
  void dispose() {
    _codeController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.blue[900],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Center(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo de l'application
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Image.asset(
                        'assets/images/logo_entreprise.png',
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                  const SizedBox(height: 40),
                  // Titre
                  const Text(
                    'General HCP CRM',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 60),
                  // Affichage conditionnel selon le mode (code ou admin)
                  if (!_showAdminLogin) ...[
                    // Champ de saisie du code
                    TextField(
                      controller: _codeController,
                      obscureText: true,
                      keyboardType: TextInputType.number,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.white.withValues(alpha: 0.1),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30),
                          borderSide: BorderSide.none,
                        ),
                        hintText: 'Entrez le code',
                        hintStyle: const TextStyle(color: Colors.white54),
                        prefixIcon: const Icon(
                          Icons.lock,
                          color: Colors.white54,
                        ),
                      ),
                      onSubmitted: (_) => _checkCode(),
                    ),
                    const SizedBox(height: 20),
                    // Bouton de validation
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isAuthenticating ? null : _checkCode,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.blue[900],
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child:
                            _isAuthenticating
                                ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.blue,
                                    ),
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Text('Valider'),
                      ),
                    ),
                  ] else ...[
                    // Champ de saisie email admin
                    TextField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.white.withValues(alpha: 0.1),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30),
                          borderSide: BorderSide.none,
                        ),
                        hintText: 'Email administrateur',
                        hintStyle: const TextStyle(color: Colors.white54),
                        prefixIcon: const Icon(
                          Icons.email,
                          color: Colors.white54,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Champ de saisie mot de passe admin
                    TextField(
                      controller: _passwordController,
                      obscureText: true,
                      style: const TextStyle(color: Colors.white),
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: Colors.white.withValues(alpha: 0.1),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(30),
                          borderSide: BorderSide.none,
                        ),
                        hintText: 'Mot de passe',
                        hintStyle: const TextStyle(color: Colors.white54),
                        prefixIcon: const Icon(
                          Icons.lock,
                          color: Colors.white54,
                        ),
                      ),
                      onSubmitted: (_) => _loginAdmin(),
                    ),
                    const SizedBox(height: 16),
                    // Option rester connecté
                    Row(
                      children: [
                        Checkbox(
                          value: _rememberMe,
                          onChanged: (value) {
                            setState(() {
                              _rememberMe = value ?? false;
                            });
                          },
                          fillColor: WidgetStateProperty.resolveWith<Color>((
                            Set<WidgetState> states,
                          ) {
                            if (states.contains(WidgetState.selected)) {
                              return Colors.white;
                            }
                            return Colors.white54;
                          }),
                          checkColor: Colors.blue[900],
                        ),
                        const Text(
                          'Rester connecté',
                          style: TextStyle(color: Colors.white),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Bouton de connexion admin
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isAuthenticating ? null : _loginAdmin,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: Colors.blue[900],
                          padding: const EdgeInsets.symmetric(
                            horizontal: 32,
                            vertical: 16,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child:
                            _isAuthenticating
                                ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.blue,
                                    ),
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Text('Connexion Admin'),
                      ),
                    ),
                  ],
                  const SizedBox(height: 20),
                  if (_errorMessage != null) ...[
                    Text(
                      _errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                  ],
                  // Bouton pour basculer entre mode code et mode admin
                  TextButton(
                    onPressed: _toggleAdminLogin,
                    child: Text(
                      _showAdminLogin
                          ? 'Utiliser le code de connexion'
                          : 'Connexion administrateur',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                  const SizedBox(height: 30),
                  const Text(
                    "L'accès à l'application est protégé par un code ou un compte administrateur.",
                    style: TextStyle(fontSize: 14, color: Colors.white70),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
