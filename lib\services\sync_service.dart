import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'firebase_service.dart';
import 'inventory_service.dart';
import 'invoice_service.dart';
import 'task_service.dart';
import 'colis_service.dart';
import 'user_service.dart';
import 'offline_config_service.dart';

class SyncService {
  static SyncService? _instance;
  static SyncService get instance {
    _instance ??= SyncService._internal();
    return _instance!;
  }

  SyncService._internal();

  // Services
  final FirebaseService _firebaseService = FirebaseService.instance;
  final InventoryService _inventoryService = InventoryService.instance;
  final InvoiceService _invoiceService = InvoiceService();
  final TaskService _taskService = TaskService.instance;
  final ColisService _colisService = ColisService.instance;
  final UserService _userService = UserService();

  // Sync status
  bool _isSyncing = false;
  bool _isInitialized = false;
  DateTime? _lastSyncTime;
  Timer? _syncTimer;

  // Sync preferences
  static const String _autoSyncEnabledKey = 'auto_sync_enabled';
  static const String _syncIntervalKey = 'sync_interval_minutes';
  static const String _initialMigrationDoneKey = 'initial_migration_done';

  // Vérifier si le service est initialisé
  bool isInitialized() {
    return _isInitialized;
  }

  // Initialiser le service en arrière-plan
  void initializeInBackground() {
    if (_isInitialized) return;

    // Utiliser Future.microtask pour ne pas bloquer l'interface utilisateur
    Future.microtask(() async {
      try {
        // Vérifier si la synchronisation automatique est autorisée
        final canAutoSync =
            await OfflineConfigService.instance.canPerformAutoSync();
        if (canAutoSync) {
          await initialize();
          debugPrint('Sync service initialized in background');
        } else {
          debugPrint('Auto sync disabled - sync service not initialized');
        }
      } catch (e) {
        debugPrint('Error initializing sync service in background: $e');
      }
    });
  }

  // Initialize sync service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize Firebase service
      await _firebaseService.initialize();

      // Get last sync time
      _lastSyncTime = await _firebaseService.getLastSyncTime();

      // Check if initial migration is needed
      final prefs = await SharedPreferences.getInstance();
      final initialMigrationDone =
          prefs.getBool(_initialMigrationDoneKey) ?? false;

      if (!initialMigrationDone && _firebaseService.isOnline()) {
        // Perform initial migration
        await _performInitialMigration();
      }

      // Setup auto-sync if enabled
      final autoSyncEnabled = prefs.getBool(_autoSyncEnabledKey) ?? true;
      if (autoSyncEnabled) {
        _startAutoSync();
      }

      _isInitialized = true;
      debugPrint('Sync service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing sync service: $e');
    }
  }

  // Perform initial migration of data to Firebase
  Future<void> _performInitialMigration() async {
    // Vérifier si les opérations réseau sont autorisées
    final canUseNetwork =
        await OfflineConfigService.instance.canPerformNetworkOperation();
    if (!canUseNetwork) {
      debugPrint('Migration skipped - offline mode enabled');
      return;
    }

    debugPrint('Starting initial data migration to Firebase...');
    _isSyncing = true;

    try {
      // Migrate products and categories
      await _migrateProducts();

      // Migrate invoices
      await _migrateInvoices();

      // Migrate tasks
      await _migrateTasks();

      // Migrate colis
      await _migrateColis();

      // Migrate users (only if current user is admin)
      if (_userService.canManageUsers()) {
        await _migrateUsers();
      }

      // Mark initial migration as done
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_initialMigrationDoneKey, true);

      _lastSyncTime = DateTime.now();
      debugPrint('Initial migration completed successfully');
    } catch (e) {
      debugPrint('Error during initial migration: $e');
    } finally {
      _isSyncing = false;
    }
  }

  // Migrate products and categories
  Future<void> _migrateProducts() async {
    // Migrate categories first (products depend on them)
    final categories = await _inventoryService.getCategories();
    for (final category in categories) {
      await _firebaseService.addCategory(category);
    }

    // Then migrate products
    final products = await _inventoryService.getProducts();
    for (final product in products) {
      await _firebaseService.addProduct(product);
    }
  }

  // Migrate invoices
  Future<void> _migrateInvoices() async {
    final invoices = await InvoiceService.loadInvoices();
    for (final invoice in invoices) {
      await _firebaseService.addInvoice(invoice);
    }
  }

  // Migrate tasks
  Future<void> _migrateTasks() async {
    final tasks = await _taskService.getTasks();
    for (final task in tasks) {
      await _firebaseService.addTask(task);
    }
  }

  // Migrate colis
  Future<void> _migrateColis() async {
    final colisList = await _colisService.getAllColis();
    for (final colis in colisList) {
      await _firebaseService.addColis(colis);
    }
  }

  // Migrate users
  Future<void> _migrateUsers() async {
    // Only admins can migrate users
    if (!_userService.canManageUsers()) {
      return;
    }

    // Note: Local users would need to be loaded from a local service
    // This is a placeholder - implement based on your local user storage
    debugPrint('User migration completed (placeholder)');
  }

  // Start auto-sync timer
  void _startAutoSync() {
    _stopAutoSync(); // Stop any existing timer

    // Get sync interval (default: 15 minutes)
    SharedPreferences.getInstance().then((prefs) {
      final intervalMinutes = prefs.getInt(_syncIntervalKey) ?? 15;
      final interval = Duration(minutes: intervalMinutes);

      _syncTimer = Timer.periodic(interval, (_) => syncNow());
      debugPrint('Auto-sync scheduled every $intervalMinutes minutes');
    });
  }

  // Stop auto-sync timer
  void _stopAutoSync() {
    _syncTimer?.cancel();
    _syncTimer = null;
  }

  // Sync data now (manual or auto)
  Future<bool> syncNow() async {
    // Si le service n'est pas initialisé ou est déjà en cours de synchronisation ou hors ligne
    if (!_isInitialized || _isSyncing || !_firebaseService.isOnline()) {
      return false;
    }

    _isSyncing = true;
    bool success = false;

    try {
      // Process any pending operations first
      await _firebaseService.forceSyncNow();

      // Sync products and categories
      await _syncProducts();

      // Sync invoices
      await _syncInvoices();

      // Sync tasks
      await _syncTasks();

      // Sync colis
      await _syncColis();

      // Sync users (only if current user is admin)
      if (_userService.canManageUsers()) {
        await _syncUsers();
      }

      _lastSyncTime = DateTime.now();
      success = true;
      debugPrint('Sync completed successfully');
    } catch (e) {
      debugPrint('Error during sync: $e');
    } finally {
      _isSyncing = false;
    }

    return success;
  }

  // Sync products and categories
  Future<void> _syncProducts() async {
    // Get remote data
    final remoteCategories = await _firebaseService.getAllCategories();
    final remoteProducts = await _firebaseService.getAllProducts();

    // Get local data
    final localCategories = await _inventoryService.getCategories();
    final localProducts = await _inventoryService.getProducts();

    // Create maps for easier lookup
    final remoteCategoriesMap = {for (var c in remoteCategories) c.id: c};
    final remoteProductsMap = {for (var p in remoteProducts) p.id: p};
    final localCategoriesMap = {for (var c in localCategories) c.id: c};
    final localProductsMap = {for (var p in localProducts) p.id: p};

    // Sync categories
    for (final localCategory in localCategories) {
      if (!remoteCategoriesMap.containsKey(localCategory.id)) {
        // Category exists locally but not remotely - add to remote
        await _firebaseService.addCategory(localCategory);
      }
    }

    for (final remoteCategory in remoteCategories) {
      if (!localCategoriesMap.containsKey(remoteCategory.id)) {
        // Category exists remotely but not locally - add to local
        await _inventoryService.addCategory(remoteCategory);
      }
    }

    // Sync products
    for (final localProduct in localProducts) {
      if (!remoteProductsMap.containsKey(localProduct.id)) {
        // Product exists locally but not remotely - add to remote
        await _firebaseService.addProduct(localProduct);
      }
    }

    for (final remoteProduct in remoteProducts) {
      if (!localProductsMap.containsKey(remoteProduct.id)) {
        // Product exists remotely but not locally - add to local
        await _inventoryService.addProduct(remoteProduct);
      }
    }
  }

  // Sync invoices
  Future<void> _syncInvoices() async {
    // Get remote data
    final remoteInvoices = await _firebaseService.getAllInvoices();

    // Get local data
    final localInvoices = await InvoiceService.loadInvoices();

    // Create maps for easier lookup
    final remoteInvoicesMap = {for (var i in remoteInvoices) i.id: i};
    final localInvoicesMap = {for (var i in localInvoices) i.id: i};

    // Sync invoices
    for (final localInvoice in localInvoices) {
      if (!remoteInvoicesMap.containsKey(localInvoice.id)) {
        // Invoice exists locally but not remotely - add to remote
        await _firebaseService.addInvoice(localInvoice);
      }
    }

    for (final remoteInvoice in remoteInvoices) {
      if (!localInvoicesMap.containsKey(remoteInvoice.id)) {
        // Invoice exists remotely but not locally - add to local
        await _invoiceService.addInvoice(remoteInvoice);
      }
    }
  }

  // Sync tasks
  Future<void> _syncTasks() async {
    // Get remote data
    final remoteTasks = await _firebaseService.getAllTasks();

    // Get local data
    final localTasks = await _taskService.getTasks();

    // Create maps for easier lookup
    final remoteTasksMap = {for (var t in remoteTasks) t.id: t};
    final localTasksMap = {for (var t in localTasks) t.id: t};

    // Sync tasks
    for (final localTask in localTasks) {
      if (!remoteTasksMap.containsKey(localTask.id)) {
        // Task exists locally but not remotely - add to remote
        await _firebaseService.addTask(localTask);
      }
    }

    for (final remoteTask in remoteTasks) {
      if (!localTasksMap.containsKey(remoteTask.id)) {
        // Task exists remotely but not locally - add to local
        await _taskService.addTask(remoteTask);
      }
    }
  }

  // Sync colis
  Future<void> _syncColis() async {
    try {
      // Get remote data
      final remoteColis = await _firebaseService.getAllColis();

      // Get local data
      final localColis = await _colisService.getAllColis();

      // Create maps for easier lookup
      final remoteColisMap = {for (var c in remoteColis) c.id: c};
      final localColisMap = {for (var c in localColis) c.id: c};

      // Sync colis from local to remote
      for (final localColisItem in localColis) {
        if (!remoteColisMap.containsKey(localColisItem.id)) {
          // Colis exists locally but not remotely - add to remote
          await _firebaseService.addColis(localColisItem);
        } else {
          // Check if local version is newer and update if needed
          // Compare timestamps or version numbers here if available
          // For now, we'll assume remote is authoritative for existing items
        }
      }

      // Sync colis from remote to local
      for (final remoteColisItem in remoteColis) {
        if (!localColisMap.containsKey(remoteColisItem.id)) {
          // Colis exists remotely but not locally - add to local
          await _colisService.addColis(remoteColisItem);
        } else {
          // Update local with remote data (remote is authoritative)
          await _colisService.updateColis(remoteColisItem);
        }
      }

      debugPrint('Colis sync completed successfully');
    } catch (e) {
      debugPrint('Error syncing colis: $e');
      // Don't rethrow - let other sync operations continue
    }
  }

  // Sync users
  Future<void> _syncUsers() async {
    try {
      // Only admins can sync users
      if (!_userService.canManageUsers()) {
        return;
      }

      // Get remote users
      final remoteUsers = await _firebaseService.getAllUsers();

      // Update local user cache with remote data
      // This assumes UserService has methods to handle user data locally
      for (final remoteUser in remoteUsers) {
        // Update or cache user data locally
        // Implementation depends on how you want to handle local user storage
        debugPrint('Processing user: ${remoteUser.email}');
      }

      debugPrint('Users sync completed successfully');
    } catch (e) {
      debugPrint('Error syncing users: $e');
      // Don't rethrow - let other sync operations continue
    }
  }

  // SETTINGS METHODS

  // Enable or disable auto-sync
  Future<void> setAutoSyncEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoSyncEnabledKey, enabled);

    if (enabled) {
      _startAutoSync();
    } else {
      _stopAutoSync();
    }
  }

  // Set sync interval in minutes
  Future<void> setSyncInterval(int minutes) async {
    if (minutes < 1) minutes = 1; // Minimum 1 minute

    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_syncIntervalKey, minutes);

    // Restart auto-sync with new interval if enabled
    if (await isAutoSyncEnabled()) {
      _startAutoSync();
    }
  }

  // Check if auto-sync is enabled
  Future<bool> isAutoSyncEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_autoSyncEnabledKey) ?? true; // Default: enabled
  }

  // Get sync interval in minutes
  Future<int> getSyncInterval() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_syncIntervalKey) ?? 15; // Default: 15 minutes
  }

  // Get last sync time
  DateTime? getLastSyncTime() {
    return _lastSyncTime;
  }

  // Check if sync is in progress
  bool isSyncing() {
    return _isSyncing;
  }

  // Check if initial migration is done
  Future<bool> isInitialMigrationDone() async {
    // Si le service n'est pas initialisé, considérer que la migration n'est pas faite
    if (!_isInitialized) {
      return false;
    }

    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_initialMigrationDoneKey) ?? false;
  }

  // Reset initial migration flag (for testing)
  Future<void> resetInitialMigration() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_initialMigrationDoneKey, false);
  }

  // Get sync status information
  Future<Map<String, dynamic>> getSyncStatus() async {
    return {
      'isOnline': _firebaseService.isOnline(),
      'isSyncing': _isSyncing,
      'lastSyncTime': _lastSyncTime?.toIso8601String(),
      'pendingOperations': _firebaseService.getPendingOperationsCount(),
      'autoSyncEnabled': await isAutoSyncEnabled(),
      'syncInterval': await getSyncInterval(),
      'initialMigrationDone': await isInitialMigrationDone(),
    };
  }

  // Dispose
  void dispose() {
    _stopAutoSync();
  }
}
