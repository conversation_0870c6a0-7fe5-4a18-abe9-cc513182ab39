import 'package:flutter/material.dart';
import '../models/ai_models.dart';
import '../services/ai_service.dart';
import 'gemini_flash_test_page.dart';

class AIConfigurationPage extends StatefulWidget {
  const AIConfigurationPage({super.key});

  @override
  State<AIConfigurationPage> createState() => _AIConfigurationPageState();
}

class _AIConfigurationPageState extends State<AIConfigurationPage>
    with TickerProviderStateMixin {
  final AIService _aiService = AIService();

  List<AIConfiguration> _configurations = [];
  bool _isLoading = true;
  bool _isTesting = false;
  String _testResult = '';

  // Contrôleurs d'animation
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadConfigurations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _loadConfigurations() async {
    setState(() => _isLoading = true);

    try {
      await _aiService.initialize();
      setState(() {
        _configurations = _aiService.configurations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _refreshConfigurations() async {
    setState(() => _isLoading = true);

    try {
      // Force la réinitialisation du service AI pour ajouter les nouveaux modèles
      await _aiService.initialize();
      setState(() {
        _configurations = _aiService.configurations;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Configurations mises à jour avec succès'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la mise à jour: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _testConfiguration(AIConfiguration config) async {
    setState(() {
      _isTesting = true;
      _testResult = '';
    });

    try {
      final response = await _aiService.generateResponse(
        message: 'Bonjour, ceci est un test de connexion.',
        context: 'Test de configuration IA',
      );

      setState(() {
        _testResult =
            response != null
                ? '✅ Test réussi ! Modèle: ${response.model.modelName}'
                : '❌ Test échoué - Vérifiez votre clé API';
        _isTesting = false;
      });
    } catch (e) {
      setState(() {
        _testResult = '❌ Erreur: ${e.toString()}';
        _isTesting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Configuration IA'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF2196F3), // Bleu
                Color(0xFF3F51B5), // Indigo
                Color(0xFF9C27B0), // Violet
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshConfigurations,
            tooltip: 'Actualiser les configurations',
          ),
          IconButton(
            icon: const Icon(Icons.auto_awesome),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const GeminiFlashTestPage(),
                ),
              );
            },
            tooltip: 'Test Gemini Flash',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelp,
            tooltip: 'Aide',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child:
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _buildInfoCard(),
                        const SizedBox(height: 16),
                        if (_testResult.isNotEmpty) ...[
                          _buildTestResultCard(),
                          const SizedBox(height: 16),
                        ],
                        ..._configurations.map(
                          (config) => _buildConfigCard(config),
                        ),
                        const SizedBox(height: 16),
                        _buildAddProviderButton(),
                        const SizedBox(height: 8),
                        _buildRefreshButton(),
                      ],
                    ),
                  ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                const Text(
                  'Configuration des Fournisseurs IA',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            const Text(
              'Configurez vos clés API pour activer les différents fournisseurs d\'intelligence artificielle. '
              'Au moins un fournisseur doit être configuré pour utiliser les fonctionnalités IA.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestResultCard() {
    return Card(
      color: _testResult.startsWith('✅') ? Colors.green[50] : Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(
              _testResult.startsWith('✅') ? Icons.check_circle : Icons.error,
              color: _testResult.startsWith('✅') ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _testResult,
                style: TextStyle(
                  color:
                      _testResult.startsWith('✅')
                          ? Colors.green[800]
                          : Colors.red[800],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigCard(AIConfiguration config) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                _buildProviderIcon(config.provider),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        config.provider.displayName,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        config.model.modelName,
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      if (_getModelDescription(config.model).isNotEmpty)
                        Text(
                          _getModelDescription(config.model),
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.blue[600],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                    ],
                  ),
                ),
                Switch(
                  value: config.isEnabled && config.apiKey.isNotEmpty,
                  onChanged: (value) {
                    if (value && config.apiKey.isEmpty) {
                      _editConfiguration(config);
                    } else {
                      _toggleConfiguration(config, value);
                    }
                  },
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildConfigStatus(config),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _editConfiguration(config),
                    icon: const Icon(Icons.edit),
                    label: const Text('Configurer'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed:
                        config.apiKey.isNotEmpty && !_isTesting
                            ? () => _testConfiguration(config)
                            : null,
                    icon:
                        _isTesting
                            ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                            : const Icon(Icons.play_arrow),
                    label: Text(_isTesting ? 'Test...' : 'Tester'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProviderIcon(AIProvider provider) {
    IconData icon;
    Color color;

    switch (provider) {
      case AIProvider.openai:
        icon = Icons.psychology;
        color = Colors.green;
        break;
      case AIProvider.anthropic:
        icon = Icons.smart_toy;
        color = Colors.orange;
        break;
      case AIProvider.gemini:
        icon = Icons.auto_awesome;
        color = Colors.blue;
        break;
      case AIProvider.gemma:
        icon = Icons.diamond;
        color = Colors.indigo;
        break;
      case AIProvider.mistral:
        icon = Icons.wind_power;
        color = Colors.purple;
        break;
      case AIProvider.deepseek:
        icon = Icons.explore;
        color = Colors.teal;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(icon, color: color, size: 24),
    );
  }

  Widget _buildConfigStatus(AIConfiguration config) {
    if (config.apiKey.isEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.orange[100],
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Clé API requise',
          style: TextStyle(
            fontSize: 12,
            color: Colors.orange[800],
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    } else if (!config.isEnabled) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Désactivé',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[700],
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    } else {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green[100],
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Configuré et actif',
          style: TextStyle(
            fontSize: 12,
            color: Colors.green[800],
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }
  }

  Widget _buildAddProviderButton() {
    return OutlinedButton.icon(
      onPressed: _showAddProviderDialog,
      icon: const Icon(Icons.add),
      label: const Text('Ajouter un Fournisseur'),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }

  Future<void> _editConfiguration(AIConfiguration config) async {
    final result = await showDialog<AIConfiguration>(
      context: context,
      builder: (context) => _ConfigurationDialog(configuration: config),
    );

    if (result != null) {
      await _aiService.updateConfiguration(result);
      await _loadConfigurations();
    }
  }

  Future<void> _toggleConfiguration(
    AIConfiguration config,
    bool enabled,
  ) async {
    final updatedConfig = AIConfiguration(
      provider: config.provider,
      model: config.model,
      apiKey: config.apiKey,
      baseUrl: config.baseUrl,
      temperature: config.temperature,
      maxTokens: config.maxTokens,
      isEnabled: enabled,
      additionalParams: config.additionalParams,
    );

    await _aiService.updateConfiguration(updatedConfig);
    await _loadConfigurations();
  }

  void _showAddProviderDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Ajouter un Fournisseur'),
            content: const Text('Sélectionnez un fournisseur IA à configurer:'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
            ],
          ),
    );
  }

  Widget _buildRefreshButton() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.refresh, color: Colors.blue[700], size: 24),
                const SizedBox(width: 12),
                const Expanded(
                  child: Text(
                    'Actualiser les configurations',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Cliquez pour ajouter les nouveaux modèles IA disponibles (Gemini 1.5 Flash, 2.5 Flash, etc.)',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLoading ? null : _refreshConfigurations,
                icon:
                    _isLoading
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.refresh),
                label: Text(_isLoading ? 'Actualisation...' : 'Actualiser'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[700],
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Aide Configuration IA'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Comment obtenir les clés API:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• OpenAI: https://platform.openai.com/api-keys'),
                  Text('• Anthropic: https://console.anthropic.com/'),
                  Text('• Google Gemini: https://makersuite.google.com/'),
                  Text(
                    '• Gemma (Hugging Face): https://huggingface.co/settings/tokens',
                  ),
                  Text('• Mistral: https://console.mistral.ai/'),
                  Text('• DeepSeek: https://platform.deepseek.com/'),
                  SizedBox(height: 16),
                  Text(
                    'Modèles Gemini disponibles:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• Gemini Pro: Modèle standard'),
                  Text('• Gemini 1.5 Pro: Version améliorée'),
                  Text('• Gemini 1.5 Flash: Rapide et efficace'),
                  Text('• Gemini 2.5 Flash: Dernière version Flash'),
                  SizedBox(height: 16),
                  Text(
                    'Paramètres:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('• Température: Créativité des réponses (0.0-1.0)'),
                  Text('• Max Tokens: Longueur maximale des réponses'),
                  Text('• Base URL: URL personnalisée (optionnel)'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
            ],
          ),
    );
  }

  String _getModelDescription(AIModel model) {
    switch (model) {
      case AIModel.gemini15Flash:
        return 'Rapide et efficace - Optimisé pour la vitesse';
      case AIModel.gemini25Flash:
        return 'Dernière version Flash - Performance améliorée';
      case AIModel.gemini15Pro:
        return 'Version Pro - Capacités étendues';
      case AIModel.geminiPro:
        return 'Version standard';
      case AIModel.gpt35Turbo:
        return 'Modèle rapide et économique';
      case AIModel.gpt4:
        return 'Modèle avancé GPT-4';
      case AIModel.gpt4Turbo:
        return 'Version optimisée GPT-4';
      case AIModel.claude3Sonnet:
        return 'Équilibré performance/coût';
      case AIModel.claude3Haiku:
        return 'Rapide et léger';
      case AIModel.claude35Sonnet:
        return 'Modèle le plus avancé';
      case AIModel.deepseekV3:
        return 'Dernière version DeepSeek';
      case AIModel.deepseekChat:
        return 'Optimisé pour la conversation';
      case AIModel.deepseekCoder:
        return 'Spécialisé en programmation';
      case AIModel.gemma3_8b:
        return 'Modèle compact 8B';
      case AIModel.gemma3_27b:
        return 'Modèle équilibré 27B';
      case AIModel.gemma3_70b:
        return 'Modèle puissant 70B';
      case AIModel.mistralMedium:
        return 'Performance équilibrée';
      case AIModel.mistralLarge:
        return 'Modèle le plus puissant';
    }
  }
}

class _ConfigurationDialog extends StatefulWidget {
  final AIConfiguration configuration;

  const _ConfigurationDialog({required this.configuration});

  @override
  State<_ConfigurationDialog> createState() => _ConfigurationDialogState();
}

class _ConfigurationDialogState extends State<_ConfigurationDialog> {
  late TextEditingController _apiKeyController;
  late TextEditingController _baseUrlController;
  late TextEditingController _temperatureController;
  late TextEditingController _maxTokensController;

  bool _isEnabled = true;
  bool _obscureApiKey = true;

  @override
  void initState() {
    super.initState();
    _apiKeyController = TextEditingController(
      text: widget.configuration.apiKey,
    );
    _baseUrlController = TextEditingController(
      text: widget.configuration.baseUrl ?? '',
    );
    _temperatureController = TextEditingController(
      text: widget.configuration.temperature.toString(),
    );
    _maxTokensController = TextEditingController(
      text: widget.configuration.maxTokens.toString(),
    );
    _isEnabled = widget.configuration.isEnabled;
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    _baseUrlController.dispose();
    _temperatureController.dispose();
    _maxTokensController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Configuration ${widget.configuration.provider.displayName}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _apiKeyController,
              decoration: InputDecoration(
                labelText: 'Clé API *',
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _obscureApiKey ? Icons.visibility : Icons.visibility_off,
                  ),
                  onPressed:
                      () => setState(() => _obscureApiKey = !_obscureApiKey),
                ),
              ),
              obscureText: _obscureApiKey,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _baseUrlController,
              decoration: const InputDecoration(
                labelText: 'URL de base (optionnel)',
                border: OutlineInputBorder(),
                hintText: 'https://api.example.com',
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _temperatureController,
                    decoration: const InputDecoration(
                      labelText: 'Température',
                      border: OutlineInputBorder(),
                      hintText: '0.7',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _maxTokensController,
                    decoration: const InputDecoration(
                      labelText: 'Max Tokens',
                      border: OutlineInputBorder(),
                      hintText: '1000',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Activer ce fournisseur'),
              value: _isEnabled,
              onChanged: (value) => setState(() => _isEnabled = value),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Annuler'),
        ),
        ElevatedButton(
          onPressed: _saveConfiguration,
          child: const Text('Sauvegarder'),
        ),
      ],
    );
  }

  void _saveConfiguration() {
    final apiKey = _apiKeyController.text.trim();
    if (apiKey.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('La clé API est requise'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final temperature = double.tryParse(_temperatureController.text) ?? 0.7;
    final maxTokens = int.tryParse(_maxTokensController.text) ?? 1000;

    final updatedConfig = AIConfiguration(
      provider: widget.configuration.provider,
      model: widget.configuration.model,
      apiKey: apiKey,
      baseUrl:
          _baseUrlController.text.trim().isEmpty
              ? null
              : _baseUrlController.text.trim(),
      temperature: temperature.clamp(0.0, 1.0),
      maxTokens: maxTokens.clamp(1, 4000),
      isEnabled: _isEnabled,
      additionalParams: widget.configuration.additionalParams,
    );

    Navigator.pop(context, updatedConfig);
  }
}
