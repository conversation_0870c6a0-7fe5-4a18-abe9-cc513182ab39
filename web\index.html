<!DOCTYPE html>
<html lang="fr">
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="General HCP CRM">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>General HCP CRM</title>
  <link rel="manifest" href="manifest.json">
  
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: white;
      overflow: hidden;
    }
    
    html {
      margin: 0;
      padding: 0;
      background-color: white;
    }
    
    #flutter-view {
      margin: 0;
      padding: 0;
      background-color: white;
    }
    
    flt-glass-pane {
      background-color: transparent !important;
    }
  </style>
  
  <!-- Firebase SDKs -->
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-database-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/10.7.0/firebase-firestore-compat.js"></script>
  
  <!-- Configuration Firebase -->
  <script src="firebase-config.js"></script>
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
