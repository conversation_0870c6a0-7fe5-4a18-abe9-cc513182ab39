import 'package:flutter_test/flutter_test.dart';
import 'package:general_hcp_crm/services/employee_performance_service.dart';
import 'package:general_hcp_crm/services/gamification_service.dart';
import 'package:general_hcp_crm/models/invoice.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('EmployeePerformanceService Tests', () {
    late EmployeePerformanceService service;

    setUp(() {
      service = EmployeePerformanceService.instance;
    });

    test(
      'calculateSalary should return correct salary for different client counts',
      () {
        // Test avec 0 clients
        expect(service.calculateSalary(0), equals(70000.0));

        // Test avec 100 clients (50% de l'objectif)
        expect(
          service.calculateSalary(100),
          equals(80000.0),
        ); // 70000 + (100/200 * 20000)

        // Test avec 200 clients (objectif atteint)
        expect(service.calculateSalary(200), equals(90000.0)); // 70000 + 20000

        // Test avec plus de 200 clients (plafonné)
        expect(
          service.calculateSalary(300),
          equals(90000.0),
        ); // Plafonné à 90000
      },
    );

    test('calculateSalary should handle edge cases', () {
      // Test avec des valeurs négatives (ne devrait pas arriver mais on teste)
      expect(service.calculateSalary(-10), equals(70000.0));

      // Test avec exactement l'objectif
      expect(service.calculateSalary(200), equals(90000.0));

      // Test avec 1 client
      expect(
        service.calculateSalary(1),
        equals(70100.0),
      ); // 70000 + (1/200 * 20000)
    });

    test('salary calculation should match the original function logic', () {
      // Fonction originale modifiée avec les nouveaux paramètres
      double calculerSalaireOriginal(int clientsValides) {
        const salaireFixe = 70000.0;
        const bonusMax = 20000.0;
        const objectifClients = 200;

        final clients = clientsValides.clamp(0, objectifClients);
        final bonus = (clients / objectifClients) * bonusMax;

        return salaireFixe + bonus;
      }

      // Test plusieurs valeurs pour s'assurer que notre service correspond
      for (int clients in [0, 50, 100, 150, 200, 250]) {
        expect(
          service.calculateSalary(clients),
          equals(calculerSalaireOriginal(clients)),
          reason: 'Failed for $clients clients',
        );
      }
    });
  });

  group('Salary Calculation Examples', () {
    test('should demonstrate salary calculation examples', () {
      final service = EmployeePerformanceService.instance;

      print('\n=== EXEMPLES DE CALCUL DE SALAIRE ===');
      print('Salaire fixe: 70 000 FCFA');
      print('Objectif: 200 clients avec commande ≥ 3 000 FCFA');
      print('Bonus maximum: 20 000 FCFA');
      print('Salaire maximum: 90 000 FCFA\n');

      final examples = [0, 25, 50, 100, 150, 200, 250];

      for (final clients in examples) {
        final salary = service.calculateSalary(clients);
        final percentage = (clients / 200 * 100).clamp(0, 100);
        final bonus = salary - 70000;

        print('$clients clients valides:');
        print('  - Progression: ${percentage.toStringAsFixed(1)}%');
        print('  - Bonus: ${bonus.toStringAsFixed(0)} FCFA');
        print('  - Salaire total: ${salary.toStringAsFixed(0)} FCFA\n');
      }
    });
  });

  group('GamificationService Tests', () {
    late GamificationService gamificationService;

    setUp(() {
      gamificationService = GamificationService.instance;
    });

    test('should create sample invoice for testing', () {
      final invoice = Invoice(
        id: 'test-123',
        clientName: 'Client Test',
        clientNumber: '123456789',
        products: 'Produit Test',
        items: [
          InvoiceItem(
            id: 'item-1',
            name: 'Produit 1',
            price: 5000,
            quantity: 2,
            isCustom: false,
            categoryName: 'Test',
          ),
        ],
        deliveryLocation: 'Cocody',
        deliveryPrice: 1000,
        advance: 0,
        subtotal: 10000,
        total: 11000,
        status: InvoiceStatus.terminee,
        createdAt: DateTime.now(),
      );

      expect(invoice.clientName, equals('Client Test'));
      expect(invoice.total, equals(11000));
      expect(invoice.status, equals(InvoiceStatus.terminee));
    });

    test('should demonstrate gamification system', () async {
      print('\n=== SYSTÈME DE GAMIFICATION ===');
      print('🎮 Points de base par facture: 10');
      print('💰 Bonus grosse commande (≥10k): +20 points');
      print('💵 Bonus belle commande (≥5k): +10 points');
      print('🆕 Bonus nouveau client: +15 points');
      print('🔥 Bonus série (tous les 5 jours): +série points');
      print('🏆 Badges débloquables selon les performances\n');

      // Réinitialiser les stats pour le test
      await gamificationService.resetStats();

      // Simuler quelques factures
      final invoices = [
        // Petite commande
        Invoice(
          id: 'test-1',
          clientName: 'Client 1',
          clientNumber: '111111111',
          products: 'Produit A',
          items: [
            InvoiceItem(
              id: '1',
              name: 'Produit A',
              price: 2000,
              quantity: 1,
              isCustom: false,
              categoryName: 'Cat1',
            ),
          ],
          deliveryLocation: 'Cocody',
          deliveryPrice: 1000,
          advance: 0,
          subtotal: 2000,
          total: 3000,
          status: InvoiceStatus.terminee,
          createdAt: DateTime.now(),
        ),
        // Belle commande
        Invoice(
          id: 'test-2',
          clientName: 'Client 2',
          clientNumber: '222222222',
          products: 'Produit B',
          items: [
            InvoiceItem(
              id: '2',
              name: 'Produit B',
              price: 7000,
              quantity: 1,
              isCustom: false,
              categoryName: 'Cat2',
            ),
          ],
          deliveryLocation: 'Plateau',
          deliveryPrice: 1500,
          advance: 0,
          subtotal: 7000,
          total: 8500,
          status: InvoiceStatus.terminee,
          createdAt: DateTime.now(),
        ),
        // Grosse commande
        Invoice(
          id: 'test-3',
          clientName: 'Client 3',
          clientNumber: '333333333',
          products: 'Produit C',
          items: [
            InvoiceItem(
              id: '3',
              name: 'Produit C',
              price: 15000,
              quantity: 1,
              isCustom: false,
              categoryName: 'Cat3',
            ),
          ],
          deliveryLocation: 'Marcory',
          deliveryPrice: 1500,
          advance: 0,
          subtotal: 15000,
          total: 16500,
          status: InvoiceStatus.terminee,
          createdAt: DateTime.now(),
        ),
      ];

      for (int i = 0; i < invoices.length; i++) {
        final reward = await gamificationService.processNewInvoice(invoices[i]);

        print('Facture ${i + 1} (${invoices[i].subtotal} FCFA):');
        print('  Points gagnés: ${reward.pointsEarned}');
        print('  Messages: ${reward.messages.join(', ')}');
        if (reward.newBadges.isNotEmpty) {
          print(
            '  Nouveaux badges: ${reward.newBadges.map((b) => b.name).join(', ')}',
          );
        }
        if (reward.levelUp) {
          print('  🌟 NIVEAU ${reward.newLevel} ATTEINT !');
        }
        print('');
      }

      final stats = await gamificationService.getGamificationStats();
      print('=== RÉSUMÉ FINAL ===');
      print('Total points: ${stats.totalPoints}');
      print('Niveau: ${stats.level}');
      print('Badges: ${stats.badges.length}');
      print('Série: ${stats.currentStreak} jours');

      expect(stats.totalPoints, greaterThan(0));
      expect(stats.level, greaterThanOrEqualTo(1));
    });
  });
}
