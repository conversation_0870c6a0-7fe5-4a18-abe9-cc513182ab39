// Stub file for dart:html when not available on non-web platforms

class Blob {
  Blob(List<String> parts, String type);
}

class Url {
  static String createObjectUrl(Blob blob) => '';
  static void revokeObjectUrl(String url) {}
}

class AnchorElement {
  AnchorElement({String? href});
  String? download;
  void click() {}
  void remove() {}
}

class Document {
  Element? get body => null;
}

class Element {
  void append(AnchorElement element) {}
}

final document = Document();