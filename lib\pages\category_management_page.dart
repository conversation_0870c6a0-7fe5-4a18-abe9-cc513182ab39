import 'package:flutter/material.dart';
import 'package:general_hcp_crm/models/category.dart';
import 'package:general_hcp_crm/services/inventory_service.dart';

class CategoryManagementPage extends StatefulWidget {
  const CategoryManagementPage({super.key});

  @override
  State<CategoryManagementPage> createState() => _CategoryManagementPageState();
}

class _CategoryManagementPageState extends State<CategoryManagementPage> {
  final InventoryService _inventoryService = InventoryService.instance;
  List<Category> _categories = [];
  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    try {
      _categories = await _inventoryService.getCategories();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erreur de chargement des catégories: ${e.toString()}',
            ),
          ),
        );
      }
    }
    // setState(() => _isLoading = false); // isLoading is not used
    setState(
      () {},
    ); // Trigger rebuild after loading categories or handling error
  }

  void _showAddEditCategoryDialog({Category? category}) {
    final nameController = TextEditingController(
      text: category?.name ?? '',
    );
    final priceController = TextEditingController(
      text: category?.defaultPrice?.toString() ?? '',
    );
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(category == null ? 'Ajouter une catégorie' : 'Modifier la catégorie'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Nom de la catégorie',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Veuillez entrer un nom de catégorie';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: priceController,
                decoration: const InputDecoration(
                  labelText: 'Prix par défaut (optionnel)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.euro),
                  suffixText: '€',
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final price = double.tryParse(value);
                    if (price == null || price < 0) {
                      return 'Veuillez entrer un prix valide';
                    }
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                final defaultPrice = priceController.text.isNotEmpty 
                    ? double.tryParse(priceController.text) 
                    : null;
                
                final newCategory = Category(
                  id: category?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
                  name: nameController.text.trim(),
                  defaultPrice: defaultPrice,
                );

                if (category == null) {
                  await _inventoryService.addCategory(newCategory);
                } else {
                  await _inventoryService.updateCategory(newCategory);
                }

                // ignore: use_build_context_synchronously
                Navigator.pop(context);
                _loadCategories();
              }
            },
            child: Text(category == null ? 'Ajouter' : 'Modifier'),
          ),
        ],
      ),
    );

  }

  Future<void> _deleteCategory(Category category) async {
    final navigator = Navigator.of(
      context,
    ); // Capture Navigator before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(
      context,
    ); // Capture ScaffoldMessenger before async gap
    final confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        // Use a different context name for the dialog
        return AlertDialog(
          title: const Text('Confirmer la suppression'),
          content: Text(
            'Voulez-vous vraiment supprimer la catégorie "${category.name}" ? Cela pourrait affecter les produits associés.',
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Annuler'),
              onPressed: () => navigator.pop(false), // Use captured navigator
            ),
            TextButton(
              child: const Text(
                'Supprimer',
                style: TextStyle(color: Colors.red),
              ),
              onPressed: () => navigator.pop(true), // Use captured navigator
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      // scaffoldMessenger is already captured before the async gap
      bool deletedSuccessfully = false;
      try {
        await _inventoryService.deleteCategory(category.id);
        deletedSuccessfully = true; // Mark as successful
      } catch (e) {
        if (mounted) {
          // Ensure mounted check before using context for ScaffoldMessenger
          scaffoldMessenger.showSnackBar(
            // Use the captured scaffoldMessenger
            SnackBar(
              content: Text('Erreur lors de la suppression: ${e.toString()}'),
            ),
          );
        }
      }

      if (deletedSuccessfully && mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text('Catégorie "${category.name}" supprimée.')),
        );
        _loadCategories(); // Load categories only if deletion was successful and widget is still mounted
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Gestion des Catégories')),
      body:
          _categories.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Show loading indicator only if categories are truly empty and we are likely loading
                    // This assumes _loadCategories is called in initState and will populate _categories
                    // or show an error. If _categories remains empty after that, it means no categories.
                    if (mounted &&
                        _categories.isEmpty &&
                        ModalRoute.of(context)?.isCurrent != true)
                      const CircularProgressIndicator()
                    else
                      const Text(
                        'Aucune catégorie trouvée.',
                      ), // Default to no categories if not loading
                    const SizedBox(height: 20),
                    ElevatedButton.icon(
                      icon: const Icon(Icons.add),
                      label: const Text('Ajouter une catégorie'),
                      onPressed: () => _showAddEditCategoryDialog(),
                    ),
                  ],
                ),
              )
              : ListView.builder(
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  return Card(
                    margin: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    child: ListTile(
                      title: Text(
                        category.name,
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      subtitle: category.defaultPrice != null
                          ? Text(
                              'Prix par défaut: ${category.defaultPrice!.toStringAsFixed(2)} €',
                              style: TextStyle(
                                color: Colors.green[600],
                                fontWeight: FontWeight.w500,
                              ),
                            )
                          : const Text(
                              'Aucun prix par défaut',
                              style: TextStyle(
                                color: Colors.grey,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.category,
                          color: Colors.blue[600],
                          size: 20,
                        ),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: Icon(
                              Icons.edit,
                              color: Colors.blue[600],
                            ),
                            onPressed: () => _showAddEditCategoryDialog(category: category),
                            tooltip: 'Modifier',
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.delete,
                              color: Colors.red[600],
                            ),
                            onPressed: () => _deleteCategory(category),
                            tooltip: 'Supprimer',
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
      floatingActionButton: Padding(
        padding: EdgeInsets.only(
          bottom: 70 + 16 * 2 + MediaQuery.viewPaddingOf(context).bottom + 16,
        ),
        child: FloatingActionButton(
          onPressed: () => _showAddEditCategoryDialog(),
          tooltip: 'Ajouter une catégorie',
          child: const Icon(Icons.add),
        ),
      ),
    );
  }
}
