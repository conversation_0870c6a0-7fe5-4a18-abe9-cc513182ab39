import 'package:flutter/foundation.dart';
import 'package:google_ml_kit/google_ml_kit.dart';

/// Service OCR pour extraire automatiquement des données spécifiques des photos
/// Extrait : prix, dates, noms de produits, numéros
class OcrService {
  static final OcrService _instance = OcrService._internal();
  factory OcrService() => _instance;
  OcrService._internal();

  static OcrService get instance => _instance;

  late final TextRecognizer _textRecognizer;
  bool _isInitialized = false;

  /// Initialiser le service OCR
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _textRecognizer = TextRecognizer();
      _isInitialized = true;
      debugPrint('✅ Service OCR initialisé');
    } catch (e) {
      debugPrint('❌ Erreur initialisation OCR: $e');
      rethrow;
    }
  }

  /// Analyser une image et extraire les données spécifiques
  Future<Map<String, String>> analyzeImage(String imagePath) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final recognizedText = await _textRecognizer.processImage(inputImage);

      return _extractSpecificData(recognizedText.text);
    } catch (e) {
      debugPrint('❌ Erreur analyse OCR: $e');
      return {};
    }
  }

  /// Analyser une image spécifiquement pour les données de colis
  Future<Map<String, String>> analyzePackageImage(String imagePath) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final inputImage = InputImage.fromFilePath(imagePath);
      final recognizedText = await _textRecognizer.processImage(inputImage);

      return _extractPackageData(recognizedText.text);
    } catch (e) {
      debugPrint('❌ Erreur analyse OCR colis: $e');
      return {};
    }
  }

  /// Extraire des données spécifiques du texte reconnu
  Map<String, String> _extractSpecificData(String text) {
    final Map<String, String> extractedData = {};

    // Nettoyer le texte
    final cleanText = text.replaceAll('\n', ' ').trim();

    // Extraire les prix (formats: 1000, 1.000, 1,000, 1000 FCFA, etc.)
    final prices = _extractPrices(cleanText);
    if (prices.isNotEmpty) {
      extractedData['prix'] = prices.join(', ');
    }

    // Extraire les dates (formats: DD/MM/YYYY, DD-MM-YYYY, DD.MM.YYYY)
    final dates = _extractDates(cleanText);
    if (dates.isNotEmpty) {
      extractedData['dates'] = dates.join(', ');
    }

    // Extraire les numéros (téléphone, facture, etc.)
    final numbers = _extractNumbers(cleanText);
    if (numbers.isNotEmpty) {
      extractedData['numeros'] = numbers.join(', ');
    }

    // Extraire les noms/produits (mots en majuscules ou patterns spécifiques)
    final names = _extractNames(cleanText);
    if (names.isNotEmpty) {
      extractedData['noms'] = names.join(', ');
    }

    // Stocker le texte complet pour référence
    extractedData['texte_complet'] = cleanText;

    debugPrint('📝 Données OCR extraites: $extractedData');
    return extractedData;
  }

  /// Extraire des données spécifiques pour les colis
  Map<String, String> _extractPackageData(String text) {
    final Map<String, String> packageData = {};

    // Nettoyer le texte
    final cleanText = text.replaceAll('\n', ' ').trim();
    debugPrint('🔍 Texte OCR colis: $cleanText');

    // 1. Extraire le libellé/description du produit
    final productDescription = _extractProductDescription(cleanText);
    if (productDescription.isNotEmpty) {
      packageData['libelle'] = productDescription;
    }

    // 2. Extraire le nom du client
    final clientName = _extractClientName(cleanText);
    if (clientName.isNotEmpty) {
      packageData['nomClient'] = clientName;
    }

    // 3. Extraire le numéro de téléphone
    final phoneNumber = _extractPhoneNumber(cleanText);
    if (phoneNumber.isNotEmpty) {
      packageData['numeroClient'] = phoneNumber;
    }

    // 4. Extraire l'adresse de livraison
    final address = _extractDeliveryAddress(cleanText);
    if (address.isNotEmpty) {
      packageData['adresseLivraison'] = address;
    }

    // 5. Extraire les montants (reste à payer, frais de livraison)
    final amounts = _extractAmounts(cleanText);
    if (amounts['resteAPayer']?.isNotEmpty == true) {
      packageData['resteAPayer'] = amounts['resteAPayer']!;
    }
    if (amounts['fraisLivraison']?.isNotEmpty == true) {
      packageData['fraisLivraison'] = amounts['fraisLivraison']!;
    }

    // 6. Extraire les notes/informations supplémentaires
    final notes = _extractPackageNotes(cleanText);
    if (notes.isNotEmpty) {
      packageData['notes'] = notes;
    }

    // Stocker le texte complet pour référence
    packageData['texte_complet'] = cleanText;

    debugPrint('📦 Données colis extraites: $packageData');
    return packageData;
  }

  /// Extraire les prix du texte
  List<String> _extractPrices(String text) {
    final List<String> prices = [];

    // Patterns pour les prix
    final pricePatterns = [
      RegExp(
        r'\b(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{2})?)\s*(?:FCFA|CFA|F|€|USD|\$)\b',
        caseSensitive: false,
      ),
      RegExp(
        r'\b(?:FCFA|CFA|F|€|USD|\$)\s*(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{2})?)\b',
        caseSensitive: false,
      ),
      RegExp(r'\b(\d{1,3}(?:[.,]\d{3})+)\b'), // Nombres avec séparateurs
    ];

    for (final pattern in pricePatterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        final price = match.group(1) ?? match.group(0);
        if (price != null && !prices.contains(price)) {
          prices.add(price);
        }
      }
    }

    return prices;
  }

  /// Extraire les dates du texte
  List<String> _extractDates(String text) {
    final List<String> dates = [];

    // Patterns pour les dates
    final datePatterns = [
      RegExp(r'\b(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})\b'),
      RegExp(
        r'\b(\d{1,2}\s+(?:janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)\s+\d{2,4})\b',
        caseSensitive: false,
      ),
      RegExp(r'\b(\d{2,4}[\/\-\.]\d{1,2}[\/\-\.]\d{1,2})\b'),
    ];

    for (final pattern in datePatterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        final date = match.group(1);
        if (date != null && !dates.contains(date)) {
          dates.add(date);
        }
      }
    }

    return dates;
  }

  /// Extraire les numéros du texte
  List<String> _extractNumbers(String text) {
    final List<String> numbers = [];

    // Patterns pour les numéros
    final numberPatterns = [
      RegExp(
        r'\b(?:TEL|TELEPHONE|PHONE|MOBILE|CELL)[\s:]*(\+?\d{2,3}[\s\-]?\d{2,3}[\s\-]?\d{2,3}[\s\-]?\d{2,3})\b',
        caseSensitive: false,
      ),
      RegExp(r'\b(\+?\d{2,3}[\s\-]?\d{2,3}[\s\-]?\d{2,3}[\s\-]?\d{2,3})\b'),
      RegExp(
        r'\b(?:FACTURE|INVOICE|REF|REFERENCE)[\s:]*([A-Z0-9\-]+)\b',
        caseSensitive: false,
      ),
      RegExp(r'\b([A-Z]{2,3}\-\d{4,})\b'), // Références type ABC-1234
    ];

    for (final pattern in numberPatterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        final number = match.group(1);
        if (number != null && !numbers.contains(number)) {
          numbers.add(number);
        }
      }
    }

    return numbers;
  }

  /// Extraire les noms/produits du texte
  List<String> _extractNames(String text) {
    final List<String> names = [];

    // Patterns pour les noms
    final namePatterns = [
      RegExp(r'\b([A-Z][A-Z\s]{2,})\b'), // Mots en majuscules
      RegExp(
        r'\b(?:PRODUIT|ARTICLE|ITEM)[\s:]*([A-Za-z\s]+)\b',
        caseSensitive: false,
      ),
      RegExp(r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b'), // Noms propres
    ];

    for (final pattern in namePatterns) {
      final matches = pattern.allMatches(text);
      for (final match in matches) {
        final name = match.group(1)?.trim();
        if (name != null && name.length > 2 && !names.contains(name)) {
          // Filtrer les mots communs
          if (!_isCommonWord(name)) {
            names.add(name);
          }
        }
      }
    }

    return names.take(5).toList(); // Limiter à 5 noms
  }

  /// Vérifier si un mot est un mot commun à ignorer
  bool _isCommonWord(String word) {
    final commonWords = {
      'THE',
      'AND',
      'FOR',
      'ARE',
      'BUT',
      'NOT',
      'YOU',
      'ALL',
      'CAN',
      'HER',
      'WAS',
      'ONE',
      'OUR',
      'LE',
      'LA',
      'LES',
      'UN',
      'UNE',
      'DES',
      'ET',
      'OU',
      'MAIS',
      'DONC',
      'CAR',
      'NI',
      'POUR',
      'AVEC',
      'SANS',
      'DANS',
      'SUR',
      'SOUS',
      'VERS',
      'CHEZ',
      'PAR',
      'SELON',
      'ENTRE',
      'TOTAL',
      'PRIX',
      'DATE',
      'NUMERO',
      'FACTURE',
      'INVOICE',
      'BILL',
      'RECEIPT',
    };

    return commonWords.contains(word.toUpperCase());
  }

  // ===== MÉTHODES SPÉCIALISÉES POUR LES COLIS =====

  /// Extraire la description/libellé du produit
  String _extractProductDescription(String text) {
    // Patterns pour identifier les descriptions de produits
    final productPatterns = [
      RegExp(
        r'(?:PRODUIT|ARTICLE|ITEM|DESCRIPTION)[\s:]*([A-Za-z0-9\s\-_]+)',
        caseSensitive: false,
      ),
      RegExp(
        r'(?:LIBELLE|LIBELLÉ)[\s:]*([A-Za-z0-9\s\-_]+)',
        caseSensitive: false,
      ),
      RegExp(
        r'^([A-Z][A-Za-z\s]{5,30})',
        multiLine: true,
      ), // Première ligne en majuscules
    ];

    for (final pattern in productPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        final description = match.group(1)?.trim();
        if (description != null && description.length > 3) {
          return description;
        }
      }
    }

    // Fallback: prendre les premiers mots significatifs
    final words = text.split(' ').where((w) => w.length > 2).take(3);
    return words.join(' ');
  }

  /// Extraire le nom du client
  String _extractClientName(String text) {
    final namePatterns = [
      RegExp(
        r'(?:NOM|NAME|CLIENT|DESTINATAIRE)[\s:]*([A-Z][A-Za-z\s]{2,30})',
        caseSensitive: false,
      ),
      RegExp(
        r'(?:M\.|MR|MME|MLLE)[\s]*([A-Z][A-Za-z\s]{2,30})',
        caseSensitive: false,
      ),
      RegExp(r'([A-Z][a-z]+\s+[A-Z][a-z]+)'), // Prénom Nom
    ];

    for (final pattern in namePatterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        final name = match.group(1)?.trim();
        if (name != null && name.length > 3 && !_isCommonWord(name)) {
          return name;
        }
      }
    }

    return '';
  }

  /// Extraire le numéro de téléphone
  String _extractPhoneNumber(String text) {
    final phonePatterns = [
      RegExp(
        r'(?:TEL|TELEPHONE|PHONE|MOBILE|CONTACT)[\s:]*(\+?[0-9\s\-]{8,15})',
        caseSensitive: false,
      ),
      RegExp(r'(\+?225[\s\-]?[0-9\s\-]{8,12})'), // Format ivoirien
      RegExp(
        r'(\+?[0-9]{2,3}[\s\-]?[0-9]{2,3}[\s\-]?[0-9]{2,3}[\s\-]?[0-9]{2,3})',
      ), // Format général
    ];

    for (final pattern in phonePatterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        final phone = match.group(1)?.replaceAll(RegExp(r'[\s\-]'), '');
        if (phone != null && phone.length >= 8) {
          return phone;
        }
      }
    }

    return '';
  }

  /// Extraire l'adresse de livraison
  String _extractDeliveryAddress(String text) {
    final addressPatterns = [
      RegExp(
        r'(?:ADRESSE|ADDRESS|LIVRAISON|DELIVERY)[\s:]*([A-Za-z0-9\s,\-\.]{10,100})',
        caseSensitive: false,
      ),
      RegExp(
        r'(?:COCODY|YOPOUGON|ADJAME|PLATEAU|MARCORY|TREICHVILLE|ABOBO|KOUMASSI)[\s,]*([A-Za-z0-9\s,\-\.]{5,50})',
        caseSensitive: false,
      ),
    ];

    for (final pattern in addressPatterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        final address = match.group(1)?.trim();
        if (address != null && address.length > 5) {
          return address;
        }
      }
    }

    return '';
  }

  /// Extraire les montants (reste à payer, frais de livraison)
  Map<String, String> _extractAmounts(String text) {
    final amounts = <String, String>{};

    final restePattern = RegExp(
      r'(?:RESTE|SOLDE|DU|BALANCE)[\s:]*(\d{1,6}(?:[.,]\d{3})*)',
      caseSensitive: false,
    );
    final fraisPattern = RegExp(
      r'(?:FRAIS|LIVRAISON|DELIVERY|TRANSPORT)[\s:]*(\d{1,6}(?:[.,]\d{3})*)',
      caseSensitive: false,
    );

    final resteMatch = restePattern.firstMatch(text);
    if (resteMatch != null) {
      amounts['resteAPayer'] = resteMatch.group(1)?.replaceAll(',', '') ?? '';
    }

    final fraisMatch = fraisPattern.firstMatch(text);
    if (fraisMatch != null) {
      amounts['fraisLivraison'] =
          fraisMatch.group(1)?.replaceAll(',', '') ?? '';
    }

    return amounts;
  }

  /// Extraire les notes/informations supplémentaires
  String _extractPackageNotes(String text) {
    final notePatterns = [
      RegExp(
        r'(?:NOTE|NOTES|REMARQUE|COMMENT|INFO)[\s:]*([A-Za-z0-9\s,\-\.]{5,100})',
        caseSensitive: false,
      ),
      RegExp(
        r'(?:URGENT|FRAGILE|ATTENTION)[\s:]*([A-Za-z0-9\s,\-\.]{5,50})',
        caseSensitive: false,
      ),
    ];

    for (final pattern in notePatterns) {
      final match = pattern.firstMatch(text);
      if (match != null) {
        final note = match.group(1)?.trim();
        if (note != null && note.length > 3) {
          return note;
        }
      }
    }

    return '';
  }

  /// Libérer les ressources
  Future<void> dispose() async {
    if (_isInitialized) {
      await _textRecognizer.close();
      _isInitialized = false;
      debugPrint('🗑️ Service OCR libéré');
    }
  }
}
