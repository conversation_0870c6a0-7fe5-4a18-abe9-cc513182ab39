import 'package:flutter/material.dart';
import '../models/colis.dart';
import '../models/invoice.dart';
import '../services/colis_service.dart';
import '../services/invoice_service.dart';
import 'dashboard_page.dart';
import 'create_invoice_page.dart';
import 'ajouter_livraison_page.dart';

class ConfirmationLivraisonPage extends StatefulWidget {
  final String colisId;

  const ConfirmationLivraisonPage({super.key, required this.colisId});

  @override
  State<ConfirmationLivraisonPage> createState() =>
      _ConfirmationLivraisonPageState();
}

class _ConfirmationLivraisonPageState extends State<ConfirmationLivraisonPage> {
  final ColisService _colisService = ColisService.instance;
  // final InvoiceService _invoiceService = InvoiceService(); // Non utilisé

  Colis? _colis;
  List<Invoice> _factures = [];
  bool _isLoading = true;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _chargerDonnees();
  }

  Future<void> _chargerDonnees() async {
    try {
      final colis = await _colisService.obtenirColisParId(widget.colisId);
      final factures = await InvoiceService.loadInvoices();

      if (mounted) {
        setState(() {
          _colis = colis;
          _factures = factures;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  Future<void> _associerFactureExistante() async {
    if (_colis == null) return;

    // Filtrer les factures qui correspondent au client
    final facturesClient =
        _factures.where((facture) {
          return facture.clientName.toLowerCase().contains(
                _colis!.nomClient?.toLowerCase() ?? '',
              ) ||
              facture.clientNumber == _colis!.numeroClient;
        }).toList();

    if (facturesClient.isEmpty) {
      // Afficher toutes les factures si aucune correspondance
      await _afficherListeFactures(_factures);
    } else {
      // Afficher les factures correspondantes en premier
      await _afficherListeFactures(facturesClient, autresFactures: _factures);
    }
  }

  Future<void> _afficherListeFactures(
    List<Invoice> facturesPrincipales, {
    List<Invoice>? autresFactures,
  }) async {
    final factureSelectionnee = await showDialog<Invoice>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Sélectionner une facture'),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child: Column(
                children: [
                  if (facturesPrincipales.isNotEmpty) ...[
                    const Text(
                      'Factures correspondantes:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: ListView.builder(
                        itemCount: facturesPrincipales.length,
                        itemBuilder: (context, index) {
                          final facture = facturesPrincipales[index];
                          return Card(
                            child: ListTile(
                              title: Text(facture.clientName),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('N° ${facture.invoiceNumber}'),
                                  Text(facture.clientNumber),
                                  Text(
                                    '${facture.total.toStringAsFixed(0)} FCFA',
                                  ),
                                ],
                              ),
                              trailing: Chip(
                                label: Text(facture.status.displayName),
                                backgroundColor: _getStatusColor(
                                  facture.status.displayName,
                                ),
                              ),
                              onTap: () => Navigator.of(context).pop(facture),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                  if (autresFactures != null && autresFactures.isNotEmpty) ...[
                    const Divider(),
                    const Text(
                      'Autres factures:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: ListView.builder(
                        itemCount: autresFactures.length,
                        itemBuilder: (context, index) {
                          final facture = autresFactures[index];
                          // Éviter les doublons
                          if (facturesPrincipales.contains(facture)) {
                            return const SizedBox.shrink();
                          }
                          return Card(
                            child: ListTile(
                              title: Text(facture.clientName),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('N° ${facture.invoiceNumber}'),
                                  Text(facture.clientNumber),
                                  Text(
                                    '${facture.total.toStringAsFixed(0)} FCFA',
                                  ),
                                ],
                              ),
                              trailing: Chip(
                                label: Text(facture.status.displayName),
                                backgroundColor: _getStatusColor(
                                  facture.status.displayName,
                                ),
                              ),
                              onTap: () => Navigator.of(context).pop(facture),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Annuler'),
              ),
            ],
          ),
    );

    if (factureSelectionnee != null) {
      await _lierFacture(factureSelectionnee.id);
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'payée':
        return Colors.green[100]!;
      case 'en attente':
        return Colors.orange[100]!;
      case 'annulée':
        return Colors.red[100]!;
      default:
        return Colors.grey[100]!;
    }
  }

  Future<void> _lierFacture(String factureId) async {
    if (_colis == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      await _colisService.associerFacture(widget.colisId, factureId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Livraison associée à la facture avec succès'),
            backgroundColor: Colors.green,
          ),
        );

        // Retourner au dashboard
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const DashboardPage()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'association: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  Future<void> _creerNouvelleFacture() async {
    if (_colis == null) return;

    // Naviguer vers la page de création de facture avec les données pré-remplies
    final result = await Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const CreateInvoicePage()));

    if (result != null && result is String) {
      // Une nouvelle facture a été créée, l'associer au colis
      await _lierFacture(result);
    }
  }

  Future<void> _terminerSansLiaison() async {
    // Retourner au dashboard sans associer de facture
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const DashboardPage()),
      (route) => false,
    );
  }

  Future<void> _ajouterAutreLivraison() async {
    // Naviguer vers la page d'ajout de livraison
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const AjouterLivraisonPage(),
      ),
      (route) => route.isFirst, // Garder seulement la page d'accueil dans la pile
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Livraison enregistrée',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.green[700],
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _isProcessing
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Traitement en cours...'),
                  ],
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Message de succès
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.green[200]!),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.check_circle,
                            size: 64,
                            color: Colors.green[600],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Livraison enregistrée avec succès !',
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.green[800],
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Votre colis a été ajouté à la liste des livraisons.',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.green[700],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Résumé du colis
                    if (_colis != null) ...[
                      Card(
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.inventory_2,
                                    color: Colors.blue[600],
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Résumé de la livraison',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),

                              _buildInfoRow('Libellé', _colis!.libelle),
                              _buildInfoRow('Zone', _colis!.zoneLivraison),
                              _buildInfoRow(
                                'Client',
                                _colis!.nomClient ?? 'Non renseigné',
                              ),
                              _buildInfoRow('Téléphone', _colis!.numeroClient),
                              _buildInfoRow(
                                'Reste à payer',
                                '${_colis!.resteAPayer.toStringAsFixed(0)} FCFA',
                              ),
                              _buildInfoRow(
                                'Frais de livraison',
                                '${_colis!.fraisLivraison.toStringAsFixed(0)} FCFA',
                              ),
                              _buildInfoRow(
                                'Statut',
                                '${_colis!.statut.emoji} ${_colis!.statut.libelle}',
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 24),
                    ],

                    // Options d'action
                    Card(
                      elevation: 2,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.link, color: Colors.blue[600]),
                                const SizedBox(width: 8),
                                const Text(
                                  'Que souhaitez-vous faire maintenant ?',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Associer à une facture existante
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: _associerFactureExistante,
                                icon: const Icon(Icons.link),
                                label: const Text(
                                  'Associer à une facture existante',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue[600],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 12),

                            // Créer une nouvelle facture
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: _creerNouvelleFacture,
                                icon: const Icon(Icons.add),
                                label: const Text(
                                  'Créer une nouvelle facture',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green[600],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 12),

                            // Ajouter une autre livraison
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: _ajouterAutreLivraison,
                                icon: const Icon(Icons.add_box),
                                label: const Text(
                                  'Ajouter une autre livraison',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange[600],
                                  foregroundColor: Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),

                            const SizedBox(height: 12),

                            // Terminer sans liaison
                            SizedBox(
                              width: double.infinity,
                              child: OutlinedButton.icon(
                                onPressed: _terminerSansLiaison,
                                icon: const Icon(Icons.home),
                                label: const Text(
                                  'Terminer et retourner à l\'accueil',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.grey[700],
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}
