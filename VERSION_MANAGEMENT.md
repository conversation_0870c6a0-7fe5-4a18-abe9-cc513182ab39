# 🚀 Gestion des Versions - General HCP CRM

## 📋 Vue d'ensemble

Ce document explique comment gérer les versions de l'application General HCP CRM pour s'assurer que les nouvelles versions sont correctement déployées et que les utilisateurs voient les changements.

## 🔢 Système de Versioning

L'application utilise le format de version sémantique : `MAJOR.MINOR.PATCH+BUILD`

- **MAJOR** : Changements incompatibles avec les versions précédentes
- **MINOR** : Nouvelles fonctionnalités compatibles
- **PATCH** : Corrections de bugs
- **BUILD** : Numéro de build incrémental

### Version Actuelle : **2.0.0+2**

## 🛠️ Scripts Automatisés

### 1. Mise à jour de version

```bash
# Mettre à jour vers une nouvelle version
dart run scripts/update_version.dart 2.1.0

# Avec un numéro de build spécifique
dart run scripts/update_version.dart 2.1.0 5
```

### 2. Build et déploiement

```bash
# Build pour le web (debug)
dart run scripts/build_and_deploy.dart web

# Build pour le web (release)
dart run scripts/build_and_deploy.dart web --release

# Build pour Android
dart run scripts/build_and_deploy.dart android --release

# Build pour Windows
dart run scripts/build_and_deploy.dart windows --release
```

## 📱 Fonctionnalités de Version

### ✅ Détection automatique des mises à jour
- L'application détecte automatiquement les nouvelles versions
- Nettoyage automatique du cache lors des mises à jour
- Affichage des informations de version dans l'application

### 🧹 Gestion du cache
- Cache automatiquement nettoyé lors des mises à jour
- Bouton manuel de nettoyage du cache dans les paramètres
- Suppression des données obsolètes

### 📊 Informations de version
- Page dédiée aux informations de l'application
- Affichage du changelog de la version actuelle
- Dates d'installation et de mise à jour

## 🔄 Processus de Mise à Jour

### 1. Développement
```bash
# 1. Faire les modifications de code
# 2. Mettre à jour la version
dart run scripts/update_version.dart 2.1.0

# 3. Mettre à jour le changelog dans VersionService
# 4. Tester l'application
flutter run --debug

# 5. Build pour production
dart run scripts/build_and_deploy.dart web --release
```

### 2. Déploiement Web
```bash
# Build optimisé pour le web
dart run scripts/build_and_deploy.dart web --release

# Les fichiers sont dans build/web/
# Déployer sur votre serveur web
```

### 3. Déploiement Mobile
```bash
# Android
dart run scripts/build_and_deploy.dart android --release

# L'APK est dans build/app/outputs/flutter-apk/
```

## 🐛 Résolution des Problèmes

### Les changements ne sont pas visibles après mise à jour

1. **Vérifier la version** :
   - Ouvrir l'application
   - Aller dans le dashboard
   - Cliquer sur l'icône ℹ️ (Informations)
   - Vérifier que la version affichée est correcte

2. **Nettoyer le cache** :
   ```bash
   flutter clean
   flutter pub get
   flutter run
   ```

3. **Forcer le nettoyage du cache utilisateur** :
   - Dans l'application : Dashboard → ℹ️ → "Nettoyer le cache"
   - Ou redémarrer l'application

### Cache du navigateur (Web)

Pour le web, ajouter ces en-têtes HTTP sur votre serveur :
```
Cache-Control: no-cache, no-store, must-revalidate
Pragma: no-cache
Expires: 0
```

## 📝 Changelog

### Version 2.0.0 - 08/08/2025

#### ✅ Nouvelles fonctionnalités
- ✅ Système de versioning automatique
- ✅ Page d'informations de l'application
- ✅ Gestion intelligente du cache
- ✅ Scripts de build automatisés

#### 🔧 Améliorations
- ✅ Suppression du doublon d'objectif mensuel
- ✅ Amélioration de la validation d'authentification
- ✅ Messages d'erreur plus clairs
- ✅ Interface utilisateur optimisée

#### 🐛 Corrections de bugs
- ✅ Correction du doublon de carte d'objectif mensuel
- ✅ Amélioration de la gestion des erreurs Firebase
- ✅ Correction des problèmes de cache

#### 🔄 Changements techniques
- ✅ Ajout du service VersionService
- ✅ Nettoyage automatique du cache
- ✅ Optimisation des performances
- ✅ Amélioration de la structure du code

## 🎯 Prochaines Versions

### Version 2.1.0 (Planifiée)
- [ ] Nouvelles fonctionnalités de reporting
- [ ] Amélioration des notifications
- [ ] Interface utilisateur améliorée

### Version 2.2.0 (Planifiée)
- [ ] Synchronisation temps réel améliorée
- [ ] Nouvelles options d'export
- [ ] Fonctionnalités collaboratives

## 📞 Support

En cas de problème avec le versioning :
1. Vérifier ce document
2. Exécuter les scripts de nettoyage
3. Vérifier les logs de l'application
4. Contacter l'équipe de développement
