import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/invoice.dart';
import '../services/invoice_service.dart';

class InvoiceDebugPage extends StatefulWidget {
  const InvoiceDebugPage({super.key});

  @override
  State<InvoiceDebugPage> createState() => _InvoiceDebugPageState();
}

class _InvoiceDebugPageState extends State<InvoiceDebugPage> {
  final List<String> _logs = [];
  List<Invoice> _invoices = [];
  bool _isLoading = false;

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> _testCreateAndLoad() async {
    setState(() {
      _isLoading = true;
      _logs.clear();
    });

    try {
      _addLog('🚀 Début du test création + chargement');

      // 1. Créer une facture de test
      final testInvoice = Invoice(
        id: 'debug_${DateTime.now().millisecondsSinceEpoch}',
        clientName: 'Client Debug',
        clientNumber: '123456789',
        products: 'Produit Debug',
        items: [],
        deliveryLocation: 'Debug Location',
        deliveryPrice: 10.0,
        advance: 0.0,
        subtotal: 100.0,
        total: 110.0,
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
        type: InvoiceType.normale,
      );

      _addLog('📝 Facture de test créée: ${testInvoice.id}');

      // 2. Sauvegarder via le service
      final invoiceService = InvoiceService();
      final savedInvoice = await invoiceService.addInvoice(testInvoice);
      _addLog('✅ Facture sauvegardée: ${savedInvoice.id}');

      // 3. Vérifier dans SharedPreferences directement
      await _checkSharedPreferences();

      // 4. Charger via le service
      final loadedInvoices = await InvoiceService.loadInvoices();
      _addLog('📊 ${loadedInvoices.length} factures chargées via service');

      // 5. Vérifier si notre facture est présente
      final foundInvoice = loadedInvoices.firstWhere(
        (inv) => inv.id == savedInvoice.id,
        orElse: () => throw Exception('Facture non trouvée'),
      );
      _addLog('🎯 Facture trouvée: ${foundInvoice.clientName}');

      setState(() {
        _invoices = loadedInvoices;
      });

      _addLog('🎉 Test réussi!');
    } catch (e) {
      _addLog('❌ Erreur: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _checkSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('hcp_invoices');

      if (jsonString == null) {
        _addLog('⚠️ Aucune donnée dans SharedPreferences');
        return;
      }

      final jsonList = jsonDecode(jsonString) as List;
      _addLog('📦 ${jsonList.length} factures dans SharedPreferences');

      // Afficher les IDs des factures
      for (int i = 0; i < jsonList.length && i < 3; i++) {
        final invoice = jsonList[i] as Map<String, dynamic>;
        _addLog('  - ${invoice['id']}: ${invoice['clientName']}');
      }

      if (jsonList.length > 3) {
        _addLog('  ... et ${jsonList.length - 3} autres');
      }
    } catch (e) {
      _addLog('❌ Erreur lecture SharedPreferences: $e');
    }
  }

  Future<void> _clearAllInvoices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('hcp_invoices');
      _addLog('🧹 Toutes les factures supprimées');

      setState(() {
        _invoices.clear();
      });
    } catch (e) {
      _addLog('❌ Erreur suppression: $e');
    }
  }

  Future<void> _loadInvoices() async {
    try {
      _addLog('🔄 Chargement des factures...');
      final invoices = await InvoiceService.loadInvoices();
      _addLog('📊 ${invoices.length} factures chargées');

      setState(() {
        _invoices = invoices;
      });
    } catch (e) {
      _addLog('❌ Erreur chargement: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Factures'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF2196F3), // Bleu
                Color(0xFF3F51B5), // Indigo
                Color(0xFF9C27B0), // Violet
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Boutons de test
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Tests de Debug',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _testCreateAndLoad,
                            icon: const Icon(Icons.play_arrow),
                            label: const Text('Test Complet'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _loadInvoices,
                            icon: const Icon(Icons.refresh),
                            label: const Text('Charger'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _checkSharedPreferences,
                            icon: const Icon(Icons.storage),
                            label: const Text('Vérifier Storage'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _clearAllInvoices,
                            icon: const Icon(Icons.delete),
                            label: const Text('Tout Supprimer'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red[700],
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Résumé des factures
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Factures Chargées (${_invoices.length})',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    if (_invoices.isEmpty)
                      const Text('Aucune facture trouvée')
                    else
                      Column(
                        children:
                            _invoices.take(5).map((invoice) {
                              return ListTile(
                                dense: true,
                                title: Text(invoice.clientName),
                                subtitle: Text('ID: ${invoice.id}'),
                                trailing: Text('${invoice.total} FCFA'),
                              );
                            }).toList(),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Logs
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'Logs de Debug',
                            style: Theme.of(context).textTheme.titleLarge,
                          ),
                          const Spacer(),
                          if (_isLoading)
                            const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[300]!),
                          ),
                          child:
                              _logs.isEmpty
                                  ? const Text(
                                    'Aucun log. Lancez un test pour commencer.',
                                    style: TextStyle(
                                      fontStyle: FontStyle.italic,
                                    ),
                                  )
                                  : ListView.builder(
                                    itemCount: _logs.length,
                                    itemBuilder: (context, index) {
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 2,
                                        ),
                                        child: Text(
                                          _logs[index],
                                          style: const TextStyle(
                                            fontFamily: 'monospace',
                                            fontSize: 12,
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
