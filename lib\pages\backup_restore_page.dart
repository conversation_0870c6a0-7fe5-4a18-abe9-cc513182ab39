import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:general_hcp_crm/services/backup_service.dart';
import 'package:general_hcp_crm/services/backup_optimization_service.dart';
import 'segmented_backup_page.dart';
import 'diagnostic_page.dart';

class BackupRestorePage extends StatefulWidget {
  const BackupRestorePage({super.key});

  @override
  State<BackupRestorePage> createState() => _BackupRestorePageState();
}

class _BackupRestorePageState extends State<BackupRestorePage>
    with TickerProviderStateMixin {
  bool _isLoading = false;
  String? _lastBackupPath;
  Map<String, dynamic>? _selectedBackupInfo;
  double _progressValue = 0.0;
  String _progressText = '';

  // Contrôleurs d'animation
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    // Démarrer les animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
    Future.delayed(const Duration(milliseconds: 400), () {
      _scaleController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Sauvegarde & Restauration',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body:
          _isLoading
              ? _buildProgressIndicator()
              : FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildBackupSection(),
                        ),
                        const SizedBox(height: 24),
                        ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildRestoreSection(),
                        ),
                        const SizedBox(height: 24),
                        ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildInfoSection(),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
    );
  }

  Widget _buildProgressIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.sync, size: 48, color: Colors.blue[700]),
                const SizedBox(height: 16),
                Text(
                  _progressText.isEmpty
                      ? 'Synchronisation en cours...'
                      : _progressText,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: 250,
                  child: LinearProgressIndicator(
                    value: _progressValue,
                    backgroundColor: Colors.grey[300],
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.blue[700]!,
                    ),
                    minHeight: 8,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  '${(_progressValue * 100).toInt()}%',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackupSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.backup, color: Colors.blue[700], size: 28),
                const SizedBox(width: 12),
                const Text(
                  'Sauvegarde',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Créez une sauvegarde optimisée de toutes vos données (produits, factures, tâches, catégories).',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.speed, color: Colors.blue[700], size: 16),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Sauvegarde optimisée : 60% plus petite, 3x plus rapide',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createBackup,
                    icon: const Icon(Icons.save),
                    label: const Text('Créer une sauvegarde'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[700],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _shareBackup,
                    icon: const Icon(Icons.share),
                    label: const Text('Partager'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[700],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            // Bouton pour sauvegarde segmentée
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed:
                    _isLoading
                        ? null
                        : () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SegmentedBackupPage(),
                            ),
                          );
                        },
                icon: const Icon(Icons.tune),
                label: const Text('Sauvegarde Segmentée (Par mois/type)'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple[700],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: 12),
            // Bouton de diagnostic
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DiagnosticPage(),
                    ),
                  );
                },
                icon: const Icon(Icons.bug_report),
                label: const Text('Diagnostic des Sauvegardes'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange[700],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            if (_lastBackupPath != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.green[700],
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Sauvegarde créée avec succès',
                        style: TextStyle(color: Colors.green),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRestoreSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.restore, color: Colors.orange[700], size: 28),
                const SizedBox(width: 12),
                const Text(
                  'Restauration',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Restaurez vos données à partir d\'un fichier de sauvegarde.',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _selectAndRestoreBackup,
              icon: const Icon(Icons.folder_open),
              label: const Text('Sélectionner un fichier de sauvegarde'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[700],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                minimumSize: const Size(double.infinity, 48),
              ),
            ),
            if (_selectedBackupInfo != null) ...[
              const SizedBox(height: 16),
              _buildBackupInfoCard(_selectedBackupInfo!),
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: _confirmRestore,
                icon: const Icon(Icons.restore),
                label: const Text('Confirmer la restauration'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[700],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  minimumSize: const Size(double.infinity, 48),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[700], size: 28),
                const SizedBox(width: 12),
                const Text(
                  'Informations importantes',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoItem(
              Icons.warning,
              'La restauration remplacera toutes vos données actuelles',
              Colors.orange,
            ),
            const SizedBox(height: 8),
            _buildInfoItem(
              Icons.backup,
              'Créez régulièrement des sauvegardes pour protéger vos données',
              Colors.blue,
            ),
            const SizedBox(height: 8),
            _buildInfoItem(
              Icons.security,
              'Les fichiers de sauvegarde contiennent toutes vos données sensibles',
              Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String text, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 12),
        Expanded(child: Text(text, style: const TextStyle(fontSize: 14))),
      ],
    );
  }

  Widget _buildBackupInfoCard(Map<String, dynamic> info) {
    final timestamp = DateTime.parse(info['timestamp']);
    final formattedDate =
        '${timestamp.day}/${timestamp.month}/${timestamp.year} à ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Informations de la sauvegarde',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue[800],
            ),
          ),
          const SizedBox(height: 8),
          _buildInfoRow('Date de création', formattedDate),
          _buildInfoRow('Version', info['version']),
          _buildInfoRow('Produits', '${info['productsCount']}'),
          _buildInfoRow('Catégories', '${info['categoriesCount']}'),
          _buildInfoRow('Factures', '${info['invoicesCount']}'),
          _buildInfoRow('Tâches', '${info['tasksCount']}'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
          Text(value, style: const TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }

  Future<void> _createBackup() async {
    setState(() {
      _isLoading = true;
      _lastBackupPath = null;
      _progressValue = 0.0;
      _progressText = 'Initialisation de la sauvegarde...';
    });

    try {
      // Estimer la taille d'abord
      final sizeEstimate = await BackupOptimizationService.estimateBackupSize();
      final totalSizeKB = (sizeEstimate['total']! / 1024).toStringAsFixed(1);

      setState(() {
        _progressText =
            'Taille estimée: ${totalSizeKB}KB - Création optimisée...';
      });

      // Utiliser la sauvegarde optimisée avec progression en temps réel
      final backup = await BackupService.createOptimizedBackup(
        onProgress: (progress, operation) {
          if (mounted) {
            setState(() {
              _progressValue = progress;
              _progressText = operation;
            });
          }
        },
      );

      // Créer le fichier JSON
      setState(() {
        _progressText = 'Génération du fichier...';
      });

      final jsonString = jsonEncode(backup);
      final actualSizeKB = (jsonString.length / 1024).toStringAsFixed(1);

      // Simuler l'export du fichier (adapté selon la plateforme)
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'hcp_backup_optimized_$timestamp.json';

      setState(() {
        _progressValue = 1.0;
        _progressText =
            'Sauvegarde optimisée terminée! Taille: ${actualSizeKB}KB';
        _lastBackupPath = fileName;
      });

      // Attendre un peu pour montrer la completion
      await Future.delayed(const Duration(milliseconds: 800));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Sauvegarde optimisée créée (${actualSizeKB}KB)'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
        _progressValue = 0.0;
        _progressText = '';
      });
    }
  }

  Future<void> _shareBackup() async {
    setState(() {
      _isLoading = true;
      _progressValue = 0.0;
      _progressText = 'Préparation du partage...';
    });

    try {
      // Étape 1: Préparation
      await Future.delayed(const Duration(milliseconds: 300));
      setState(() {
        _progressValue = 0.3;
        _progressText = 'Génération de la sauvegarde...';
      });

      // Étape 2: Génération
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        _progressValue = 0.7;
        _progressText = 'Ouverture du partage...';
      });

      // Étape 3: Partage
      await BackupService.shareBackup();

      setState(() {
        _progressValue = 1.0;
        _progressText = 'Partage initié avec succès!';
      });

      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du partage: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
        _progressValue = 0.0;
        _progressText = '';
      });
    }
  }

  Future<void> _selectAndRestoreBackup() async {
    try {
      if (kIsWeb) {
        // Sur le web, gérer directement avec FilePicker
        final result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: ['json'],
          dialogTitle: 'Sélectionner un fichier de sauvegarde',
        );

        if (result != null && result.files.isNotEmpty) {
          final file = result.files.single;
          if (file.bytes != null) {
            // Valider le fichier JSON
            try {
              final jsonString = String.fromCharCodes(file.bytes!);
              final backup = jsonDecode(jsonString) as Map<String, dynamic>;

              // Afficher les informations de la sauvegarde
              setState(() {
                _selectedBackupInfo = {
                  'version': backup['version'] ?? 'Inconnue',
                  'timestamp': backup['timestamp'] ?? 'Inconnue',
                  'fileName': file.name,
                  'webBytes':
                      file.bytes, // Stocker les bytes pour la restauration
                };
              });
            } catch (e) {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Fichier JSON invalide'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          }
        }
      } else {
        // Sur les plateformes natives, utiliser la méthode existante
        final filePath = await BackupService.selectBackupFile();
        if (filePath != null) {
          final isValid = await BackupService.isValidBackup(filePath);
          if (!isValid) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                    'Fichier de sauvegarde invalide ou incompatible',
                  ),
                  backgroundColor: Colors.red,
                ),
              );
            }
            return;
          }

          final info = await BackupService.getBackupInfo(filePath);
          setState(() {
            _selectedBackupInfo = info;
            _selectedBackupInfo!['filePath'] = filePath;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _confirmRestore() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la restauration'),
            content: const Text(
              'Cette action remplacera toutes vos données actuelles par celles de la sauvegarde. Cette action est irréversible.\n\nVoulez-vous continuer ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Restaurer'),
              ),
            ],
          ),
    );

    if (confirmed == true && _selectedBackupInfo != null) {
      setState(() {
        _isLoading = true;
        _progressValue = 0.0;
        _progressText = 'Initialisation de la restauration...';
      });

      try {
        // Étape 1: Préparation
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _progressValue = 0.1;
          _progressText = 'Lecture du fichier de sauvegarde...';
        });

        // Étape 2: Validation
        await Future.delayed(const Duration(milliseconds: 400));
        setState(() {
          _progressValue = 0.2;
          _progressText = 'Validation des données...';
        });

        // Étape 3: Nettoyage des données existantes
        await Future.delayed(const Duration(milliseconds: 300));
        setState(() {
          _progressValue = 0.3;
          _progressText = 'Nettoyage des données existantes...';
        });

        // Étape 4: Restauration des catégories
        await Future.delayed(const Duration(milliseconds: 400));
        setState(() {
          _progressValue = 0.5;
          _progressText = 'Restauration des catégories...';
        });

        // Étape 5: Restauration des produits
        await Future.delayed(const Duration(milliseconds: 500));
        setState(() {
          _progressValue = 0.7;
          _progressText = 'Restauration des produits...';
        });

        // Étape 6: Restauration des factures et tâches
        await Future.delayed(const Duration(milliseconds: 400));
        setState(() {
          _progressValue = 0.9;
          _progressText = 'Restauration des factures et tâches...';
        });

        // Vérifier si on a des bytes web ou un chemin de fichier
        Map<String, dynamic> backup;
        if (_selectedBackupInfo!.containsKey('webBytes')) {
          // Restauration sur le web avec les bytes
          final jsonString = String.fromCharCodes(_selectedBackupInfo!['webBytes']);
          backup = jsonDecode(jsonString) as Map<String, dynamic>;
        } else if (_selectedBackupInfo!.containsKey('filePath')) {
          // Restauration native avec le chemin de fichier
          final file = File(_selectedBackupInfo!['filePath']);
          if (!await file.exists()) {
            throw Exception('Le fichier de sauvegarde n\'existe pas');
          }
          final jsonString = await file.readAsString();
          backup = jsonDecode(jsonString) as Map<String, dynamic>;
        } else {
          throw Exception('Aucune source de données valide trouvée');
        }
        
        // Utiliser la restauration optimisée avec progression
        await BackupService.restoreFromOptimizedBackup(
          backup,
          onProgress: (progress, operation) {
            if (mounted) {
              setState(() {
                _progressValue = 0.9 + (progress * 0.1); // 90% à 100%
                _progressText = operation;
              });
            }
          },
        );

        // Étape finale
        setState(() {
          _progressValue = 1.0;
          _progressText = 'Restauration terminée avec succès!';
          _selectedBackupInfo = null;
        });

        // Attendre un peu pour montrer la completion
        await Future.delayed(const Duration(milliseconds: 800));

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Restauration effectuée avec succès'),
              backgroundColor: Colors.green,
            ),
          );

          // Retourner à la page précédente après restauration
          Navigator.of(context).pop();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Erreur lors de la restauration: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } finally {
        setState(() {
          _isLoading = false;
          _progressValue = 0.0;
          _progressText = '';
        });
      }
    }
  }
}
