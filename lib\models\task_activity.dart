import 'package:flutter/material.dart';

/// Modèle de commentaire
class TaskComment {
  final String id;
  final String taskId;
  final String authorId;
  final String content;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<String> attachmentIds;
  final String? parentCommentId; // Pour les réponses

  TaskComment({
    required this.id,
    required this.taskId,
    required this.authorId,
    required this.content,
    required this.createdAt,
    this.updatedAt,
    this.attachmentIds = const [],
    this.parentCommentId,
  });

  TaskComment copyWith({
    String? id,
    String? taskId,
    String? authorId,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? attachmentIds,
    String? parentCommentId,
  }) {
    return TaskComment(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      authorId: authorId ?? this.authorId,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      attachmentIds: attachmentIds ?? this.attachmentIds,
      parentCommentId: parentCommentId ?? this.parentCommentId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'authorId': authorId,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'attachmentIds': attachmentIds,
      'parentCommentId': parentCommentId,
    };
  }

  factory TaskComment.fromJson(Map<String, dynamic> json) {
    return TaskComment(
      id: json['id'],
      taskId: json['taskId'],
      authorId: json['authorId'],
      content: json['content'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      attachmentIds: List<String>.from(json['attachmentIds'] ?? []),
      parentCommentId: json['parentCommentId'],
    );
  }
}

/// Modèle de pièce jointe
class TaskAttachment {
  final String id;
  final String taskId;
  final String? commentId;
  final String name;
  final String filePath;
  final String mimeType;
  final int fileSize;
  final String uploadedBy;
  final DateTime uploadedAt;

  TaskAttachment({
    required this.id,
    required this.taskId,
    this.commentId,
    required this.name,
    required this.filePath,
    required this.mimeType,
    required this.fileSize,
    required this.uploadedBy,
    required this.uploadedAt,
  });

  TaskAttachment copyWith({
    String? id,
    String? taskId,
    String? commentId,
    String? name,
    String? filePath,
    String? mimeType,
    int? fileSize,
    String? uploadedBy,
    DateTime? uploadedAt,
  }) {
    return TaskAttachment(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      commentId: commentId ?? this.commentId,
      name: name ?? this.name,
      filePath: filePath ?? this.filePath,
      mimeType: mimeType ?? this.mimeType,
      fileSize: fileSize ?? this.fileSize,
      uploadedBy: uploadedBy ?? this.uploadedBy,
      uploadedAt: uploadedAt ?? this.uploadedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'commentId': commentId,
      'name': name,
      'filePath': filePath,
      'mimeType': mimeType,
      'fileSize': fileSize,
      'uploadedBy': uploadedBy,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }

  factory TaskAttachment.fromJson(Map<String, dynamic> json) {
    return TaskAttachment(
      id: json['id'],
      taskId: json['taskId'],
      commentId: json['commentId'],
      name: json['name'],
      filePath: json['filePath'],
      mimeType: json['mimeType'],
      fileSize: json['fileSize'],
      uploadedBy: json['uploadedBy'],
      uploadedAt: DateTime.parse(json['uploadedAt']),
    );
  }

  String get formattedFileSize {
    if (fileSize < 1024) return '$fileSize B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  IconData get fileIcon {
    if (mimeType.startsWith('image/')) return Icons.image;
    if (mimeType.startsWith('video/')) return Icons.video_file;
    if (mimeType.startsWith('audio/')) return Icons.audio_file;
    if (mimeType.contains('pdf')) return Icons.picture_as_pdf;
    if (mimeType.contains('word') || mimeType.contains('document')) return Icons.description;
    if (mimeType.contains('excel') || mimeType.contains('spreadsheet')) return Icons.table_chart;
    if (mimeType.contains('powerpoint') || mimeType.contains('presentation')) return Icons.slideshow;
    return Icons.attach_file;
  }
}

/// Modèle d'activité/historique
class TaskActivity {
  final String id;
  final String taskId;
  final String userId;
  final ActivityType type;
  final String description;
  final Map<String, dynamic>? oldValues;
  final Map<String, dynamic>? newValues;
  final DateTime createdAt;

  TaskActivity({
    required this.id,
    required this.taskId,
    required this.userId,
    required this.type,
    required this.description,
    this.oldValues,
    this.newValues,
    required this.createdAt,
  });

  TaskActivity copyWith({
    String? id,
    String? taskId,
    String? userId,
    ActivityType? type,
    String? description,
    Map<String, dynamic>? oldValues,
    Map<String, dynamic>? newValues,
    DateTime? createdAt,
  }) {
    return TaskActivity(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      description: description ?? this.description,
      oldValues: oldValues ?? this.oldValues,
      newValues: newValues ?? this.newValues,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'taskId': taskId,
      'userId': userId,
      'type': type.name,
      'description': description,
      'oldValues': oldValues,
      'newValues': newValues,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TaskActivity.fromJson(Map<String, dynamic> json) {
    return TaskActivity(
      id: json['id'],
      taskId: json['taskId'],
      userId: json['userId'],
      type: ActivityType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => ActivityType.other,
      ),
      description: json['description'],
      oldValues: json['oldValues'] != null 
          ? Map<String, dynamic>.from(json['oldValues']) 
          : null,
      newValues: json['newValues'] != null 
          ? Map<String, dynamic>.from(json['newValues']) 
          : null,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

/// Types d'activité
enum ActivityType {
  created('Création'),
  updated('Modification'),
  statusChanged('Changement de statut'),
  assigneeChanged('Changement de responsable'),
  priorityChanged('Changement de priorité'),
  dueDateChanged('Changement d\'échéance'),
  commentAdded('Commentaire ajouté'),
  attachmentAdded('Pièce jointe ajoutée'),
  attachmentRemoved('Pièce jointe supprimée'),
  dependencyAdded('Dépendance ajoutée'),
  dependencyRemoved('Dépendance supprimée'),
  subtaskAdded('Sous-tâche ajoutée'),
  subtaskRemoved('Sous-tâche supprimée'),
  completed('Tâche terminée'),
  reopened('Tâche rouverte'),
  deleted('Suppression'),
  other('Autre');

  const ActivityType(this.displayName);
  final String displayName;
}
