import 'package:flutter/material.dart';
import '../services/data_recovery_service.dart';
import '../services/data_diagnostic_service.dart';
import '../services/unified_sync_service.dart';

class DataRecoveryPage extends StatefulWidget {
  const DataRecoveryPage({super.key});

  @override
  State<DataRecoveryPage> createState() => _DataRecoveryPageState();
}

class _DataRecoveryPageState extends State<DataRecoveryPage> {
  final DataRecoveryService _recoveryService = DataRecoveryService.instance;
  final UnifiedSyncService _syncService = UnifiedSyncService.instance;

  Map<String, dynamic>? _diagnosis;
  Map<String, dynamic>? _fullDiagnostic;
  bool _isLoading = false;
  bool _isRecovering = false;
  bool _isSyncing = false;
  bool _isRunningFullDiagnostic = false;

  @override
  void initState() {
    super.initState();
    _runDiagnosis();
  }

  Future<void> _runDiagnosis() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final diagnosis = await _recoveryService.diagnoseDataProblems();
      setState(() {
        _diagnosis = diagnosis;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du diagnostic: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _recoverOldData() async {
    setState(() {
      _isRecovering = true;
    });

    try {
      final success = await _recoveryService.recoverOldData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? '✅ Données récupérées avec succès'
                  : '⚠️ Aucune donnée à récupérer',
            ),
            backgroundColor: success ? Colors.green : Colors.orange,
          ),
        );

        if (success) {
          // Relancer le diagnostic
          await _runDiagnosis();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la récupération: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isRecovering = false;
      });
    }
  }

  /// Lancer un diagnostic complet avec le nouveau service
  Future<void> _runFullDiagnostic() async {
    setState(() {
      _isRunningFullDiagnostic = true;
    });

    try {
      final diagnostic = await DataDiagnosticService.runFullDiagnostic();
      setState(() {
        _fullDiagnostic = diagnostic;
        _isRunningFullDiagnostic = false;
      });
    } catch (e) {
      setState(() {
        _isRunningFullDiagnostic = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du diagnostic: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Récupérer toutes les factures perdues
  Future<void> _recoverAllInvoices() async {
    setState(() {
      _isRecovering = true;
    });

    try {
      final recoveredInvoices =
          await DataDiagnosticService.recoverAllInvoicesWithFix();

      if (recoveredInvoices.isNotEmpty) {
        final success = await DataDiagnosticService.saveRecoveredInvoices(
          recoveredInvoices,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success
                    ? '✅ ${recoveredInvoices.length} factures récupérées avec succès!'
                    : '❌ Erreur lors de la sauvegarde des factures récupérées',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );

          if (success) {
            // Relancer les diagnostics
            await Future.wait([_runDiagnosis(), _runFullDiagnostic()]);
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('⚠️ Aucune facture trouvée à récupérer'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la récupération: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isRecovering = false;
      });
    }
  }

  /// Récupérer UNIQUEMENT les anciennes factures (sans boutons commande)
  Future<void> _recoverOldInvoicesOnly() async {
    setState(() {
      _isRecovering = true;
    });

    try {
      final recoveredInvoices =
          await DataDiagnosticService.recoverOldInvoicesOnly();

      if (recoveredInvoices.isNotEmpty) {
        final success = await DataDiagnosticService.saveRecoveredInvoices(
          recoveredInvoices,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success
                    ? '✅ ${recoveredInvoices.length} ANCIENNES factures récupérées avec succès!'
                    : '❌ Erreur lors de la sauvegarde des anciennes factures',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );

          if (success) {
            // Relancer les diagnostics
            await Future.wait([_runDiagnosis(), _runFullDiagnostic()]);
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('⚠️ Aucune ancienne facture trouvée à récupérer'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Erreur lors de la récupération des anciennes factures: $e',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isRecovering = false;
      });
    }
  }

  Future<void> _forceSynchronization() async {
    setState(() {
      _isSyncing = true;
    });

    try {
      await _syncService.forceMigration();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ Synchronisation forcée terminée'),
            backgroundColor: Colors.green,
          ),
        );

        // Relancer le diagnostic
        await _runDiagnosis();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors de la synchronisation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSyncing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Récupération des Données'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF2196F3), // Bleu
                Color(0xFF3F51B5), // Indigo
                Color(0xFF9C27B0), // Violet
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _runDiagnosis,
            tooltip: 'Relancer le diagnostic',
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Diagnostic en cours...'),
                  ],
                ),
              )
              : _diagnosis == null
              ? const Center(child: Text('Erreur lors du diagnostic'))
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDiagnosisCard(),
                    const SizedBox(height: 16),
                    _buildLocalDataCard(),
                    const SizedBox(height: 16),
                    _buildFirebaseDataCard(),
                    const SizedBox(height: 16),
                    _buildSyncStatusCard(),
                    const SizedBox(height: 16),
                    _buildProblemsCard(),
                    const SizedBox(height: 16),
                    _buildSolutionsCard(),
                    const SizedBox(height: 16),
                    if (_fullDiagnostic != null) ...[
                      _buildFullDiagnosticCard(),
                      const SizedBox(height: 16),
                    ],
                    _buildActionsCard(),
                  ],
                ),
              ),
    );
  }

  Widget _buildDiagnosisCard() {
    final problems = _diagnosis?['problems'] as List<String>? ?? [];
    final hasProblems = problems.isNotEmpty;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasProblems ? Icons.warning : Icons.check_circle,
                  color: hasProblems ? Colors.orange : Colors.green,
                ),
                const SizedBox(width: 8),
                Text(
                  'État du Système',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              hasProblems
                  ? '${problems.length} problème(s) détecté(s)'
                  : 'Aucun problème détecté',
              style: TextStyle(
                color: hasProblems ? Colors.orange : Colors.green,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLocalDataCard() {
    final localData = _diagnosis?['localData'] as Map<String, dynamic>? ?? {};

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.phone_android, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Données Locales',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildDataRow('Factures', localData['invoices']),
            _buildDataRow('Colis', localData['colis']),
            _buildDataRow('Produits', localData['products']),
            _buildDataRow('Tâches', localData['tasks']),
          ],
        ),
      ),
    );
  }

  Widget _buildFirebaseDataCard() {
    final firebaseData =
        _diagnosis?['firebaseData'] as Map<String, dynamic>? ?? {};
    final isOnline = firebaseData['status'] == 'online';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.cloud, color: isOnline ? Colors.green : Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Données Firebase',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const Spacer(),
                Chip(
                  label: Text(
                    isOnline ? 'En ligne' : 'Hors ligne',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  backgroundColor: isOnline ? Colors.green : Colors.red,
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (isOnline) ...[
              _buildDataRow('Factures', firebaseData['invoices']),
              _buildDataRow('Colis', firebaseData['colis']),
              _buildDataRow('Produits', firebaseData['products']),
              _buildDataRow('Tâches', firebaseData['tasks']),
            ] else
              const Text(
                'Impossible de vérifier les données Firebase (hors ligne)',
                style: TextStyle(color: Colors.red),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSyncStatusCard() {
    final syncStatus = _diagnosis?['syncStatus'] as Map<String, dynamic>? ?? {};
    final migrationComplete = syncStatus['migrationComplete'] ?? false;
    final pendingOps = syncStatus['pendingOperations'] ?? 0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.sync,
                  color: migrationComplete ? Colors.green : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Statut de Synchronisation',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildStatusRow(
              'Migration terminée',
              migrationComplete ? 'Oui' : 'Non',
              migrationComplete,
            ),
            if (syncStatus['lastSync'] != null)
              _buildStatusRow(
                'Dernière sync',
                _formatDate(syncStatus['lastSync']),
                true,
              ),
            if (pendingOps > 0)
              _buildStatusRow(
                'Opérations en attente',
                pendingOps.toString(),
                false,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProblemsCard() {
    final problems = _diagnosis?['problems'] as List<String>? ?? [];

    if (problems.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green),
              const SizedBox(width: 8),
              Text(
                'Aucun problème détecté',
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.warning, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Problèmes Détectés',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...problems.map(
              (problem) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(child: Text(problem)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSolutionsCard() {
    final solutions = _diagnosis?['solutions'] as List<String>? ?? [];

    if (solutions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Solutions Recommandées',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...solutions.map(
              (solution) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.check, color: Colors.green, size: 20),
                    const SizedBox(width: 8),
                    Expanded(child: Text(solution)),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.build, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Actions de Récupération',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRecovering ? null : _recoverOldData,
                icon:
                    _isRecovering
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.restore),
                label: Text(
                  _isRecovering
                      ? 'Récupération en cours...'
                      : 'Récupérer les Anciennes Données',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRunningFullDiagnostic ? null : _runFullDiagnostic,
                icon:
                    _isRunningFullDiagnostic
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.search),
                label: Text(
                  _isRunningFullDiagnostic
                      ? 'Diagnostic en cours...'
                      : 'Diagnostic Complet des Données',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRecovering ? null : _recoverOldInvoicesOnly,
                icon:
                    _isRecovering
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.history),
                label: Text(
                  _isRecovering
                      ? 'Récupération en cours...'
                      : 'Récupérer ANCIENNES Factures UNIQUEMENT',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.deepOrange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isRecovering ? null : _recoverAllInvoices,
                icon:
                    _isRecovering
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.healing),
                label: Text(
                  _isRecovering
                      ? 'Récupération en cours...'
                      : 'Récupérer TOUTES les Factures (avec correction)',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isSyncing ? null : _forceSynchronization,
                icon:
                    _isSyncing
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.sync),
                label: Text(
                  _isSyncing
                      ? 'Synchronisation en cours...'
                      : 'Forcer la Synchronisation',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataRow(String label, Map<String, dynamic>? data) {
    final count = data?['count'] ?? 0;
    final hasData = data?['hasData'] ?? false;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Row(
            children: [
              Text(
                count.toString(),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: hasData ? Colors.green : Colors.grey,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                hasData ? Icons.check_circle : Icons.cancel,
                color: hasData ? Colors.green : Colors.grey,
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, bool isGood) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: isGood ? Colors.green : Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                isGood ? Icons.check_circle : Icons.warning,
                color: isGood ? Colors.green : Colors.orange,
                size: 16,
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 0) {
        return 'Il y a ${difference.inDays} jour(s)';
      } else if (difference.inHours > 0) {
        return 'Il y a ${difference.inHours} heure(s)';
      } else {
        return 'Il y a ${difference.inMinutes} minute(s)';
      }
    } catch (e) {
      return dateStr;
    }
  }

  /// Construire la carte du diagnostic complet
  Widget _buildFullDiagnosticCard() {
    if (_fullDiagnostic == null) return const SizedBox.shrink();

    final totalInvoices = _fullDiagnostic!['totalInvoicesFound'] as int? ?? 0;
    final invoiceKeys =
        _fullDiagnostic!['invoiceKeys'] as Map<String, dynamic>? ?? {};
    final estimatedSize =
        _fullDiagnostic!['estimatedSize'] as Map<String, dynamic>? ?? {};
    final totalMB = estimatedSize['totalMB'] as String? ?? '0';

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.analytics, color: Colors.deepPurple),
                const SizedBox(width: 8),
                Text(
                  'Diagnostic Complet',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Résumé général
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: totalInvoices > 0 ? Colors.green[50] : Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: totalInvoices > 0 ? Colors.green : Colors.red,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '📊 Résumé',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color:
                          totalInvoices > 0
                              ? Colors.green[800]
                              : Colors.red[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('• Total factures trouvées: $totalInvoices'),
                  Text('• Clés avec données: ${invoiceKeys.length}'),
                  Text('• Taille totale: $totalMB MB'),
                ],
              ),
            ),

            if (invoiceKeys.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                '🔍 Détail par clé de stockage:',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              ...invoiceKeys.entries.map((entry) {
                final keyName = entry.key;
                final keyData = entry.value as Map<String, dynamic>;
                final count = keyData['count'] as int? ?? 0;
                final validInvoice = keyData['validInvoice'] as bool? ?? false;

                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: validInvoice ? Colors.blue[50] : Colors.orange[50],
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: validInvoice ? Colors.blue : Colors.orange,
                      width: 0.5,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        validInvoice ? Icons.check_circle : Icons.warning,
                        color: validInvoice ? Colors.blue : Colors.orange,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '$keyName: $count factures',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                      if (validInvoice)
                        const Text(
                          'Valide',
                          style: TextStyle(
                            color: Colors.blue,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                );
              }),
            ],

            if (totalInvoices > 0) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green, width: 1),
                ),
                child: Row(
                  children: [
                    Icon(Icons.lightbulb, color: Colors.green[700]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Vos factures sont présentes ! Utilisez le bouton "Récupérer TOUTES les Factures" pour les restaurer.',
                        style: TextStyle(
                          color: Colors.green[800],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
