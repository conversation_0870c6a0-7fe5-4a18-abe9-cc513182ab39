import 'dart:async';
import 'package:flutter/material.dart';

/// Utilitaires d'optimisation de performance pour l'application
class PerformanceOptimizer {
  static const int _defaultCacheSize = 100;
  static const Duration _defaultCacheDuration = Duration(minutes: 5);

  // Cache pour les widgets coûteux
  static final Map<String, Widget> _widgetCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};

  /// Optimise les setState en les regroupant
  static void batchSetState(List<VoidCallback> updates, State state) {
    if (state.mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        for (final update in updates) {
          update();
        }
      });
    }
  }

  /// Cache un widget coûteux à construire
  static Widget cacheWidget(String key, Widget Function() builder) {
    final now = DateTime.now();
    final timestamp = _cacheTimestamps[key];

    // Vérifier si le cache est valide
    if (timestamp != null &&
        now.difference(timestamp) < _defaultCacheDuration &&
        _widgetCache.containsKey(key)) {
      return _widgetCache[key]!;
    }

    // Construire et mettre en cache
    final widget = builder();
    _widgetCache[key] = widget;
    _cacheTimestamps[key] = now;

    // Nettoyer le cache si nécessaire
    _cleanCache();

    return widget;
  }

  /// Nettoie le cache des widgets expirés
  static void _cleanCache() {
    if (_widgetCache.length > _defaultCacheSize) {
      final now = DateTime.now();
      final expiredKeys = <String>[];

      _cacheTimestamps.forEach((key, timestamp) {
        if (now.difference(timestamp) > _defaultCacheDuration) {
          expiredKeys.add(key);
        }
      });

      for (final key in expiredKeys) {
        _widgetCache.remove(key);
        _cacheTimestamps.remove(key);
      }
    }
  }

  /// Optimise les images réseau avec mise en cache
  static Widget optimizedNetworkImage(
    String url, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return Image.network(
      url,
      width: width,
      height: height,
      fit: fit,
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return placeholder ??
            const Center(child: CircularProgressIndicator(strokeWidth: 2));
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ??
            const Icon(Icons.broken_image, color: Colors.grey);
      },
    );
  }

  /// Débounce pour les recherches et filtres
  static Timer? _debounceTimer;
  static void debounce(Duration delay, VoidCallback callback) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  /// Optimise les listes avec pagination
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    int pageSize = 20,
    ScrollController? controller,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
  }) {
    return ListView.builder(
      controller: controller,
      shrinkWrap: shrinkWrap,
      physics: physics,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // Lazy loading des éléments
        return itemBuilder(context, index);
      },
      cacheExtent: 500, // Cache plus d'éléments
    );
  }

  /// Réduit les animations simultanées
  static void staggerAnimations(
    List<AnimationController> controllers,
    Duration delay,
  ) {
    for (int i = 0; i < controllers.length; i++) {
      Timer(Duration(milliseconds: delay.inMilliseconds * i), () {
        try {
          controllers[i].forward();
        } catch (e) {
          // Controller peut être disposé
        }
      });
    }
  }

  /// Optimise les couleurs pour éviter les recalculs
  static const Map<String, Color> _colorCache = {
    'primary': Color(0xFF1565C0),
    'secondary': Color(0xFF0D47A1),
    'success': Color(0xFF4CAF50),
    'warning': Color(0xFFFF9800),
    'error': Color(0xFFF44336),
    'info': Color(0xFF2196F3),
  };

  static Color getColor(String name) {
    return _colorCache[name] ?? Colors.grey;
  }

  /// Précharge les assets critiques
  static Future<void> preloadAssets(BuildContext context) async {
    final assets = [
      'assets/images/logo.png',
      'assets/qr_codes/QR_Site/qr_site.png',
      'assets/qr_codes/QR_WhatsApp/qr_whatsapp.png',
      'assets/qr_codes/QR_Paiement_Wave/qr_wave.png',
      'assets/qr_codes/QR_Paiement_Orange/qr_orange.png',
    ];

    for (final asset in assets) {
      try {
        await precacheImage(AssetImage(asset), context);
      } catch (e) {
        // Asset non trouvé, continuer
      }
    }
  }

  /// Nettoie tous les caches
  static void clearAllCaches() {
    _widgetCache.clear();
    _cacheTimestamps.clear();
    _debounceTimer?.cancel();
  }
}

/// Mixin pour optimiser les StatefulWidgets
mixin PerformanceOptimizedState<T extends StatefulWidget> on State<T> {
  bool _isDisposed = false;

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  /// setState optimisé qui vérifie si le widget est toujours monté
  void safeSetState(VoidCallback fn) {
    if (!_isDisposed && mounted) {
      setState(fn);
    }
  }

  /// Débounce pour les opérations coûteuses
  void debouncedOperation(VoidCallback operation, [Duration? delay]) {
    PerformanceOptimizer.debounce(
      delay ?? const Duration(milliseconds: 300),
      operation,
    );
  }
}
