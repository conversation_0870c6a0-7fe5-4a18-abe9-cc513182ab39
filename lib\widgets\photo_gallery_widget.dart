import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/order_photo.dart';

/// Widget de galerie de photos pour les commandes
class PhotoGalleryWidget extends StatefulWidget {
  final List<OrderPhoto> photos;
  final Function(OrderPhoto)? onPhotoTap;
  final Function(OrderPhoto)? onPhotoDelete;
  final Function(List<OrderPhoto>)? onSelectionChanged;
  final bool showOcrData;
  final bool allowSelection;

  const PhotoGalleryWidget({
    super.key,
    required this.photos,
    this.onPhotoTap,
    this.onPhotoDelete,
    this.onSelectionChanged,
    this.showOcrData = true,
    this.allowSelection = false,
  });

  @override
  State<PhotoGalleryWidget> createState() => _PhotoGalleryWidgetState();
}

class _PhotoGalleryWidgetState extends State<PhotoGalleryWidget> {
  final Set<String> _selectedPhotos = <String>{};

  @override
  Widget build(BuildContext context) {
    if (widget.photos.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // En-tête avec nombre de photos
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${widget.photos.length} photo${widget.photos.length > 1 ? 's' : ''}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (widget.allowSelection)
              TextButton.icon(
                onPressed: _toggleSelectAll,
                icon: Icon(
                  _selectedPhotos.length == widget.photos.length
                      ? Icons.deselect
                      : Icons.select_all,
                  size: 18,
                ),
                label: Text(
                  _selectedPhotos.length == widget.photos.length
                      ? 'Désélectionner'
                      : 'Tout sélectionner',
                ),
              ),
          ],
        ),

        // Affichage du nombre de photos sélectionnées
        if (widget.allowSelection && _selectedPhotos.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              '${_selectedPhotos.length} photo${_selectedPhotos.length > 1 ? 's' : ''} sélectionnée${_selectedPhotos.length > 1 ? 's' : ''}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

        const SizedBox(height: 12),

        // Grille de photos
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 0.8,
          ),
          itemCount: widget.photos.length,
          itemBuilder: (context, index) {
            final photo = widget.photos[index];
            return _buildPhotoCard(context, photo);
          },
        ),
      ],
    );
  }

  /// Basculer la sélection de toutes les photos
  void _toggleSelectAll() {
    setState(() {
      if (_selectedPhotos.length == widget.photos.length) {
        _selectedPhotos.clear();
      } else {
        _selectedPhotos.clear();
        _selectedPhotos.addAll(widget.photos.map((p) => p.id));
      }
    });

    _notifySelectionChanged();
  }

  /// Basculer la sélection d'une photo
  void _togglePhotoSelection(OrderPhoto photo) {
    setState(() {
      if (_selectedPhotos.contains(photo.id)) {
        _selectedPhotos.remove(photo.id);
      } else {
        _selectedPhotos.add(photo.id);
      }
    });

    _notifySelectionChanged();
  }

  /// Notifier le parent des changements de sélection
  void _notifySelectionChanged() {
    if (widget.onSelectionChanged != null) {
      final selectedPhotoObjects =
          widget.photos.where((p) => _selectedPhotos.contains(p.id)).toList();
      widget.onSelectionChanged!(selectedPhotoObjects);
    }
  }

  /// Construire l'état vide
  Widget _buildEmptyState() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_camera_outlined,
              size: 32,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 8),
            Text(
              'Aucune photo',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  /// Construire une carte de photo
  Widget _buildPhotoCard(BuildContext context, OrderPhoto photo) {
    final isSelected = _selectedPhotos.contains(photo.id);

    return Card(
      elevation: isSelected ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            isSelected
                ? BorderSide(color: Colors.blue, width: 2)
                : BorderSide.none,
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          if (widget.allowSelection) {
            _togglePhotoSelection(photo);
          } else if (widget.onPhotoTap != null) {
            widget.onPhotoTap!(photo);
          }
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Image
            Expanded(
              flex: 3,
              child: ClipRRect(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(12),
                ),
                child: _buildPhotoImage(photo),
              ),
            ),

            // Informations
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Titre et bouton supprimer
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            photo.displayName,
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (widget.onPhotoDelete != null)
                          InkWell(
                            onTap: () => widget.onPhotoDelete!(photo),
                            child: Icon(
                              Icons.delete_outline,
                              size: 16,
                              color: Colors.red[400],
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 4),

                    // Groupe si applicable
                    if (photo.isGrouped) ...[
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue[50],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.blue[200]!,
                            width: 0.5,
                          ),
                        ),
                        child: Text(
                          photo.groupName ?? 'Groupe',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.blue[700],
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(height: 4),
                    ],

                    // Description si disponible
                    if (photo.description != null &&
                        photo.description!.isNotEmpty) ...[
                      Text(
                        photo.description!,
                        style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                    ],

                    // Indicateur OCR
                    if (widget.showOcrData && photo.hasOcrData)
                      Row(
                        children: [
                          Icon(
                            Icons.text_fields,
                            size: 12,
                            color: Colors.green[600],
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              '${photo.ocrData.length} donnée${photo.ocrData.length > 1 ? 's' : ''} OCR',
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.green[600],
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Construire l'image de la photo
  Widget _buildPhotoImage(OrderPhoto photo) {
    final isSelected = _selectedPhotos.contains(photo.id);

    return Container(
      decoration: BoxDecoration(color: Colors.grey[100]),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // Image compatible web/mobile
          _buildImageWidget(photo.filePath),

          // Overlay de sélection
          if (widget.allowSelection)
            Positioned(
              top: 8,
              left: 8,
              child: Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? Colors.blue
                          : Colors.white.withValues(alpha: 0.8),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.blue : Colors.grey,
                    width: 2,
                  ),
                ),
                child:
                    isSelected
                        ? Icon(Icons.check, size: 16, color: Colors.white)
                        : null,
              ),
            ),

          // Overlay avec informations rapides
          if (widget.showOcrData && photo.hasOcrData)
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(Icons.text_fields, size: 12, color: Colors.white),
              ),
            ),
        ],
      ),
    );
  }

  /// Construire l'image compatible web/mobile
  Widget _buildImageWidget(String imagePath) {
    if (kIsWeb) {
      // Sur le web, utiliser Image.network ou un placeholder
      return Container(
        color: Colors.grey[200],
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.photo, size: 32, color: Colors.grey[400]),
            const SizedBox(height: 4),
            Text(
              'Photo\n(Web)',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 10, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    } else {
      // Sur mobile, utiliser Image.file
      try {
        return Image.file(
          File(imagePath),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return _buildImageError();
          },
        );
      } catch (e) {
        return _buildImageError();
      }
    }
  }

  /// Construire l'état d'erreur d'image
  Widget _buildImageError() {
    return Container(
      color: Colors.grey[200],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.broken_image_outlined, size: 32, color: Colors.grey[400]),
          const SizedBox(height: 4),
          Text(
            'Image\nindisponible',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 10, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }
}

/// Widget pour afficher les détails OCR d'une photo
class PhotoOcrDetailsWidget extends StatelessWidget {
  final OrderPhoto photo;

  const PhotoOcrDetailsWidget({super.key, required this.photo});

  @override
  Widget build(BuildContext context) {
    if (!photo.hasOcrData) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'Aucune donnée OCR disponible',
            style: TextStyle(color: Colors.grey, fontStyle: FontStyle.italic),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Données extraites (OCR)',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ...photo.ocrData.entries.map((entry) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 80,
                      child: Text(
                        '${entry.key}:',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        entry.value,
                        style: const TextStyle(color: Colors.black54),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
