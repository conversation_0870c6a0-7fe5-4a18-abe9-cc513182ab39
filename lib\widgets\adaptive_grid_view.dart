import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/responsive_service.dart';

/// Widget de grille adaptative qui assure une expérience identique
/// entre web et mobile
class AdaptiveGridView extends StatelessWidget {
  final List<Widget> children;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final EdgeInsets? padding;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final double? childAspectRatio;
  final int? forcedCrossAxisCount;

  const AdaptiveGridView({
    super.key,
    required this.children,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
    this.childAspectRatio,
    this.forcedCrossAxisCount,
  });

  @override
  Widget build(BuildContext context) {
    // Pour assurer une expérience identique, on force 2 colonnes partout
    // sauf si explicitement spécifié
    final crossAxisCount = forcedCrossAxisCount ?? 2;
    
    final effectiveMainAxisSpacing = mainAxisSpacing ?? 
        ResponsiveService.getGridSpacing(context);
    final effectiveCrossAxisSpacing = crossAxisSpacing ?? 
        ResponsiveService.getGridSpacing(context);
    final effectiveChildAspectRatio = childAspectRatio ?? 
        ResponsiveService.getCardAspectRatio(context);
    final effectivePadding = padding ?? 
        const EdgeInsets.symmetric(horizontal: 8);

    Widget gridView = GridView.count(
      crossAxisCount: crossAxisCount,
      shrinkWrap: shrinkWrap,
      physics: physics,
      mainAxisSpacing: effectiveMainAxisSpacing,
      crossAxisSpacing: effectiveCrossAxisSpacing,
      childAspectRatio: effectiveChildAspectRatio,
      padding: effectivePadding,
      children: children,
    );

    // Sur web desktop, on peut centrer le contenu
    if (kIsWeb && ResponsiveService.isDesktop(context)) {
      return Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: ResponsiveService.getMaxContentWidth(context),
          ),
          child: gridView,
        ),
      );
    }

    return gridView;
  }
}

/// Widget de grille adaptative avec builder pour de meilleures performances
class AdaptiveGridViewBuilder extends StatelessWidget {
  final int itemCount;
  final Widget Function(BuildContext context, int index) itemBuilder;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final EdgeInsets? padding;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final double? childAspectRatio;
  final int? forcedCrossAxisCount;

  const AdaptiveGridViewBuilder({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
    this.childAspectRatio,
    this.forcedCrossAxisCount,
  });

  @override
  Widget build(BuildContext context) {
    // Pour assurer une expérience identique, on force 2 colonnes partout
    final crossAxisCount = forcedCrossAxisCount ?? 2;
    
    final effectiveMainAxisSpacing = mainAxisSpacing ?? 
        ResponsiveService.getGridSpacing(context);
    final effectiveCrossAxisSpacing = crossAxisSpacing ?? 
        ResponsiveService.getGridSpacing(context);
    final effectiveChildAspectRatio = childAspectRatio ?? 
        ResponsiveService.getCardAspectRatio(context);
    final effectivePadding = padding ?? 
        const EdgeInsets.all(8.0);

    Widget gridView = GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: effectiveCrossAxisSpacing,
        mainAxisSpacing: effectiveMainAxisSpacing,
        childAspectRatio: effectiveChildAspectRatio,
      ),
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      shrinkWrap: shrinkWrap,
      physics: physics,
      padding: effectivePadding,
    );

    // Sur web desktop, on peut centrer le contenu
    if (kIsWeb && ResponsiveService.isDesktop(context)) {
      return Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: ResponsiveService.getMaxContentWidth(context),
          ),
          child: gridView,
        ),
      );
    }

    return gridView;
  }
}

/// Widget conteneur adaptatif pour assurer une expérience uniforme
class AdaptiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;

  const AdaptiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final effectivePadding = padding ?? 
        ResponsiveService.getAdaptivePadding(context);

    Widget container = Container(
      padding: effectivePadding,
      margin: margin,
      child: child,
    );

    // Sur web desktop, on centre le contenu
    if (kIsWeb && ResponsiveService.isDesktop(context)) {
      return Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: ResponsiveService.getMaxContentWidth(context),
          ),
          child: container,
        ),
      );
    }

    return container;
  }
}
