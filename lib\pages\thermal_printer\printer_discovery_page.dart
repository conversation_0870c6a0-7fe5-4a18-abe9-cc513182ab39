import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import '../../models/invoice.dart';
import '../../services/printer_service.dart';
import 'print_settings_page.dart';

class PrinterDiscoveryPage extends StatefulWidget {
  final Invoice? invoice;
  final String? pdfPath;
  final String? documentTitle;

  const PrinterDiscoveryPage({
    super.key,
    this.invoice,
    this.pdfPath,
    this.documentTitle,
  }) : assert(
         invoice != null || (pdfPath != null && documentTitle != null),
         'Either invoice or both pdfPath and documentTitle must be provided',
       );

  @override
  State<PrinterDiscoveryPage> createState() => _PrinterDiscoveryPageState();
}

class _PrinterDiscoveryPageState extends State<PrinterDiscoveryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PrinterService _printerService;
  List<DiscoveredPrinter> _bluetoothPrinters = [];
  List<DiscoveredPrinter> _wifiPrinters = [];
  bool _isScanning = false;
  String? _errorMessage;
  DiscoveredPrinter? _selectedPrinter;
  bool _bluetoothEnabled = false;
  String _bluetoothStatus = 'Vérification...';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _printerService = context.read<PrinterService>();
    _initializePrinterService();
    _checkBluetoothStatus();
  }

  Future<void> _checkBluetoothStatus() async {
    try {
      // Vérifier l'état du Bluetooth
      bool isEnabled = await PrintBluetoothThermal.bluetoothEnabled;
      setState(() {
        _bluetoothEnabled = isEnabled;
        _bluetoothStatus = isEnabled ? 'Activé' : 'Désactivé';
      });
    } catch (e) {
      setState(() {
        _bluetoothEnabled = false;
        _bluetoothStatus = 'Erreur: ${e.toString()}';
      });
    }
  }

  Future<void> _initializePrinterService() async {
    try {
      await _printerService.initialize();
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur d\'initialisation: ${e.toString()}';
      });
    }
  }

  Future<void> _scanForPrinters() async {
    if (_isScanning) return;

    setState(() {
      _isScanning = true;
      _errorMessage = null;
      _bluetoothPrinters.clear();
      _wifiPrinters.clear();
    });

    try {
      // Vérifier d'abord l'état du Bluetooth
      await _checkBluetoothStatus();

      if (!_bluetoothEnabled && _tabController.index == 0) {
        setState(() {
          _errorMessage =
              'Le Bluetooth doit être activé pour scanner les imprimantes.';
        });
        return;
      }

      if (_tabController.index == 0) {
        // Scan Bluetooth
        final printers = await _printerService.scanBluetoothPrinters();
        setState(() {
          _bluetoothPrinters = printers;
        });

        // Tester la connectivité de chaque imprimante Bluetooth
        await _testPrintersConnectivity(_bluetoothPrinters);
      } else {
        // Scan WiFi
        final printers = await _printerService.scanWifiPrinters();
        setState(() {
          _wifiPrinters = printers;
        });

        // Tester la connectivité de chaque imprimante WiFi
        await _testPrintersConnectivity(_wifiPrinters);
      }

      // Afficher un message si aucune imprimante n'est trouvée
      if (_bluetoothPrinters.isEmpty && _wifiPrinters.isEmpty) {
        setState(() {
          _errorMessage =
              'Aucune imprimante détectée. Assurez-vous que votre imprimante est:\n• Allumée\n• En mode appairage/découvrable\n• À proximité de votre appareil';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Erreur de scan: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isScanning = false;
      });
    }
  }

  Future<void> _testPrintersConnectivity(
    List<DiscoveredPrinter> printers,
  ) async {
    for (int i = 0; i < printers.length; i++) {
      try {
        // Test de connectivité rapide pour chaque imprimante
        bool isReachable = await _printerService.testPrinterConnectivity(
          printers[i],
        );
        setState(() {
          printers[i] = printers[i].copyWith(isConnected: isReachable);
        });
      } catch (e) {
        // En cas d'erreur, marquer comme non connectée
        setState(() {
          printers[i] = printers[i].copyWith(isConnected: false);
        });
      }
    }
  }

  Future<void> _connectToPrinter() async {
    if (_selectedPrinter == null) return;

    setState(() {
      _isScanning = true;
      _errorMessage = null;
    });

    try {
      final printerService = Provider.of<PrinterService>(
        context,
        listen: false,
      );

      // Afficher un message de progression
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              const SizedBox(width: 16),
              Text('Connexion à ${_selectedPrinter!.name}...'),
            ],
          ),
          duration: const Duration(seconds: 30),
          backgroundColor: Colors.blue[600],
        ),
      );

      debugPrint(
        '🔗 Tentative de connexion à ${_selectedPrinter!.name} (${_selectedPrinter!.address})',
      );

      bool connected = await printerService.connect(_selectedPrinter!);

      // Vérifier que le widget est toujours monté
      if (!mounted) return;

      // Masquer le snackbar de progression
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (connected) {
        debugPrint('✅ Connexion réussie à ${_selectedPrinter!.name}');

        // Afficher un message de succès
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 16),
                Text('Connecté à ${_selectedPrinter!.name}'),
              ],
            ),
            backgroundColor: Colors.green[600],
            duration: const Duration(seconds: 2),
          ),
        );

        // Attendre un peu pour s'assurer que la connexion est stable
        await Future.delayed(const Duration(milliseconds: 1000));

        // Navigate to print settings
        if (widget.invoice != null) {
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) =>
                        PrintSettingsPage.fromInvoice(invoice: widget.invoice!),
              ),
            );
          }
        } else {
          if (mounted) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) =>
                        PrintSettingsPage.fromPdf(pdfPath: widget.pdfPath!),
              ),
            );
          }
        }
      } else {
        debugPrint('❌ Échec de la connexion à ${_selectedPrinter!.name}');
        throw Exception('Échec de la connexion à l\'imprimante');
      }
    } catch (e) {
      // Vérifier que le widget est toujours monté
      if (!mounted) return;

      // Masquer le snackbar de progression
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      debugPrint('❌ Erreur de connexion: $e');

      String errorMessage = e.toString();

      // Nettoyer le message d'erreur
      if (errorMessage.startsWith('Exception: ')) {
        errorMessage = errorMessage.substring(11);
      }

      // Messages d'erreur spécifiques
      if (errorMessage.contains('Bluetooth désactivé')) {
        errorMessage =
            'Bluetooth désactivé. Veuillez l\'activer dans les paramètres.';
      } else if (errorMessage.contains('Connexion Bluetooth perdue')) {
        errorMessage =
            'Connexion Bluetooth instable. Essayez de:\n• Redémarrer l\'imprimante\n• Vous rapprocher de l\'imprimante\n• Vérifier que l\'imprimante n\'est pas connectée ailleurs';
      } else if (errorMessage.contains('timeout') ||
          errorMessage.contains('Timeout')) {
        errorMessage =
            'Timeout de connexion. Vérifiez que:\n• L\'imprimante est allumée\n• Elle est à proximité (< 10m)\n• Aucun obstacle ne bloque le signal';
      } else if (errorMessage.contains('après 3 tentatives') ||
          errorMessage.contains('après plusieurs tentatives')) {
        errorMessage =
            'Impossible de se connecter après plusieurs tentatives.\n\nVérifiez:\n• L\'imprimante est allumée et prête\n• Elle n\'est pas connectée à un autre appareil\n• Le Bluetooth fonctionne correctement\n\nEssayez de redémarrer l\'imprimante.';
      } else if (errorMessage.contains('Échec de l\'envoi des données')) {
        errorMessage =
            'Problème de communication avec l\'imprimante.\nEssayez de redémarrer l\'imprimante et réessayer.';
      } else if (errorMessage.contains('Échec de la connexion')) {
        errorMessage =
            'Connexion échouée. Vérifiez que:\n• L\'imprimante A70pro_63BD est allumée\n• Elle est en mode appairage\n• Vous êtes à proximité de l\'imprimante';
      }

      setState(() {
        _errorMessage = errorMessage;
      });

      // Afficher aussi un snackbar d'erreur
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 16),
                Expanded(child: Text('Erreur: $errorMessage')),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      setState(() {
        _isScanning = false;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sélection d\'imprimante'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.bluetooth), text: 'Bluetooth Device'),
            Tab(icon: Icon(Icons.wifi), text: 'WiFi Device'),
          ],
        ),
      ),
      body: Column(
        children: [
          // Error message
          if (_errorMessage != null)
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red[600]),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Erreur de connexion',
                          style: TextStyle(
                            color: Colors.red[700],
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _errorMessage!,
                    style: TextStyle(color: Colors.red[700]),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Conseils de dépannage:',
                    style: TextStyle(
                      color: Colors.red[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '• Vérifiez que le Bluetooth est activé\n• Assurez-vous que l\'imprimante est allumée\n• Rapprochez-vous de l\'imprimante\n• Redémarrez l\'imprimante si nécessaire',
                    style: TextStyle(color: Colors.red[600], fontSize: 12),
                  ),
                ],
              ),
            ),

          // Section de diagnostic Bluetooth
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600]),
                    const SizedBox(width: 12),
                    Text(
                      'Diagnostic de connexion',
                      style: TextStyle(
                        color: Colors.blue[700],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildDiagnosticItem(
                  'État Bluetooth',
                  _bluetoothStatus,
                  Icons.bluetooth,
                  _bluetoothEnabled ? Colors.green : Colors.red,
                ),
                _buildDiagnosticItem(
                  'Imprimantes détectées',
                  '${_bluetoothPrinters.length + _wifiPrinters.length}',
                  Icons.print,
                  _bluetoothPrinters.isNotEmpty || _wifiPrinters.isNotEmpty
                      ? Colors.green
                      : Colors.orange,
                ),
                _buildDiagnosticItem(
                  'Imprimante sélectionnée',
                  _selectedPrinter?.name ?? 'Aucune',
                  Icons.check_circle,
                  _selectedPrinter != null ? Colors.green : Colors.grey,
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          await _checkBluetoothStatus();
                          _scanForPrinters();
                        },
                        icon: const Icon(Icons.refresh, size: 16),
                        label: const Text('Actualiser diagnostic'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue[100],
                          foregroundColor: Colors.blue[700],
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                        ),
                      ),
                    ),
                    if (!_bluetoothEnabled) ...[
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () async {
                            try {
                              await PrintBluetoothThermal.bluetoothEnabled;
                              // ignore: use_build_context_synchronously
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Veuillez activer le Bluetooth dans les paramètres système',
                                  ),
                                  backgroundColor: Colors.orange,
                                ),
                              );
                            } catch (e) {
                              // ignore: use_build_context_synchronously
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Erreur: ${e.toString()}'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          },
                          icon: const Icon(Icons.bluetooth, size: 16),
                          label: const Text('Activer Bluetooth'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange[100],
                            foregroundColor: Colors.orange[700],
                            elevation: 0,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Scan button
          Padding(
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isScanning ? null : _scanForPrinters,
                icon:
                    _isScanning
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Icon(Icons.search),
                label: Text(
                  _isScanning ? 'Recherche...' : 'Scanner les imprimantes',
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ),

          // Printer list
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPrinterList(_bluetoothPrinters, PrinterType.bluetooth),
                _buildPrinterList(_wifiPrinters, PrinterType.wifi),
              ],
            ),
          ),

          // Add printer button
          if (_selectedPrinter != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Bouton de connexion principal
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _isScanning ? null : _connectToPrinter,
                      icon:
                          _isScanning
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : const Icon(Icons.add),
                      label: Text(
                        _isScanning
                            ? 'Connexion...'
                            : 'Connecter à ${_selectedPrinter!.name}',
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),

                  // Bouton de reconnexion rapide si erreur
                  if (_errorMessage != null && !_isScanning) ...[
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _connectToPrinter,
                        icon: const Icon(Icons.refresh),
                        label: const Text('Réessayer la connexion'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDiagnosticItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue[700],
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrinterList(List<DiscoveredPrinter> printers, PrinterType type) {
    if (printers.isEmpty && !_isScanning) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == PrinterType.bluetooth
                  ? Icons.bluetooth_disabled
                  : Icons.wifi_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Aucune imprimante trouvée',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Appuyez sur "Scanner" pour rechercher des imprimantes',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: printers.length,
      itemBuilder: (context, index) {
        final printer = printers[index];
        final isSelected = _selectedPrinter?.id == printer.id;

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          elevation: isSelected ? 4 : 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: isSelected ? Colors.blue[600]! : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.all(16),
            leading: Stack(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isSelected ? Colors.blue[600] : Colors.grey[200],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    type == PrinterType.bluetooth
                        ? Icons.bluetooth
                        : Icons.wifi,
                    color:
                        isSelected
                            ? Colors.white
                            : (type == PrinterType.bluetooth
                                ? Colors.blue[600]
                                : Colors.green[600]),
                    size: 24,
                  ),
                ),
                // Indicateur de connectivité
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: printer.isConnected ? Colors.green : Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: Icon(
                      printer.isConnected ? Icons.check : Icons.close,
                      size: 10,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            title: Text(
              printer.name,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                color: isSelected ? Colors.blue[600] : Colors.black87,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  type == PrinterType.bluetooth
                      ? 'MAC: ${printer.address}'
                      : 'IP: ${printer.address}',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color:
                            printer.isConnected
                                ? Colors.green[100]
                                : Colors.red[100],
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        printer.isConnected ? 'Disponible' : 'Non disponible',
                        style: TextStyle(
                          color:
                              printer.isConnected
                                  ? Colors.green[700]
                                  : Colors.red[700],
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            trailing:
                isSelected
                    ? Icon(
                      Icons.check_circle,
                      color: Colors.blue[600],
                      size: 28,
                    )
                    : null,
            onTap: () {
              setState(() {
                _selectedPrinter = isSelected ? null : printer;
              });
            },
          ),
        );
      },
    );
  }
}
