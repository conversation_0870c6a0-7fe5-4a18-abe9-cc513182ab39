# Configuration des Sauvegardes Android - Dossier Downloads

## 📱 **Modifications Apportées**

### **Nouveau Répertoire de Sauvegarde**
Les fichiers de sauvegarde JSON sont maintenant stockés dans :
```
/storage/emulated/0/Download/HCP_Sauvegardes/
```

### **Avantages de cette Configuration**
- ✅ **Accès facile** : Les sauvegardes sont visibles dans l'explorateur de fichiers Android
- ✅ **Partage simplifié** : Les fichiers peuvent être partagés directement depuis le dossier Downloads
- ✅ **Sauvegarde externe** : Les fichiers persistent même après désinstallation de l'app
- ✅ **Organisation** : Sous-dossier dédié "HCP_Sauvegardes" pour éviter l'encombrement

## 🔧 **Fonctionnalités Implémentées**

### **Service de Sauvegarde Modifié**
- **Fichier** : `lib/services/backup_service.dart`
- **Fonction principale** : `_getAndroidBackupDirectory()`
- **Gestion des permissions** : Demande automatique des permissions de stockage
- **Fallback intelligent** : Utilise le répertoire interne si les permissions sont refusées

### **Logique de Répertoire**
1. **Android** : `/storage/emulated/0/Download/HCP_Sauvegardes/`
2. **Autres plateformes** : Répertoire des documents de l'application
3. **Fallback** : Répertoire interne si permissions refusées

## 📋 **Permissions Requises**

### **AndroidManifest.xml** (Déjà configuré)
```xml
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
<uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
```

### **Gestion Runtime**
- Demande automatique des permissions via `permission_handler`
- Gestion gracieuse des refus de permission
- Fallback vers stockage interne si nécessaire

## 🚀 **Utilisation**

### **Création de Sauvegarde**
```dart
// La sauvegarde sera automatiquement créée dans Downloads/HCP_Sauvegardes/
String filePath = await BackupService.exportBackupToFile();
print('Sauvegarde créée: $filePath');
```

### **Nom des Fichiers**
```
hcp_backup_[timestamp].json
```
Exemple : `hcp_backup_1703123456789.json`

## 📁 **Structure des Dossiers**

```
/storage/emulated/0/Download/
└── HCP_Sauvegardes/
    ├── hcp_backup_1703123456789.json
    ├── hcp_backup_1703123567890.json
    └── hcp_backup_1703123678901.json
```

## 🔍 **Débogage**

### **Logs de Débogage**
- Les chemins de sauvegarde sont affichés dans la console
- Messages d'erreur détaillés en cas de problème
- Indication du répertoire utilisé (externe ou interne)

### **Vérification Manuelle**
1. Ouvrir l'explorateur de fichiers Android
2. Naviguer vers `Downloads`
3. Chercher le dossier `HCP_Sauvegardes`
4. Vérifier la présence des fichiers JSON

## ⚠️ **Notes Importantes**

- **Android 11+** : Les permissions de stockage peuvent nécessiter une activation manuelle dans les paramètres
- **Sécurité** : Les fichiers sont stockés en clair, éviter les données sensibles
- **Espace disque** : Surveiller l'espace disponible dans Downloads
- **Nettoyage** : Prévoir un mécanisme de nettoyage des anciennes sauvegardes

## 🔄 **Compatibilité**

- ✅ **Android** : Stockage dans Downloads/HCP_Sauvegardes
- ✅ **iOS** : Répertoire des documents (comportement inchangé)
- ✅ **Web** : Téléchargement direct (comportement inchangé)
- ✅ **Desktop** : Répertoire des documents (comportement inchangé)

---

**Date de modification** : $(date)
**Version** : 2.0.1+3
**Statut** : ✅ Implémenté et testé