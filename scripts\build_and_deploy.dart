import 'dart:io';

/// Script pour build et déploiement avec nettoyage automatique
void main(List<String> args) async {
  print('🚀 Script de build et déploiement');
  
  final platform = args.isNotEmpty ? args[0] : 'web';
  final isRelease = args.contains('--release');
  
  try {
    print('🧹 Nettoyage du cache Flutter...');
    await runCommand('flutter', ['clean']);
    
    print('📦 Récupération des dépendances...');
    await runCommand('flutter', ['pub', 'get']);
    
    print('🔍 Analyse du code...');
    await runCommand('flutter', ['analyze']);
    
    if (platform == 'web') {
      await buildWeb(isRelease);
    } else if (platform == 'android') {
      await buildAndroid(isRelease);
    } else if (platform == 'windows') {
      await buildWindows(isRelease);
    } else {
      print('❌ Plateforme non supportée: $platform');
      print('   Plateformes disponibles: web, android, windows');
      exit(1);
    }
    
    print('✅ Build terminé avec succès!');
    
  } catch (e) {
    print('❌ Erreur lors du build: $e');
    exit(1);
  }
}

/// Build pour le web
Future<void> buildWeb(bool isRelease) async {
  print('🌐 Build pour le web...');
  
  final args = ['build', 'web'];
  
  if (isRelease) {
    args.addAll(['--release', '--web-renderer', 'html']);
  } else {
    args.add('--debug');
  }
  
  // Ajouter des optimisations pour le cache
  args.addAll([
    '--dart-define=FLUTTER_WEB_USE_SKIA=false',
    '--dart-define=FLUTTER_WEB_AUTO_DETECT=false',
  ]);
  
  await runCommand('flutter', args);
  
  if (isRelease) {
    print('📁 Fichiers de build disponibles dans: build/web/');
    print('🚀 Prêt pour le déploiement!');
    
    // Créer un fichier de version pour le cache busting
    await createVersionFile();
  }
}

/// Build pour Android
Future<void> buildAndroid(bool isRelease) async {
  print('📱 Build pour Android...');
  
  final args = ['build', 'apk'];
  
  if (isRelease) {
    args.add('--release');
  } else {
    args.add('--debug');
  }
  
  await runCommand('flutter', args);
  
  if (isRelease) {
    print('📁 APK disponible dans: build/app/outputs/flutter-apk/');
  }
}

/// Build pour Windows
Future<void> buildWindows(bool isRelease) async {
  print('🖥️  Build pour Windows...');
  
  final args = ['build', 'windows'];
  
  if (isRelease) {
    args.add('--release');
  } else {
    args.add('--debug');
  }
  
  await runCommand('flutter', args);
  
  if (isRelease) {
    print('📁 Exécutable disponible dans: build/windows/runner/Release/');
  }
}

/// Créer un fichier de version pour le cache busting
Future<void> createVersionFile() async {
  final pubspecFile = File('pubspec.yaml');
  final content = await pubspecFile.readAsString();
  
  final versionLine = content
      .split('\n')
      .firstWhere((line) => line.startsWith('version:'));
  
  final version = versionLine.split(':')[1].trim();
  
  final versionFile = File('build/web/version.json');
  await versionFile.writeAsString('''
{
  "version": "$version",
  "buildTime": "${DateTime.now().toIso8601String()}",
  "platform": "web"
}
''');
  
  print('📝 Fichier de version créé: build/web/version.json');
}

/// Exécuter une commande
Future<void> runCommand(String command, List<String> args) async {
  print('⚡ Exécution: $command ${args.join(' ')}');
  
  final result = await Process.run(command, args);
  
  if (result.exitCode != 0) {
    print('❌ Erreur lors de l\'exécution de: $command ${args.join(' ')}');
    print('Sortie d\'erreur: ${result.stderr}');
    throw Exception('Commande échouée avec le code: ${result.exitCode}');
  }
  
  if (result.stdout.toString().isNotEmpty) {
    print(result.stdout);
  }
}
