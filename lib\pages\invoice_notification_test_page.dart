import 'package:flutter/material.dart';
import '../services/invoice_service.dart';
import '../services/data_change_notifier.dart';
import '../models/invoice.dart';
import '../scripts/add_test_deliveries.dart';
import 'dart:async';

class InvoiceNotificationTestPage extends StatefulWidget {
  const InvoiceNotificationTestPage({super.key});

  @override
  State<InvoiceNotificationTestPage> createState() =>
      _InvoiceNotificationTestPageState();
}

class _InvoiceNotificationTestPageState
    extends State<InvoiceNotificationTestPage> {
  final List<String> _logs = [];
  late StreamSubscription<bool> _invoiceSubscription;
  late StreamSubscription<bool> _productSubscription;
  int _invoiceNotificationCount = 0;
  int _productNotificationCount = 0;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _setupListeners();
  }

  void _setupListeners() {
    // Écouter les notifications de factures
    _invoiceSubscription = DataChangeNotifier.instance.invoicesChanged.listen((
      _,
    ) {
      setState(() {
        _invoiceNotificationCount++;
        _addLog('🔔 Notification facture reçue (#$_invoiceNotificationCount)');
      });
    });

    // Écouter les notifications de produits
    _productSubscription = DataChangeNotifier.instance.productsChanged.listen((
      _,
    ) {
      setState(() {
        _productNotificationCount++;
        _addLog('📦 Notification produit reçue (#$_productNotificationCount)');
      });
    });
  }

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
  }

  Future<void> _testCreateInvoiceWithStock() async {
    setState(() {
      _isLoading = true;
      _logs.clear();
    });

    try {
      _addLog('🚀 Test création facture avec déduction stock');

      // Créer une facture de test avec un article du stock
      final testInvoice = Invoice(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        clientName: 'Client Test Notification',
        clientNumber: '123456789',
        products: 'Produit Test',
        items: [
          InvoiceItem(
            id: 'item_test',
            name: 'Article Test Stock',
            price: 100.0,
            quantity: 1,
            isCustom: false,
            isFromStock: true,
            productId: 'test_product_id', // ID fictif pour test
          ),
        ],
        deliveryLocation: 'Test Location',
        deliveryPrice: 10.0,
        advance: 0.0,
        subtotal: 100.0,
        total: 110.0,
        status:
            InvoiceStatus
                .terminee, // Statut terminé pour déclencher la déduction
        createdAt: DateTime.now(),
        type: InvoiceType.normale,
      );

      _addLog('📝 Facture de test créée avec statut payé');

      // Utiliser la méthode avec gamification
      final result = await InvoiceService().addInvoiceWithGamification(
        testInvoice,
      );
      final newInvoice = result['invoice'] as Invoice;
      final reward = result['reward'];

      _addLog('✅ Facture sauvegardée: ${newInvoice.id}');
      if (reward != null) {
        _addLog('🎉 Récompense obtenue');
      }

      // Attendre un peu pour voir les notifications
      await Future.delayed(const Duration(milliseconds: 500));
      _addLog('⏱️ Test terminé - Vérifiez les compteurs de notifications');
    } catch (e) {
      _addLog('❌ Erreur: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testManualNotifications() async {
    _addLog('🔔 Test notifications manuelles');

    DataChangeNotifier.instance.notifyInvoicesChanged();
    _addLog('📄 Notification facture envoyée manuellement');

    await Future.delayed(const Duration(milliseconds: 100));

    DataChangeNotifier.instance.notifyProductsChanged();
    _addLog('📦 Notification produit envoyée manuellement');
  }

  Future<void> _generateTestDeliveries() async {
    setState(() {
      _isLoading = true;
      _logs.clear();
    });

    try {
      _addLog('🚀 Génération des données de test de livraisons...');

      await TestDeliveriesGenerator.generateCompleteTestData();

      _addLog('✅ Données de test générées avec succès');
      _addLog('📊 Vous pouvez maintenant tester le Sommaire des Livraisons');
    } catch (e) {
      _addLog('❌ Erreur lors de la génération: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearLogs() {
    setState(() {
      _logs.clear();
      _invoiceNotificationCount = 0;
      _productNotificationCount = 0;
    });
  }

  @override
  void dispose() {
    _invoiceSubscription.cancel();
    _productSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Notifications Factures'),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Compteurs de notifications
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    const Text(
                      'Compteurs de Notifications',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Column(
                          children: [
                            const Icon(Icons.receipt, color: Colors.blue),
                            const Text('Factures'),
                            Text(
                              '$_invoiceNotificationCount',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            const Icon(Icons.inventory, color: Colors.green),
                            const Text('Produits'),
                            Text(
                              '$_productNotificationCount',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Boutons de test
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testCreateInvoiceWithStock,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[900],
                      foregroundColor: Colors.white,
                    ),
                    child:
                        _isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : const Text('Test Facture + Stock'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testManualNotifications,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Test Manuel'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _generateTestDeliveries,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.teal,
                      foregroundColor: Colors.white,
                    ),
                    child:
                        _isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : const Text('Générer Données Livraisons'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _clearLogs,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Effacer les Logs'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Logs
            const Text(
              'Logs:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[50],
                ),
                child: ListView.builder(
                  itemCount: _logs.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 2),
                      child: Text(
                        _logs[index],
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
