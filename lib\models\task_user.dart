import 'package:flutter/material.dart';
import 'advanced_task.dart';

/// Modèle d'utilisateur pour les tâches
class TaskUser {
  final String id;
  final String name;
  final String email;
  final String? avatarUrl;
  final Color? color;
  final UserRole role;
  final bool isActive;
  final DateTime createdAt;

  TaskUser({
    required this.id,
    required this.name,
    required this.email,
    this.avatarUrl,
    this.color,
    required this.role,
    required this.isActive,
    required this.createdAt,
  });

  TaskUser copyWith({
    String? id,
    String? name,
    String? email,
    String? avatarUrl,
    Color? color,
    UserRole? role,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return TaskUser(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      color: color ?? this.color,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatarUrl': avatarUrl,
      'color': color?.toARGB32(),
      'role': role.name,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory TaskUser.fromJson(Map<String, dynamic> json) {
    return TaskUser(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      avatarUrl: json['avatarUrl'],
      color: json['color'] != null ? Color(json['color']) : null,
      role: UserRole.values.firstWhere(
        (r) => r.name == json['role'],
        orElse: () => UserRole.member,
      ),
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : '?';
  }
}

/// Rôles d'utilisateur
enum UserRole {
  admin('Administrateur'),
  manager('Gestionnaire'),
  member('Membre'),
  viewer('Observateur');

  const UserRole(this.displayName);
  final String displayName;
}

/// Modèle de filtre de tâches
class TaskFilter {
  final String? projectId;
  final String? sectionId;
  final String? assigneeId;
  final List<String> tags;
  final List<TaskStatus> statuses;
  final List<TaskPriority> priorities;
  final DateTime? dueDateFrom;
  final DateTime? dueDateTo;
  final DateTime? startDateFrom;
  final DateTime? startDateTo;
  final bool? isOverdue;
  final bool? hasAttachments;
  final bool? hasComments;
  final String? searchQuery;

  TaskFilter({
    this.projectId,
    this.sectionId,
    this.assigneeId,
    this.tags = const [],
    this.statuses = const [],
    this.priorities = const [],
    this.dueDateFrom,
    this.dueDateTo,
    this.startDateFrom,
    this.startDateTo,
    this.isOverdue,
    this.hasAttachments,
    this.hasComments,
    this.searchQuery,
  });

  TaskFilter copyWith({
    String? projectId,
    String? sectionId,
    String? assigneeId,
    List<String>? tags,
    List<TaskStatus>? statuses,
    List<TaskPriority>? priorities,
    DateTime? dueDateFrom,
    DateTime? dueDateTo,
    DateTime? startDateFrom,
    DateTime? startDateTo,
    bool? isOverdue,
    bool? hasAttachments,
    bool? hasComments,
    String? searchQuery,
  }) {
    return TaskFilter(
      projectId: projectId ?? this.projectId,
      sectionId: sectionId ?? this.sectionId,
      assigneeId: assigneeId ?? this.assigneeId,
      tags: tags ?? this.tags,
      statuses: statuses ?? this.statuses,
      priorities: priorities ?? this.priorities,
      dueDateFrom: dueDateFrom ?? this.dueDateFrom,
      dueDateTo: dueDateTo ?? this.dueDateTo,
      startDateFrom: startDateFrom ?? this.startDateFrom,
      startDateTo: startDateTo ?? this.startDateTo,
      isOverdue: isOverdue ?? this.isOverdue,
      hasAttachments: hasAttachments ?? this.hasAttachments,
      hasComments: hasComments ?? this.hasComments,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'projectId': projectId,
      'sectionId': sectionId,
      'assigneeId': assigneeId,
      'tags': tags,
      'statuses': statuses.map((s) => s.name).toList(),
      'priorities': priorities.map((p) => p.name).toList(),
      'dueDateFrom': dueDateFrom?.toIso8601String(),
      'dueDateTo': dueDateTo?.toIso8601String(),
      'startDateFrom': startDateFrom?.toIso8601String(),
      'startDateTo': startDateTo?.toIso8601String(),
      'isOverdue': isOverdue,
      'hasAttachments': hasAttachments,
      'hasComments': hasComments,
      'searchQuery': searchQuery,
    };
  }

  factory TaskFilter.fromJson(Map<String, dynamic> json) {
    return TaskFilter(
      projectId: json['projectId'],
      sectionId: json['sectionId'],
      assigneeId: json['assigneeId'],
      tags: List<String>.from(json['tags'] ?? []),
      statuses:
          (json['statuses'] as List?)
              ?.map(
                (s) => TaskStatus.values.firstWhere(
                  (status) => status.name == s,
                  orElse: () => TaskStatus.todo,
                ),
              )
              .toList() ??
          [],
      priorities:
          (json['priorities'] as List?)
              ?.map(
                (p) => TaskPriority.values.firstWhere(
                  (priority) => priority.name == p,
                  orElse: () => TaskPriority.medium,
                ),
              )
              .toList() ??
          [],
      dueDateFrom:
          json['dueDateFrom'] != null
              ? DateTime.parse(json['dueDateFrom'])
              : null,
      dueDateTo:
          json['dueDateTo'] != null ? DateTime.parse(json['dueDateTo']) : null,
      startDateFrom:
          json['startDateFrom'] != null
              ? DateTime.parse(json['startDateFrom'])
              : null,
      startDateTo:
          json['startDateTo'] != null
              ? DateTime.parse(json['startDateTo'])
              : null,
      isOverdue: json['isOverdue'],
      hasAttachments: json['hasAttachments'],
      hasComments: json['hasComments'],
      searchQuery: json['searchQuery'],
    );
  }

  bool get isEmpty {
    return projectId == null &&
        sectionId == null &&
        assigneeId == null &&
        tags.isEmpty &&
        statuses.isEmpty &&
        priorities.isEmpty &&
        dueDateFrom == null &&
        dueDateTo == null &&
        startDateFrom == null &&
        startDateTo == null &&
        isOverdue == null &&
        hasAttachments == null &&
        hasComments == null &&
        (searchQuery == null || searchQuery!.isEmpty);
  }
}

/// Modèle de vue sauvegardée
class SavedTaskView {
  final String id;
  final String name;
  final String description;
  final TaskViewType viewType;
  final TaskFilter filter;
  final TaskSortOption sortOption;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime? updatedAt;

  SavedTaskView({
    required this.id,
    required this.name,
    required this.description,
    required this.viewType,
    required this.filter,
    required this.sortOption,
    required this.isDefault,
    required this.createdAt,
    this.updatedAt,
  });

  SavedTaskView copyWith({
    String? id,
    String? name,
    String? description,
    TaskViewType? viewType,
    TaskFilter? filter,
    TaskSortOption? sortOption,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return SavedTaskView(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      viewType: viewType ?? this.viewType,
      filter: filter ?? this.filter,
      sortOption: sortOption ?? this.sortOption,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'viewType': viewType.name,
      'filter': filter.toJson(),
      'sortOption': sortOption.name,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory SavedTaskView.fromJson(Map<String, dynamic> json) {
    return SavedTaskView(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      viewType: TaskViewType.values.firstWhere(
        (v) => v.name == json['viewType'],
        orElse: () => TaskViewType.list,
      ),
      filter: TaskFilter.fromJson(json['filter']),
      sortOption: TaskSortOption.values.firstWhere(
        (s) => s.name == json['sortOption'],
        orElse: () => TaskSortOption.dueDate,
      ),
      isDefault: json['isDefault'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }
}

/// Types de vue
enum TaskViewType {
  list('Liste'),
  kanban('Kanban'),
  calendar('Calendrier'),
  timeline('Timeline');

  const TaskViewType(this.displayName);
  final String displayName;
}

/// Options de tri
enum TaskSortOption {
  dueDate('Date d\'échéance'),
  priority('Priorité'),
  status('Statut'),
  assignee('Responsable'),
  createdDate('Date de création'),
  title('Titre'),
  project('Projet');

  const TaskSortOption(this.displayName);
  final String displayName;
}
