import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Service pour assurer une expérience utilisateur identique
/// entre les versions web et mobile
class PlatformUniformityService {
  static final PlatformUniformityService _instance = 
      PlatformUniformityService._internal();
  factory PlatformUniformityService() => _instance;
  PlatformUniformityService._internal();

  /// Configuration pour forcer l'uniformité
  static const bool _forceUniformExperience = true;

  /// Retourne les mêmes dimensions de grille partout
  static int getUniformGridCrossAxisCount({
    required BuildContext context,
    int? preferredCount,
  }) {
    if (_forceUniformExperience) {
      // Force 2 colonnes partout pour une expérience identique
      return preferredCount ?? 2;
    }
    
    // Sinon, adapte selon la plateforme
    if (kIsWeb) {
      final width = MediaQuery.of(context).size.width;
      if (width > 1200) return 4;
      if (width > 800) return 3;
      return 2;
    }
    return 2;
  }

  /// Retourne les mêmes espacements partout
  static double getUniformSpacing() {
    return 12.0; // Espacement uniforme
  }

  /// Retourne le même ratio d'aspect partout
  static double getUniformAspectRatio() {
    return 1.0; // Ratio uniforme
  }

  /// Retourne les mêmes paddings partout
  static EdgeInsets getUniformPadding() {
    return const EdgeInsets.all(16.0);
  }

  /// Retourne les mêmes marges partout
  static EdgeInsets getUniformMargin() {
    return const EdgeInsets.all(8.0);
  }

  /// Retourne la même taille de police partout
  static double getUniformFontSize(double baseSize) {
    return baseSize; // Pas d'adaptation
  }

  /// Retourne les mêmes couleurs partout
  static ColorScheme getUniformColorScheme() {
    return ColorScheme.fromSeed(
      seedColor: Colors.blue,
      brightness: Brightness.light,
    );
  }

  /// Retourne le même thème partout
  static ThemeData getUniformTheme() {
    return ThemeData(
      colorScheme: getUniformColorScheme(),
      useMaterial3: true,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        elevation: 2,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  /// Retourne les mêmes contraintes de dialog partout
  static BoxConstraints getUniformDialogConstraints(BuildContext context) {
    return BoxConstraints(
      maxWidth: MediaQuery.of(context).size.width * 0.9,
      maxHeight: MediaQuery.of(context).size.height * 0.8,
    );
  }

  /// Détermine si on doit utiliser la navigation identique
  static bool shouldUseUniformNavigation() {
    return _forceUniformExperience;
  }

  /// Retourne la configuration de navigation uniforme
  static NavigationConfig getUniformNavigationConfig() {
    return const NavigationConfig(
      useBottomNavigation: true,
      useFloatingNavigation: true,
      useDrawer: false, // Pas de drawer pour l'uniformité
      showTabBar: false,
    );
  }

  /// Retourne les mêmes animations partout
  static Duration getUniformAnimationDuration() {
    return const Duration(milliseconds: 300);
  }

  /// Retourne la même courbe d'animation partout
  static Curve getUniformAnimationCurve() {
    return Curves.easeInOut;
  }

  /// Vérifie si on est sur web mais qu'on veut une expérience mobile
  static bool isWebWithMobileExperience() {
    return kIsWeb && _forceUniformExperience;
  }

  /// Retourne les mêmes propriétés de scroll partout
  static ScrollPhysics getUniformScrollPhysics() {
    return const BouncingScrollPhysics(); // Même sur web
  }

  /// Retourne la même configuration de performance partout
  static PerformanceConfig getUniformPerformanceConfig() {
    return const PerformanceConfig(
      enableAnimations: true,
      maxCacheSize: 100,
      imageQuality: 0.8,
      enableLazyLoading: true,
    );
  }

  /// Applique les paramètres uniformes à un widget
  static Widget applyUniformStyling({
    required Widget child,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    return Container(
      padding: padding ?? getUniformPadding(),
      margin: margin ?? getUniformMargin(),
      child: child,
    );
  }

  /// Crée un GridView uniforme
  static Widget createUniformGridView({
    required List<Widget> children,
    int? crossAxisCount,
    double? childAspectRatio,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    EdgeInsets? padding,
  }) {
    return GridView.count(
      crossAxisCount: crossAxisCount ?? 2,
      childAspectRatio: childAspectRatio ?? getUniformAspectRatio(),
      mainAxisSpacing: getUniformSpacing(),
      crossAxisSpacing: getUniformSpacing(),
      shrinkWrap: shrinkWrap,
      physics: physics ?? getUniformScrollPhysics(),
      padding: padding ?? getUniformPadding(),
      children: children,
    );
  }

  /// Crée un GridView.builder uniforme
  static Widget createUniformGridViewBuilder({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    int? crossAxisCount,
    double? childAspectRatio,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    EdgeInsets? padding,
  }) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount ?? 2,
        childAspectRatio: childAspectRatio ?? getUniformAspectRatio(),
        mainAxisSpacing: getUniformSpacing(),
        crossAxisSpacing: getUniformSpacing(),
      ),
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      shrinkWrap: shrinkWrap,
      physics: physics ?? getUniformScrollPhysics(),
      padding: padding ?? getUniformPadding(),
    );
  }
}

/// Configuration de navigation uniforme
class NavigationConfig {
  final bool useBottomNavigation;
  final bool useFloatingNavigation;
  final bool useDrawer;
  final bool showTabBar;

  const NavigationConfig({
    required this.useBottomNavigation,
    required this.useFloatingNavigation,
    required this.useDrawer,
    required this.showTabBar,
  });
}

/// Configuration de performance uniforme
class PerformanceConfig {
  final bool enableAnimations;
  final int maxCacheSize;
  final double imageQuality;
  final bool enableLazyLoading;

  const PerformanceConfig({
    required this.enableAnimations,
    required this.maxCacheSize,
    required this.imageQuality,
    required this.enableLazyLoading,
  });
}
