#!/bin/bash

echo "========================================"
echo "    DEPLOIEMENT WEB HCP CRM"
echo "========================================"
echo

echo "[1/4] Construction de l'application web..."
flutter build web --release --web-renderer html
if [ $? -ne 0 ]; then
    echo "ERREUR: Échec de la construction web"
    exit 1
fi
echo "✅ Construction terminée"
echo

echo "[2/4] Optimisation des fichiers..."
cd build/web
echo "✅ Optimisation terminée"
echo

echo "[3/4] Test local du build..."
echo "L'application web est prête dans le dossier: build/web"
echo "Pour tester localement, ouvrez index.html dans votre navigateur"
echo "ou lancez: python3 -m http.server 8000"
echo

echo "[4/4] Instructions de déploiement:"
echo
echo "OPTIONS DE DEPLOIEMENT:"
echo
echo "1. GITHUB PAGES (Gratuit):"
echo "   - <PERSON><PERSON><PERSON> le dossier build/web vers votre repo GitHub"
echo "   - Allez dans Settings > Pages"
echo "   - Source: Deploy from a branch"
echo "   - Branch: main, folder: /web"
echo
echo "2. NETLIFY (Gratuit):"
echo "   - Allez sur netlify.com"
echo "   - Drag & drop le dossier build/web"
echo "   - Ou connectez votre repo GitHub"
echo
echo "3. VERCEL (Gratuit):"
echo "   - Installez: npm i -g vercel"
echo "   - Dans build/web: vercel --prod"
echo
echo "4. FIREBASE HOSTING (Gratuit):"
echo "   - Installez: npm install -g firebase-tools"
echo "   - firebase init hosting"
echo "   - firebase deploy"
echo
echo "========================================"
echo "    DEPLOIEMENT TERMINE"
echo "========================================"
echo
echo "Votre application web est prête dans: build/web"
echo 