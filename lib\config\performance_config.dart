/// Configuration des optimisations de performance
class PerformanceConfig {
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;
  static const int preloadThreshold = 5; // Éléments avant la fin pour déclencher le chargement
  
  // Cache
  static const Duration shortCacheDuration = Duration(minutes: 1);
  static const Duration mediumCacheDuration = Duration(minutes: 5);
  static const Duration longCacheDuration = Duration(hours: 1);
  static const int maxMemoryCacheSize = 100;
  
  // Images
  static const int maxImageCacheWidth = 800;
  static const int maxImageCacheHeight = 600;
  static const int thumbnailSize = 150;
  static const double imageQuality = 0.8;
  
  // Animations
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration fastAnimationDuration = Duration(milliseconds: 150);
  static const Duration slowAnimationDuration = Duration(milliseconds: 600);
  static const int maxSimultaneousAnimations = 3;
  
  // Debounce
  static const Duration searchDebounceDelay = Duration(milliseconds: 300);
  static const Duration filterDebounceDelay = Duration(milliseconds: 200);
  static const Duration apiCallDebounceDelay = Duration(milliseconds: 500);
  
  // Liste et grilles
  static const double listCacheExtent = 500.0;
  static const int gridCrossAxisCount = 2;
  static const double gridChildAspectRatio = 0.75;
  
  // Réseau
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  // Performance monitoring
  static const bool enablePerformanceLogging = true;
  static const Duration performanceLogInterval = Duration(minutes: 5);
  
  // Seuils de performance
  static const int maxRenderTime = 16; // 60 FPS = 16ms par frame
  static const int maxMemoryUsage = 100; // MB
  static const int maxCpuUsage = 80; // %
  
  /// Obtient la configuration optimale selon le type d'appareil
  static PerformanceProfile getOptimalProfile() {
    // Ici on pourrait détecter le type d'appareil et ajuster
    // Pour l'instant, on retourne un profil équilibré
    return PerformanceProfile.balanced;
  }
}

/// Profils de performance prédéfinis
enum PerformanceProfile {
  lowEnd,    // Appareils peu puissants
  balanced,  // Équilibre performance/qualité
  highEnd,   // Appareils puissants
}

/// Configuration spécifique par profil
class ProfileConfig {
  static Map<PerformanceProfile, Map<String, dynamic>> configs = {
    PerformanceProfile.lowEnd: {
      'pageSize': 10,
      'cacheSize': 50,
      'imageQuality': 0.6,
      'animationDuration': 200,
      'enableAnimations': false,
      'maxSimultaneousOperations': 2,
    },
    PerformanceProfile.balanced: {
      'pageSize': 20,
      'cacheSize': 100,
      'imageQuality': 0.8,
      'animationDuration': 300,
      'enableAnimations': true,
      'maxSimultaneousOperations': 3,
    },
    PerformanceProfile.highEnd: {
      'pageSize': 50,
      'cacheSize': 200,
      'imageQuality': 0.9,
      'animationDuration': 400,
      'enableAnimations': true,
      'maxSimultaneousOperations': 5,
    },
  };
  
  static T getValue<T>(PerformanceProfile profile, String key, T defaultValue) {
    return configs[profile]?[key] as T? ?? defaultValue;
  }
}

/// Métriques de performance
class PerformanceMetrics {
  static final Map<String, List<double>> _metrics = {};
  static final Map<String, DateTime> _lastUpdate = {};
  
  /// Enregistre une métrique
  static void record(String name, double value) {
    if (!PerformanceConfig.enablePerformanceLogging) return;
    
    _metrics[name] ??= [];
    _metrics[name]!.add(value);
    _lastUpdate[name] = DateTime.now();
    
    // Garder seulement les 100 dernières valeurs
    if (_metrics[name]!.length > 100) {
      _metrics[name]!.removeAt(0);
    }
  }
  
  /// Obtient la moyenne d'une métrique
  static double getAverage(String name) {
    final values = _metrics[name];
    if (values == null || values.isEmpty) return 0.0;
    
    return values.reduce((a, b) => a + b) / values.length;
  }
  
  /// Obtient le maximum d'une métrique
  static double getMax(String name) {
    final values = _metrics[name];
    if (values == null || values.isEmpty) return 0.0;
    
    return values.reduce((a, b) => a > b ? a : b);
  }
  
  /// Obtient le minimum d'une métrique
  static double getMin(String name) {
    final values = _metrics[name];
    if (values == null || values.isEmpty) return 0.0;
    
    return values.reduce((a, b) => a < b ? a : b);
  }
  
  /// Obtient toutes les métriques
  static Map<String, Map<String, double>> getAllMetrics() {
    final result = <String, Map<String, double>>{};
    
    for (final name in _metrics.keys) {
      result[name] = {
        'average': getAverage(name),
        'max': getMax(name),
        'min': getMin(name),
        'count': _metrics[name]!.length.toDouble(),
      };
    }
    
    return result;
  }
  
  /// Nettoie les anciennes métriques
  static void cleanup() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    _lastUpdate.forEach((key, lastUpdate) {
      if (now.difference(lastUpdate) > const Duration(hours: 1)) {
        expiredKeys.add(key);
      }
    });
    
    for (final key in expiredKeys) {
      _metrics.remove(key);
      _lastUpdate.remove(key);
    }
  }
}

/// Utilitaires de mesure de performance
class PerformanceMeasure {
  static final Map<String, DateTime> _startTimes = {};
  
  /// Démarre la mesure d'une opération
  static void start(String operation) {
    _startTimes[operation] = DateTime.now();
  }
  
  /// Termine la mesure et enregistre le résultat
  static void end(String operation) {
    final startTime = _startTimes[operation];
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);
      PerformanceMetrics.record(operation, duration.inMilliseconds.toDouble());
      _startTimes.remove(operation);
    }
  }
  
  /// Mesure une fonction et retourne son résultat
  static Future<T> measure<T>(String operation, Future<T> Function() function) async {
    start(operation);
    try {
      final result = await function();
      end(operation);
      return result;
    } catch (e) {
      end(operation);
      rethrow;
    }
  }
  
  /// Mesure une fonction synchrone
  static T measureSync<T>(String operation, T Function() function) {
    start(operation);
    try {
      final result = function();
      end(operation);
      return result;
    } catch (e) {
      end(operation);
      rethrow;
    }
  }
}
