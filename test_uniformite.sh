#!/bin/bash

echo "========================================"
echo "    TEST D'UNIFORMITÉ WEB-MOBILE"
echo "========================================"
echo

echo "[1/5] Analyse du code..."
flutter analyze
if [ $? -ne 0 ]; then
    echo "❌ ERREUR: Problèmes d'analyse détectés"
    exit 1
fi
echo "✅ Analyse réussie"
echo

echo "[2/5] Construction version mobile (debug)..."
flutter build apk --debug
if [ $? -ne 0 ]; then
    echo "❌ ERREUR: Échec de la construction mobile"
    exit 1
fi
echo "✅ Construction mobile réussie"
echo

echo "[3/5] Construction version web..."
flutter build web --release
if [ $? -ne 0 ]; then
    echo "❌ ERREUR: Échec de la construction web"
    exit 1
fi
echo "✅ Construction web réussie"
echo

echo "[4/5] Vérification des fichiers générés..."
if [ ! -d "build/web" ]; then
    echo "❌ ERREUR: Dossier build/web manquant"
    exit 1
fi

if [ ! -f "build/web/index.html" ]; then
    echo "❌ ERREUR: Fichier index.html manquant"
    exit 1
fi

if [ ! -d "build/app/outputs/flutter-apk" ]; then
    echo "❌ ERREUR: Dossier APK manquant"
    exit 1
fi
echo "✅ Fichiers générés présents"
echo

echo "[5/5] Test de démarrage local web..."
echo "Démarrage du serveur web local sur le port 8000..."
echo "Vous pouvez tester l'application web à l'adresse:"
echo "👉 http://localhost:8000"
echo
echo "Pour arrêter le serveur, appuyez sur Ctrl+C"
echo
cd build/web
python3 -m http.server 8000 2>/dev/null || python -m http.server 8000
