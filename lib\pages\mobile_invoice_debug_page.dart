import 'package:flutter/material.dart';
import '../models/invoice.dart';
import '../services/invoice_service.dart';
import '../services/data_change_notifier.dart';
import '../services/mobile_optimization_service.dart';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class MobileInvoiceDebugPage extends StatefulWidget {
  const MobileInvoiceDebugPage({super.key});

  @override
  State<MobileInvoiceDebugPage> createState() => _MobileInvoiceDebugPageState();
}

class _MobileInvoiceDebugPageState extends State<MobileInvoiceDebugPage> {
  List<Invoice> _invoices = [];
  final List<String> _debugLogs = [];
  bool _isLoading = false;
  int _notificationCount = 0;
  StreamSubscription? _subscription;
  bool _isMobile = false;
  bool _isOptimized = false;

  @override
  void initState() {
    super.initState();
    _setupNotificationListener();
    _checkMobileStatus();
    _addLog('🚀 Page de debug mobile initialisée');
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  void _addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _debugLogs.add('[$timestamp] $message');
    });
    debugPrint(message);
  }

  void _clearLogs() {
    setState(() {
      _debugLogs.clear();
      _notificationCount = 0;
    });
  }

  Future<void> _checkMobileStatus() async {
    final mobileService = MobileOptimizationService.instance;
    final isMobile = mobileService.isMobile;
    final isOptimized = await mobileService.shouldUseOptimizedMode();

    setState(() {
      _isMobile = isMobile;
      _isOptimized = isOptimized;
    });

    _addLog('📱 Mobile: $isMobile, Optimisé: $isOptimized');
  }

  void _setupNotificationListener() {
    _subscription = DataChangeNotifier.instance.invoicesChanged.listen((_) {
      setState(() {
        _notificationCount++;
      });
      _addLog('🔔 Notification reçue #$_notificationCount');
      _loadInvoicesWithDebug();
    });
    _addLog('👂 Listener de notifications configuré');
  }

  Future<void> _loadInvoicesWithDebug() async {
    setState(() {
      _isLoading = true;
    });
    _addLog('🔄 Début chargement factures...');

    try {
      final stopwatch = Stopwatch()..start();

      // Test direct SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('hcp_invoices');

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        final localInvoices =
            jsonList.map((json) => Invoice.fromJson(json)).toList();
        _addLog('💾 ${localInvoices.length} factures trouvées en local');
      } else {
        _addLog('❌ Aucune donnée locale trouvée');
      }

      // Test via service
      final invoices = await InvoiceService.loadInvoices();
      stopwatch.stop();

      setState(() {
        _invoices = invoices;
        _isLoading = false;
      });

      _addLog(
        '✅ ${invoices.length} factures chargées en ${stopwatch.elapsedMilliseconds}ms',
      );

      if (invoices.isEmpty) {
        _addLog('⚠️ Liste vide - problème potentiel détecté');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _addLog('❌ Erreur chargement: $e');
    }
  }

  Future<void> _createTestInvoiceWithDebug() async {
    _addLog('🆕 Création facture test...');

    try {
      final testInvoice = Invoice(
        id: 'debug_${DateTime.now().millisecondsSinceEpoch}',
        clientName:
            'Client Debug ${DateTime.now().hour}:${DateTime.now().minute}',
        clientNumber: '999999',
        products: 'Produit Debug',
        items: [],
        deliveryLocation: 'Debug Location',
        deliveryPrice: 5.0,
        advance: 0.0,
        subtotal: 50.0,
        total: 55.0,
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
        type: InvoiceType.normale,
      );

      _addLog('📝 Facture créée: ${testInvoice.id}');

      final stopwatch = Stopwatch()..start();
      final savedInvoice = await InvoiceService().addInvoice(testInvoice);
      stopwatch.stop();

      _addLog('💾 Facture sauvegardée en ${stopwatch.elapsedMilliseconds}ms');
      _addLog('🆔 ID sauvegardé: ${savedInvoice.id}');

      // Attendre un peu pour les notifications asynchrones
      await Future.delayed(const Duration(milliseconds: 500));

      // Vérifier si la facture apparaît dans la liste
      final hasInvoice = _invoices.any((inv) => inv.id == savedInvoice.id);
      _addLog(
        hasInvoice
            ? '✅ Facture trouvée dans la liste'
            : '❌ Facture manquante dans la liste',
      );
    } catch (e) {
      _addLog('❌ Erreur création: $e');
    }
  }

  Future<void> _testNotificationManually() async {
    _addLog('🔔 Test notification manuelle...');
    DataChangeNotifier.instance.notifyInvoicesChanged();
    _addLog('📤 Notification envoyée');
  }

  Future<void> _checkLocalStorage() async {
    _addLog('🔍 Vérification stockage local...');

    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      _addLog('🔑 ${keys.length} clés trouvées: ${keys.take(5).join(", ")}...');

      final invoicesKey = 'hcp_invoices';
      final hasInvoices = prefs.containsKey(invoicesKey);
      _addLog('📋 Clé factures existe: $hasInvoices');

      if (hasInvoices) {
        final jsonString = prefs.getString(invoicesKey);
        final length = jsonString?.length ?? 0;
        _addLog('📏 Taille données: $length caractères');

        if (jsonString != null && jsonString.isNotEmpty) {
          try {
            final jsonList = jsonDecode(jsonString) as List;
            _addLog('📊 ${jsonList.length} factures dans le stockage');
          } catch (e) {
            _addLog('❌ Erreur parsing JSON: $e');
          }
        }
      }
    } catch (e) {
      _addLog('❌ Erreur vérification: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Mobile Factures'),
        backgroundColor: Colors.red,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statut
            Card(
              color: Colors.red[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Statut Mobile',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Mobile: $_isMobile'),
                    Text('Mode optimisé: $_isOptimized'),
                    Text('Factures chargées: ${_invoices.length}'),
                    Text('Notifications: $_notificationCount'),
                    if (_isLoading) const CircularProgressIndicator(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Boutons de test
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton(
                  onPressed: _loadInvoicesWithDebug,
                  child: const Text('Charger'),
                ),
                ElevatedButton(
                  onPressed: _createTestInvoiceWithDebug,
                  child: const Text('Créer Test'),
                ),
                ElevatedButton(
                  onPressed: _testNotificationManually,
                  child: const Text('Test Notif'),
                ),
                ElevatedButton(
                  onPressed: _checkLocalStorage,
                  child: const Text('Vérif Local'),
                ),
                ElevatedButton(
                  onPressed: _clearLogs,
                  child: const Text('Effacer'),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Logs de debug
            Expanded(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Logs de Debug (${_debugLogs.length})',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: ListView.builder(
                          itemCount: _debugLogs.length,
                          itemBuilder: (context, index) {
                            final log =
                                _debugLogs[_debugLogs.length - 1 - index];
                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 2.0,
                              ),
                              child: Text(
                                log,
                                style: const TextStyle(
                                  fontFamily: 'monospace',
                                  fontSize: 12,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
