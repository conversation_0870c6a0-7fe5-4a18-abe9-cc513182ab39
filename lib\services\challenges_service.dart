import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/invoice.dart';
import 'invoice_service.dart';

/// Service pour gérer les défis temporaires
class ChallengesService {
  static final ChallengesService _instance = ChallengesService._internal();
  factory ChallengesService() => _instance;
  ChallengesService._internal();

  static ChallengesService get instance => _instance;

  static const String _activeChallengesKey = 'active_challenges';
  static const String _completedChallengesKey = 'completed_challenges';

  /// Obtenir les défis actifs
  Future<List<Challenge>> getActiveChallenges() async {
    final prefs = await SharedPreferences.getInstance();
    final challengesJson = prefs.getStringList(_activeChallengesKey) ?? [];

    final challenges =
        challengesJson
            .map((json) => Challenge.fromJson(jsonDecode(json)))
            .where((challenge) => challenge.isActive)
            .toList();

    return challenges;
  }

  /// Obtenir les défis complétés
  Future<List<Challenge>> getCompletedChallenges() async {
    final prefs = await SharedPreferences.getInstance();
    final challengesJson = prefs.getStringList(_completedChallengesKey) ?? [];

    return challengesJson
        .map((json) => Challenge.fromJson(jsonDecode(json)))
        .toList();
  }

  /// Créer des défis automatiques selon la période
  Future<void> createAutomaticChallenges() async {
    final now = DateTime.now();
    final activeChallenges = await getActiveChallenges();

    // Éviter de créer des défis en double
    if (activeChallenges.isNotEmpty) return;

    List<Challenge> newChallenges = [];

    // Défis hebdomadaires (lundi)
    if (now.weekday == 1) {
      newChallenges.addAll(_createWeeklyChallenges());
    }

    // Défis mensuels (1er du mois)
    if (now.day == 1) {
      newChallenges.addAll(_createMonthlyChallenges());
    }

    // Défis spéciaux selon les événements
    newChallenges.addAll(_createEventChallenges(now));

    if (newChallenges.isNotEmpty) {
      await _saveChallenges(newChallenges, _activeChallengesKey);
    }
  }

  /// Vérifier la progression des défis
  Future<List<ChallengeProgress>> checkChallengeProgress() async {
    final activeChallenges = await getActiveChallenges();
    final invoices = await InvoiceService.loadInvoices();

    List<ChallengeProgress> progressList = [];
    List<Challenge> completedChallenges = [];

    for (final challenge in activeChallenges) {
      final progress = await _calculateChallengeProgress(challenge, invoices);
      progressList.add(progress);

      if (progress.isCompleted && !challenge.isCompleted) {
        // Marquer le défi comme complété
        final completedChallenge = challenge.copyWith(
          isCompleted: true,
          completedAt: DateTime.now(),
        );
        completedChallenges.add(completedChallenge);
      }
    }

    // Sauvegarder les défis complétés
    if (completedChallenges.isNotEmpty) {
      await _moveToCompleted(completedChallenges);
    }

    return progressList;
  }

  /// Créer des défis hebdomadaires
  List<Challenge> _createWeeklyChallenges() {
    final now = DateTime.now();
    final endOfWeek = now.add(Duration(days: 7 - now.weekday));

    return [
      Challenge(
        id: 'weekly_sales_${now.millisecondsSinceEpoch}',
        title: 'Vendeur de la Semaine',
        description: 'Créer 15 factures cette semaine',
        type: ChallengeType.invoiceCount,
        target: 15,
        reward: ChallengeReward(
          points: 100,
          badge: 'weekly_champion',
          physicalReward: 'Bon d\'achat 5000 FCFA',
        ),
        startDate: now,
        endDate: endOfWeek,
        icon: Icons.emoji_events,
        color: Colors.amber,
      ),
      Challenge(
        id: 'weekly_revenue_${now.millisecondsSinceEpoch}',
        title: 'Objectif CA Hebdomadaire',
        description: 'Atteindre 100 000 FCFA de CA cette semaine',
        type: ChallengeType.revenue,
        target: 100000,
        reward: ChallengeReward(
          points: 150,
          badge: 'revenue_master',
          physicalReward: 'Prime de 10 000 FCFA',
        ),
        startDate: now,
        endDate: endOfWeek,
        icon: Icons.attach_money,
        color: Colors.green,
      ),
    ];
  }

  /// Créer des défis mensuels
  List<Challenge> _createMonthlyChallenges() {
    final now = DateTime.now();
    final endOfMonth = DateTime(now.year, now.month + 1, 0);

    return [
      Challenge(
        id: 'monthly_clients_${now.millisecondsSinceEpoch}',
        title: 'Maître des Clients',
        description: 'Acquérir 50 nouveaux clients ce mois-ci',
        type: ChallengeType.newClients,
        target: 50,
        reward: ChallengeReward(
          points: 300,
          badge: 'client_master',
          physicalReward: 'Smartphone ou équivalent',
        ),
        startDate: now,
        endDate: endOfMonth,
        icon: Icons.people,
        color: Colors.blue,
      ),
      Challenge(
        id: 'monthly_consistency_${now.millisecondsSinceEpoch}',
        title: 'Régularité Parfaite',
        description: 'Créer au moins 1 facture chaque jour du mois',
        type: ChallengeType.consistency,
        target: DateTime(now.year, now.month + 1, 0).day.toDouble(),
        reward: ChallengeReward(
          points: 500,
          badge: 'consistency_king',
          physicalReward: 'Congé payé supplémentaire',
        ),
        startDate: now,
        endDate: endOfMonth,
        icon: Icons.calendar_today,
        color: Colors.purple,
      ),
    ];
  }

  /// Créer des défis spéciaux selon les événements
  List<Challenge> _createEventChallenges(DateTime now) {
    List<Challenge> challenges = [];

    // Défi de fin d'année
    if (now.month == 12) {
      challenges.add(
        Challenge(
          id: 'year_end_${now.year}',
          title: 'Sprint de Fin d\'Année',
          description: 'Doubler vos ventes habituelles en décembre',
          type: ChallengeType.invoiceCount,
          target: 60, // Double de l'objectif mensuel normal
          reward: ChallengeReward(
            points: 1000,
            badge: 'year_end_champion',
            physicalReward: 'Bonus de fin d\'année 50 000 FCFA',
          ),
          startDate: DateTime(now.year, 12, 1),
          endDate: DateTime(now.year, 12, 31),
          icon: Icons.celebration,
          color: Colors.red,
        ),
      );
    }

    // Défi de rentrée (septembre)
    if (now.month == 9) {
      challenges.add(
        Challenge(
          id: 'back_to_school_${now.year}',
          title: 'Rentrée Dynamique',
          description: 'Commencer septembre en force avec 20 factures',
          type: ChallengeType.invoiceCount,
          target: 20,
          reward: ChallengeReward(
            points: 200,
            badge: 'back_to_school',
            physicalReward: 'Kit de bureau premium',
          ),
          startDate: DateTime(now.year, 9, 1),
          endDate: DateTime(now.year, 9, 15),
          icon: Icons.school,
          color: Colors.orange,
        ),
      );
    }

    return challenges;
  }

  /// Calculer la progression d'un défi
  Future<ChallengeProgress> _calculateChallengeProgress(
    Challenge challenge,
    List<Invoice> allInvoices,
  ) async {
    final relevantInvoices =
        allInvoices
            .where(
              (invoice) =>
                  invoice.createdAt.isAfter(challenge.startDate) &&
                  invoice.createdAt.isBefore(challenge.endDate) &&
                  invoice.status == InvoiceStatus.terminee,
            )
            .toList();

    double currentValue = 0;

    switch (challenge.type) {
      case ChallengeType.invoiceCount:
        currentValue = relevantInvoices.length.toDouble();
        break;
      case ChallengeType.revenue:
        currentValue = relevantInvoices.fold(
          0.0,
          (sum, invoice) =>
              sum + InvoiceService().calculateSubtotal(invoice.items),
        );
        break;
      case ChallengeType.newClients:
        final uniqueClients =
            relevantInvoices
                .map((inv) => '${inv.clientName}_${inv.clientNumber}')
                .toSet();
        currentValue = uniqueClients.length.toDouble();
        break;
      case ChallengeType.consistency:
        final daysWithInvoices =
            relevantInvoices
                .map(
                  (inv) => DateTime(
                    inv.createdAt.year,
                    inv.createdAt.month,
                    inv.createdAt.day,
                  ),
                )
                .toSet();
        currentValue = daysWithInvoices.length.toDouble();
        break;
    }

    final progress = (currentValue / challenge.target).clamp(0.0, 1.0);
    final isCompleted = currentValue >= challenge.target;

    return ChallengeProgress(
      challenge: challenge,
      currentValue: currentValue,
      progress: progress,
      isCompleted: isCompleted,
    );
  }

  /// Sauvegarder les défis
  Future<void> _saveChallenges(List<Challenge> challenges, String key) async {
    final prefs = await SharedPreferences.getInstance();
    final challengesJson =
        challenges.map((c) => jsonEncode(c.toJson())).toList();
    await prefs.setStringList(key, challengesJson);
  }

  /// Déplacer les défis vers les complétés
  Future<void> _moveToCompleted(List<Challenge> completedChallenges) async {
    // Ajouter aux complétés
    final existingCompleted = await getCompletedChallenges();
    final allCompleted = [...existingCompleted, ...completedChallenges];
    await _saveChallenges(allCompleted, _completedChallengesKey);

    // Retirer des actifs
    final activeChallenges = await getActiveChallenges();
    final remainingActive =
        activeChallenges
            .where(
              (active) =>
                  !completedChallenges.any(
                    (completed) => completed.id == active.id,
                  ),
            )
            .toList();
    await _saveChallenges(remainingActive, _activeChallengesKey);
  }
}

/// Types de défis
enum ChallengeType { invoiceCount, revenue, newClients, consistency }

/// Modèle pour un défi
class Challenge {
  final String id;
  final String title;
  final String description;
  final ChallengeType type;
  final double target;
  final ChallengeReward reward;
  final DateTime startDate;
  final DateTime endDate;
  final IconData icon;
  final Color color;
  final bool isCompleted;
  final DateTime? completedAt;

  const Challenge({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.target,
    required this.reward,
    required this.startDate,
    required this.endDate,
    required this.icon,
    required this.color,
    this.isCompleted = false,
    this.completedAt,
  });

  bool get isActive => DateTime.now().isBefore(endDate) && !isCompleted;

  Challenge copyWith({
    String? id,
    String? title,
    String? description,
    ChallengeType? type,
    double? target,
    ChallengeReward? reward,
    DateTime? startDate,
    DateTime? endDate,
    IconData? icon,
    Color? color,
    bool? isCompleted,
    DateTime? completedAt,
  }) {
    return Challenge(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      target: target ?? this.target,
      reward: reward ?? this.reward,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.index,
      'target': target,
      'reward': reward.toJson(),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'icon': icon.codePoint,
      'color': color.toARGB32(),
      'isCompleted': isCompleted,
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  static IconData _getIconFromCodePoint(int codePoint) {
    // Mapper les codePoints courants aux icônes constantes
    switch (codePoint) {
      case 0xe24e: // Icons.celebration.codePoint
        return Icons.celebration;
      case 0xe559: // Icons.school.codePoint
        return Icons.school;
      case 0xe06d: // Icons.attach_money.codePoint
        return Icons.attach_money;
      case 0xe7ef: // Icons.group.codePoint
        return Icons.group;
      case 0xef5b: // Icons.trending_up.codePoint
        return Icons.trending_up;
      case 0xe0e6: // Icons.calendar_today.codePoint
        return Icons.calendar_today;
      case 0xe838: // Icons.star.codePoint
        return Icons.star;
      case 0xef4a: // Icons.emoji_events.codePoint
        return Icons.emoji_events;
      default:
        // Fallback à une icône par défaut
        return Icons.emoji_events;
    }
  }

  factory Challenge.fromJson(Map<String, dynamic> json) {
    return Challenge(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      type: ChallengeType.values[json['type']],
      target: json['target'].toDouble(),
      reward: ChallengeReward.fromJson(json['reward']),
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      icon: _getIconFromCodePoint(json['icon']),
      color: Color(
        json['color'],
      ), // Utilisation de Color() pour compatibilité actuelle
      isCompleted: json['isCompleted'] ?? false,
      completedAt:
          json['completedAt'] != null
              ? DateTime.parse(json['completedAt'])
              : null,
    );
  }
}

/// Modèle pour les récompenses de défi
class ChallengeReward {
  final int points;
  final String? badge;
  final String? physicalReward;

  const ChallengeReward({
    required this.points,
    this.badge,
    this.physicalReward,
  });

  Map<String, dynamic> toJson() {
    return {'points': points, 'badge': badge, 'physicalReward': physicalReward};
  }

  factory ChallengeReward.fromJson(Map<String, dynamic> json) {
    return ChallengeReward(
      points: json['points'],
      badge: json['badge'],
      physicalReward: json['physicalReward'],
    );
  }
}

/// Modèle pour la progression d'un défi
class ChallengeProgress {
  final Challenge challenge;
  final double currentValue;
  final double progress;
  final bool isCompleted;

  const ChallengeProgress({
    required this.challenge,
    required this.currentValue,
    required this.progress,
    required this.isCompleted,
  });
}
