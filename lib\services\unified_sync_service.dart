import 'package:shared_preferences/shared_preferences.dart';
import 'firebase_service.dart';
import 'logging_service.dart';
import '../models/invoice.dart';
import '../models/product.dart';
import '../models/colis.dart';
import '../models/task.dart';

/// Service unifié pour synchroniser vos données existantes entre mobile et web
/// Garantit que toutes vos données mobiles apparaissent sur le web
class UnifiedSyncService {
  static final UnifiedSyncService _instance = UnifiedSyncService._internal();
  factory UnifiedSyncService() => _instance;
  UnifiedSyncService._internal();

  static UnifiedSyncService get instance => _instance;

  static const String _migrationCompleteKey = 'unified_migration_complete';
  static const String _lastSyncKey = 'last_unified_sync';

  bool _isSyncing = false;
  bool _migrationComplete = false;

  /// Initialiser le service de synchronisation unifié
  Future<void> initialize() async {
    try {
      LoggingService.info(
        '🔄 Initialisation du service de synchronisation unifié',
        'UNIFIED_SYNC',
      );

      final prefs = await SharedPreferences.getInstance();
      _migrationComplete = prefs.getBool(_migrationCompleteKey) ?? false;

      if (!_migrationComplete) {
        LoggingService.warning(
          '⚠️ Migration des données requise',
          'UNIFIED_SYNC',
        );
        await performFullMigration();
      } else {
        LoggingService.success('✅ Migration déjà effectuée', 'UNIFIED_SYNC');
        // Synchronisation incrémentale
        await performIncrementalSync();
      }
    } catch (e) {
      LoggingService.error(
        '❌ Erreur lors de l\'initialisation',
        'UNIFIED_SYNC',
        e,
      );
    }
  }

  /// Migration complète de toutes vos données mobiles vers le cloud
  Future<void> performFullMigration() async {
    if (_isSyncing) {
      LoggingService.warning('⏳ Synchronisation déjà en cours', 'UNIFIED_SYNC');
      return;
    }

    _isSyncing = true;
    LoggingService.info(
      '🚀 Début de la migration complète de vos données',
      'UNIFIED_SYNC',
    );

    try {
      // 1. Migrer depuis SharedPreferences (données locales mobiles)
      await _migrateFromSharedPreferences();

      // 2. Synchroniser vers Firebase (cloud)
      await _syncToFirebase();

      // 4. Marquer la migration comme terminée
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_migrationCompleteKey, true);
      await prefs.setString(_lastSyncKey, DateTime.now().toIso8601String());

      _migrationComplete = true;
      LoggingService.success(
        '🎉 Migration complète terminée avec succès!',
        'UNIFIED_SYNC',
      );
    } catch (e) {
      LoggingService.error(
        '❌ Erreur lors de la migration complète',
        'UNIFIED_SYNC',
        e,
      );
      rethrow;
    } finally {
      _isSyncing = false;
    }
  }

  /// Migration depuis SharedPreferences (vos données mobiles existantes)
  Future<void> _migrateFromSharedPreferences() async {
    LoggingService.info(
      '📱 Migration des données mobiles locales...',
      'UNIFIED_SYNC',
    );

    final prefs = await SharedPreferences.getInstance();
    int totalMigrated = 0;

    try {
      // Migrer les factures
      final invoicesJson = prefs.getStringList('invoices') ?? [];
      LoggingService.info(
        '📄 ${invoicesJson.length} factures trouvées',
        'UNIFIED_SYNC',
      );

      for (final invoiceJson in invoicesJson) {
        try {
          final invoice = Invoice.fromJson(
            Map<String, dynamic>.from(
              Map.from(Uri.splitQueryString(invoiceJson)),
            ),
          );
          await FirebaseService.instance.addInvoice(invoice);
          totalMigrated++;
        } catch (e) {
          LoggingService.warning(
            '⚠️ Erreur migration facture: $e',
            'UNIFIED_SYNC',
          );
        }
      }

      // Migrer les produits
      final productsJson = prefs.getStringList('products') ?? [];
      LoggingService.info(
        '📦 ${productsJson.length} produits trouvés',
        'UNIFIED_SYNC',
      );

      for (final productJson in productsJson) {
        try {
          Product.fromJson(
            Map<String, dynamic>.from(
              Map.from(Uri.splitQueryString(productJson)),
            ),
          );
          // Les produits ne sont pas synchronisés avec Firebase dans ce projet
          totalMigrated++;
        } catch (e) {
          LoggingService.warning(
            '⚠️ Erreur migration produit: $e',
            'UNIFIED_SYNC',
          );
        }
      }

      // Migrer les colis
      final colisJson = prefs.getStringList('colis') ?? [];
      LoggingService.info(
        '📮 ${colisJson.length} colis trouvés',
        'UNIFIED_SYNC',
      );

      for (final colisJsonString in colisJson) {
        try {
          final colis = Colis.fromJson(
            Map<String, dynamic>.from(
              Map.from(Uri.splitQueryString(colisJsonString)),
            ),
          );
          await FirebaseService.instance.addColis(colis);
          totalMigrated++;
        } catch (e) {
          LoggingService.warning(
            '⚠️ Erreur migration colis: $e',
            'UNIFIED_SYNC',
          );
        }
      }

      // Migrer les tâches
      final tasksJson = prefs.getStringList('tasks') ?? [];
      LoggingService.info(
        '✅ ${tasksJson.length} tâches trouvées',
        'UNIFIED_SYNC',
      );

      for (final taskJson in tasksJson) {
        try {
          final task = Task.fromJson(
            Map<String, dynamic>.from(Map.from(Uri.splitQueryString(taskJson))),
          );
          await FirebaseService.instance.addTask(task);
          totalMigrated++;
        } catch (e) {
          LoggingService.warning(
            '⚠️ Erreur migration tâche: $e',
            'UNIFIED_SYNC',
          );
        }
      }

      // Les catégories restent locales (non synchronisées avec Firebase)
      final categoriesJson = prefs.getStringList('categories') ?? [];
      LoggingService.info(
        '🏷️ ${categoriesJson.length} catégories trouvées (restent locales)',
        'UNIFIED_SYNC',
      );

      LoggingService.success(
        '✅ Migration SharedPreferences: $totalMigrated éléments migrés',
        'UNIFIED_SYNC',
      );
    } catch (e) {
      LoggingService.error(
        '❌ Erreur migration SharedPreferences',
        'UNIFIED_SYNC',
        e,
      );
      rethrow;
    }
  }

  /// Synchronisation vers Firebase (cloud)
  Future<void> _syncToFirebase() async {
    LoggingService.info('🔥 Synchronisation vers Firebase...', 'UNIFIED_SYNC');

    try {
      final firebaseService = FirebaseService.instance;
      await firebaseService.initialize();

      if (firebaseService.isOnline()) {
        LoggingService.success(
          '✅ Synchronisation Firebase terminée',
          'UNIFIED_SYNC',
        );
      } else {
        LoggingService.warning(
          '⚠️ Firebase hors ligne, synchronisation reportée',
          'UNIFIED_SYNC',
        );
      }
    } catch (e) {
      LoggingService.error(
        '❌ Erreur synchronisation Firebase',
        'UNIFIED_SYNC',
        e,
      );
      rethrow;
    }
  }

  /// Synchronisation incrémentale (pour les mises à jour)
  Future<void> performIncrementalSync() async {
    if (_isSyncing) return;

    _isSyncing = true;
    LoggingService.info('🔄 Synchronisation incrémentale...', 'UNIFIED_SYNC');

    try {
      // Synchroniser les changements récents avec Firebase
      final firebaseService = FirebaseService.instance;
      await firebaseService.initialize();

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastSyncKey, DateTime.now().toIso8601String());

      LoggingService.success(
        '✅ Synchronisation incrémentale terminée',
        'UNIFIED_SYNC',
      );
    } catch (e) {
      LoggingService.error(
        '❌ Erreur synchronisation incrémentale',
        'UNIFIED_SYNC',
        e,
      );
    } finally {
      _isSyncing = false;
    }
  }

  /// Forcer une nouvelle migration complète
  Future<void> forceMigration() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_migrationCompleteKey, false);
    _migrationComplete = false;

    await performFullMigration();
  }

  /// Vérifier le statut de la synchronisation
  bool get isSyncing => _isSyncing;
  bool get migrationComplete => _migrationComplete;

  /// Obtenir la dernière date de synchronisation
  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSyncString = prefs.getString(_lastSyncKey);
    return lastSyncString != null ? DateTime.parse(lastSyncString) : null;
  }
}
