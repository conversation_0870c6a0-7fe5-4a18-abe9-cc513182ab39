import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Service pour gérer les couleurs dynamiques de l'application
class ThemeService {
  static ThemeService? _instance;
  static ThemeService get instance => _instance ??= ThemeService._internal();

  ThemeService._internal();

  // Couleurs par page/section
  static const Map<String, PageTheme> _pageThemes = {
    // Pages principales
    'home': PageTheme(
      primary: Color(0xFF1976D2), // Bleu principal
      secondary: Color(0xFF1565C0),
      accent: Color(0xFF42A5F5),
      name: 'Accueil',
    ),
    'invoices': PageTheme(
      primary: Color(0xFF1976D2), // Bleu pour factures
      secondary: Color(0xFF1565C0),
      accent: Color(0xFF42A5F5),
      name: 'Factures',
    ),
    'products': PageTheme(
      primary: Color(0xFF388E3C), // Vert pour produits
      secondary: Color(0xFF2E7D32),
      accent: Color(0xFF66BB6A),
      name: 'Produits',
    ),
    'tasks': PageTheme(
      primary: Color(0xFFFF9800), // Orange pour tâches
      secondary: Color(0xFFF57C00),
      accent: Color(0xFFFFB74D),
      name: 'Tâches',
    ),
    'colis': PageTheme(
      primary: Color(0xFF9C27B0), // Violet pour colis
      secondary: Color(0xFF7B1FA2),
      accent: Color(0xFFBA68C8),
      name: 'Colis',
    ),
    'clients': PageTheme(
      primary: Color(0xFF00796B), // Teal pour clients
      secondary: Color(0xFF00695C),
      accent: Color(0xFF4DB6AC),
      name: 'Clients',
    ),
    'reports': PageTheme(
      primary: Color(0xFF5D4037), // Marron pour rapports
      secondary: Color(0xFF4E342E),
      accent: Color(0xFF8D6E63),
      name: 'Rapports',
    ),
    'settings': PageTheme(
      primary: Color(0xFF455A64), // Gris bleu pour paramètres
      secondary: Color(0xFF37474F),
      accent: Color(0xFF78909C),
      name: 'Paramètres',
    ),
    'backup': PageTheme(
      primary: Color(0xFFE65100), // Orange foncé pour sauvegarde
      secondary: Color(0xFFBF360C),
      accent: Color(0xFFFF8A65),
      name: 'Sauvegarde',
    ),
    'diagnostic': PageTheme(
      primary: Color(0xFFD32F2F), // Rouge pour diagnostic
      secondary: Color(0xFFC62828),
      accent: Color(0xFFEF5350),
      name: 'Diagnostic',
    ),
    'gamification': PageTheme(
      primary: Color(0xFFFFD600), // Jaune pour gamification
      secondary: Color(0xFFFFC107),
      accent: Color(0xFFFFEB3B),
      name: 'Récompenses',
    ),
    'profile': PageTheme(
      primary: Color(0xFF3F51B5), // Indigo pour profil
      secondary: Color(0xFF303F9F),
      accent: Color(0xFF7986CB),
      name: 'Profil',
    ),
  };

  /// Obtenir le thème pour une page donnée
  PageTheme getThemeForPage(String pageKey) {
    return _pageThemes[pageKey] ?? _pageThemes['home']!;
  }

  /// Obtenir le thème par nom de route
  PageTheme getThemeForRoute(String routeName) {
    // Mapper les noms de routes aux clés de thème
    final routeToThemeMap = {
      '/': 'home',
      '/home': 'home',
      '/invoices': 'invoices',
      '/invoice-list': 'invoices',
      '/create-invoice': 'invoices',
      '/products': 'products',
      '/product-list': 'products',
      '/tasks': 'tasks',
      '/task-list': 'tasks',
      '/colis': 'colis',
      '/colis-list': 'colis',
      '/clients': 'clients',
      '/reports': 'reports',
      '/settings': 'settings',
      '/backup': 'backup',
      '/diagnostic': 'diagnostic',
      '/gamification': 'gamification',
      '/profile': 'profile',
    };

    final themeKey = routeToThemeMap[routeName] ?? 'home';
    return getThemeForPage(themeKey);
  }

  /// Appliquer le thème à la barre de statut et navigation
  void applySystemUITheme(PageTheme theme, {bool isDark = false}) {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        // Barre de statut (en haut)
        statusBarColor: theme.primary,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.light,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.dark,

        // Barre de navigation système (en bas) - transparente pour edge-to-edge
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness:
            isDark ? Brightness.light : Brightness.light,
        systemNavigationBarDividerColor: Colors.transparent,
      ),
    );
    
    // Activer le mode edge-to-edge
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  /// Créer un AppBarTheme pour une page
  AppBarTheme createAppBarTheme(PageTheme theme) {
    return AppBarTheme(
      backgroundColor: theme.primary,
      foregroundColor: Colors.white,
      elevation: 4,
      shadowColor: theme.secondary.withValues(alpha: 0.5),
      centerTitle: true,
      titleTextStyle: const TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
      iconTheme: const IconThemeData(color: Colors.white, size: 24),
    );
  }

  /// Créer un thème de bouton flottant
  FloatingActionButtonThemeData createFABTheme(PageTheme theme) {
    return FloatingActionButtonThemeData(
      backgroundColor: theme.accent,
      foregroundColor: Colors.white,
      elevation: 6,
      highlightElevation: 12,
    );
  }

  /// Créer un thème de navigation bottom
  BottomNavigationBarThemeData createBottomNavTheme(PageTheme theme) {
    return BottomNavigationBarThemeData(
      backgroundColor: theme.primary,
      selectedItemColor: Colors.white,
      unselectedItemColor: Colors.white70,
      elevation: 8,
      type: BottomNavigationBarType.fixed,
    );
  }

  /// Obtenir une couleur de contraste pour le texte
  Color getContrastColor(Color backgroundColor) {
    // Calculer la luminance pour déterminer si utiliser du texte clair ou foncé
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }

  /// Créer un gradient pour les cartes
  LinearGradient createCardGradient(PageTheme theme) {
    return LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [theme.primary, theme.secondary],
    );
  }

  /// Obtenir toutes les couleurs disponibles (pour debug/admin)
  Map<String, PageTheme> getAllThemes() {
    return Map.from(_pageThemes);
  }

  /// Créer un thème Material complet pour une page
  ThemeData createPageTheme(PageTheme pageTheme, {bool isDark = false}) {
    return ThemeData(
      useMaterial3: true,
      brightness: isDark ? Brightness.dark : Brightness.light,

      // Couleurs principales
      colorScheme: ColorScheme.fromSeed(
        seedColor: pageTheme.primary,
        brightness: isDark ? Brightness.dark : Brightness.light,
      ),

      // AppBar
      appBarTheme: createAppBarTheme(pageTheme),

      // FAB
      floatingActionButtonTheme: createFABTheme(pageTheme),

      // Bottom Navigation
      bottomNavigationBarTheme: createBottomNavTheme(pageTheme),

      // Cards
      cardTheme: CardTheme(
        elevation: 4,
        shadowColor: pageTheme.secondary.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),

      // Boutons
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: pageTheme.primary,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      ),
    );
  }
}

/// Classe pour définir un thème de page
class PageTheme {
  final Color primary;
  final Color secondary;
  final Color accent;
  final String name;

  const PageTheme({
    required this.primary,
    required this.secondary,
    required this.accent,
    required this.name,
  });

  /// Créer une version plus claire du thème
  PageTheme get light => PageTheme(
    primary: primary.withValues(alpha: 0.8),
    secondary: secondary.withValues(alpha: 0.8),
    accent: accent.withValues(alpha: 0.8),
    name: '$name (Clair)',
  );

  /// Créer une version plus foncée du thème
  PageTheme get dark => PageTheme(
    primary: Color.lerp(primary, Colors.black, 0.2)!,
    secondary: Color.lerp(secondary, Colors.black, 0.2)!,
    accent: Color.lerp(accent, Colors.black, 0.2)!,
    name: '$name (Foncé)',
  );
}
