import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:io';
import 'package:url_launcher/url_launcher.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../models/invoice.dart';
import '../services/invoice_service.dart';
import '../services/pdf_service.dart';
import 'create_invoice_page.dart';
import 'create_proforma_page.dart';
import 'create_order_page.dart';
import 'invoice_detail_page.dart';
import '../constants/app_colors.dart';
import 'commandes_jour_page.dart';
import 'pdf_preview_page.dart';
import '../services/data_change_notifier.dart';
import '../services/theme_service.dart';
import 'thermal_printer/printer_discovery_page.dart';

class InvoiceListPage extends StatefulWidget {
  const InvoiceListPage({super.key});

  @override
  State<InvoiceListPage> createState() => _InvoiceListPageState();
}

class _InvoiceListPageState extends State<InvoiceListPage> {
  List<Invoice> _invoices = [];
  List<Invoice> _filteredInvoices = [];
  final List<Invoice> _selectedInvoices = <Invoice>[];
  final Set<String> _generatingMiniPDFInvoices = <String>{};
  InvoiceStatus? _selectedStatus;
  final TextEditingController _searchController = TextEditingController();
  bool _selectionMode = false;
  StreamSubscription? _invoicesChangedSubscription;
  @override
  void initState() {
    super.initState();
    _loadInvoices();

    // Écouter les changements de factures via stream
    _invoicesChangedSubscription = DataChangeNotifier.instance.invoicesChanged
        .listen((_) {
          _onDataChanged();
        });
  }

  @override
  void dispose() {
    // Arrêter d'écouter les changements
    _invoicesChangedSubscription?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  void _onDataChanged() {
    // Recharger les factures quand les données changent
    if (mounted) {
      debugPrint(
        '🔄 InvoiceListPage: Rechargement suite à changement de données',
      );
      _loadInvoices();
    }
  }

  // Méthode pour forcer le rechargement (appelée depuis l'extérieur)
  void refreshInvoices() {
    if (mounted) {
      debugPrint('🔄 Rechargement forcé des factures');
      _loadInvoices();
    }
  }

  Future<void> _loadInvoices() async {
    // Charger les données sans vider les listes d'abord
    try {
      final invoices = await InvoiceService.loadInvoices();
      if (mounted) {
        setState(() {
          _invoices =
              invoices..sort((a, b) => b.createdAt.compareTo(a.createdAt));
        });
        // Appliquer les filtres existants après le chargement
        _filterInvoices();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  void _filterInvoices() {
    setState(() {
      _filteredInvoices =
          _invoices.where((invoice) {
            final matchesStatus =
                _selectedStatus == null || invoice.status == _selectedStatus;
            final matchesSearch =
                _searchController.text.isEmpty ||
                invoice.clientName.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ) ||
                invoice.clientNumber.contains(_searchController.text) ||
                invoice.id.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                );
            return matchesStatus && matchesSearch;
          }).toList();
    });
  }

  void _clearFilters() {
    setState(() {
      _selectedStatus = null;
      _searchController.clear();
      _filteredInvoices = _invoices;
    });
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.terminee:
        return AppColors.success;
      case InvoiceStatus.enAttente:
        return AppColors.warning;
      case InvoiceStatus.enCours:
        return Colors.blue;
      case InvoiceStatus.enRetard:
        return Colors.grey;
      case InvoiceStatus.annulee:
        return AppColors.error;
    }
  }

  Color _getCardColor(Invoice invoice) {
    // Si c'est une facture proforma, retourner violet
    if (invoice.type == InvoiceType.proforma) {
      return Colors.purple;
    }
    // Sinon, retourner la couleur du statut
    return _getStatusColor(invoice.status);
  }

  Widget _buildStatusChip(InvoiceStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: _getStatusColor(status), width: 2),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: _getStatusColor(status),
          fontSize: 14,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  Future<void> _generateMiniInvoice(Invoice invoice) async {
    setState(() {
      _generatingMiniPDFInvoices.add(invoice.id);
    });

    try {
      // Utiliser directement le total de la facture (pas de calculs supplémentaires)
      final resteAPayer = invoice.total;

      // Générer la mini facture pour prévisualisation
      final pdfDocument = await PDFService.generateMiniInvoice(
        nomClient: invoice.clientName,
        numeroClient: invoice.clientNumber,
        lieuLivraison: invoice.deliveryLocation,
        resteAPayer: resteAPayer,
        referenceFacture: 'F${invoice.id.toString().padLeft(6, '0')}',
        produitArticle:
            invoice.items.isNotEmpty ? invoice.items.first.name : null,
        dateLivraison: DateTime.now(),
        detailsLivraison:
            invoice.deliveryDetails, // Ajouter les détails de livraison
      );

      if (mounted) {
        // Naviguer vers la page de prévisualisation
        final fileName =
            'mini_facture_${invoice.clientNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf';

        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => PDFPreviewPage(
                  pdfDocument: pdfDocument,
                  title: 'Prévisualisation Mini Facture',
                  fileName: fileName,
                ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la génération mini PDF: $e')),
        );
      }
    } finally {
      setState(() {
        _generatingMiniPDFInvoices.remove(invoice.id);
      });
    }
  }

  Widget _buildInvoiceListWithDateSeparators() {
    if (_filteredInvoices.isEmpty) {
      return const SizedBox.shrink();
    }

    // Grouper les factures par jour
    final Map<String, List<Invoice>> invoicesByDate = {};
    for (final invoice in _filteredInvoices) {
      final dateKey = DateFormat('yyyy-MM-dd').format(invoice.createdAt);
      if (!invoicesByDate.containsKey(dateKey)) {
        invoicesByDate[dateKey] = [];
      }
      invoicesByDate[dateKey]!.add(invoice);
    }

    // Trier les clés de date en ordre décroissant (plus récent en premier)
    final sortedDateKeys =
        invoicesByDate.keys.toList()..sort((a, b) => b.compareTo(a));

    return ListView.builder(
      padding: EdgeInsets.only(
        bottom: 70 + 16 * 2 + MediaQuery.viewPaddingOf(context).bottom,
      ),
      itemCount: _calculateTotalItemCount(invoicesByDate, sortedDateKeys),
      itemBuilder: (context, index) {
        return _buildItemAtIndex(invoicesByDate, sortedDateKeys, index);
      },
    );
  }

  int _calculateTotalItemCount(
    Map<String, List<Invoice>> invoicesByDate,
    List<String> sortedDateKeys,
  ) {
    int count = 0;
    for (final dateKey in sortedDateKeys) {
      count += 1; // Pour le séparateur de date
      count += invoicesByDate[dateKey]!.length; // Pour les factures
    }
    return count;
  }

  Widget _buildItemAtIndex(
    Map<String, List<Invoice>> invoicesByDate,
    List<String> sortedDateKeys,
    int index,
  ) {
    int currentIndex = 0;

    for (final dateKey in sortedDateKeys) {
      // Vérifier si c'est le séparateur de date
      if (currentIndex == index) {
        return _buildDateSeparator(dateKey);
      }
      currentIndex++;

      // Vérifier si c'est une facture de ce jour
      final invoicesForDate = invoicesByDate[dateKey]!;
      for (int i = 0; i < invoicesForDate.length; i++) {
        if (currentIndex == index) {
          return _buildInvoiceCard(invoicesForDate[i]);
        }
        currentIndex++;
      }
    }

    return const SizedBox.shrink();
  }

  Widget _buildDateSeparator(String dateKey) {
    final date = DateTime.parse(dateKey);
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final invoiceDate = DateTime(date.year, date.month, date.day);

    String displayText;
    if (invoiceDate == today) {
      displayText = 'Aujourd\'hui';
    } else if (invoiceDate == yesterday) {
      displayText = 'Hier';
    } else {
      displayText = DateFormat('EEEE d MMMM yyyy', 'fr_FR').format(date);
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Expanded(child: Container(height: 1, color: Colors.grey[300])),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              displayText,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(child: Container(height: 1, color: Colors.grey[300])),
        ],
      ),
    );
  }

  Widget _buildInvoiceCard(Invoice invoice) {
    // Récupérer le nom du premier article commandé (stock ou personnalisé)
    String articleName =
        invoice.items.isNotEmpty ? invoice.items.first.name : invoice.products;

    final bool isSelected = _selectedInvoices.contains(invoice);
    final bool isGeneratingMiniPDF = _generatingMiniPDFInvoices.contains(
      invoice.id,
    );

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color:
            isSelected
                ? Colors.blue.withValues(alpha: 0.2)
                : _getCardColor(invoice).withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? Colors.blue : _getCardColor(invoice),
          width: isSelected ? 3 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () async {
          if (_selectionMode) {
            setState(() {
              if (isSelected) {
                _selectedInvoices.remove(invoice);
                if (_selectedInvoices.isEmpty) {
                  _selectionMode = false;
                }
              } else {
                if (_selectedInvoices.length < 6) {
                  // Maximum 6 factures
                  _selectedInvoices.add(invoice);
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Maximum 6 factures peuvent être sélectionnées',
                      ),
                    ),
                  );
                }
              }
            });
          } else {
            final result = await Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => InvoiceDetailPage(invoiceId: invoice.id),
              ),
            );
            if (result == true) {
              _loadInvoices();
            }
          }
        },
        onLongPress: () {
          setState(() {
            if (!_selectionMode) {
              _selectionMode = true;
              _selectedInvoices.add(invoice);
            }
          });
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      articleName,
                      style: const TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.blue,
                        fontSize: 15,
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 1,
                    ),
                  ),
                  _buildStatusChip(invoice.status),
                ],
              ),
              const SizedBox(height: 4),
              Container(
                decoration: BoxDecoration(
                  color: Colors.transparent,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.white, width: 2),
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      invoice.clientName,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.phone, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          invoice.clientNumber,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Text(
                          invoice.deliveryLocation,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Créé le: ${DateFormat('dd/MM/yyyy').format(invoice.createdAt)}',
                style: TextStyle(color: Colors.grey[600], fontSize: 12),
              ),
              if (invoice.type == InvoiceType.proforma &&
                  invoice.validityDate != null) ...[
                const SizedBox(height: 2),
                Text(
                  'Valide jusqu\'au: ${DateFormat('dd/MM/yyyy').format(invoice.validityDate!)}',
                  style: TextStyle(
                    color:
                        invoice.validityDate!.isBefore(DateTime.now())
                            ? Colors.red[600]
                            : Colors.orange[600],
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              const SizedBox(height: 6),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'N° ${invoice.invoiceNumber}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _formatCurrency(_resteAPayer(invoice)),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w900,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Image du produit en miniature
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.white, width: 2),
                      color: Colors.grey[200],
                    ),
                    child:
                        (invoice.productImagePath != null &&
                                invoice.productImagePath!.isNotEmpty &&
                                File(invoice.productImagePath!).existsSync())
                            ? ClipRRect(
                              borderRadius: BorderRadius.circular(6),
                              child: Image.file(
                                File(invoice.productImagePath!),
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    Icons.image_not_supported,
                                    color: Colors.grey[400],
                                    size: 30,
                                  );
                                },
                              ),
                            )
                            : Icon(
                              Icons.image_not_supported,
                              color: Colors.grey[400],
                              size: 30,
                            ),
                  ),
                ],
              ),
              if (invoice.type == InvoiceType.proforma) ...[
                const SizedBox(height: 2),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.purple[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'PROFORMA',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple[800],
                    ),
                  ),
                ),
              ],
              const SizedBox(height: 4),
              Column(
                children: [
                  // Première ligne de boutons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Bouton WhatsApp (en premier)
                      IconButton(
                        onPressed: () => _openWhatsApp(invoice),
                        icon: const FaIcon(FontAwesomeIcons.whatsapp, size: 20),
                        color: const Color(
                          0xFF25D366,
                        ), // Couleur officielle WhatsApp
                        tooltip: 'WhatsApp',
                      ),
                      // Bouton Mini Facture
                      IconButton(
                        onPressed:
                            isGeneratingMiniPDF
                                ? null
                                : () => _generateMiniInvoice(invoice),
                        icon:
                            isGeneratingMiniPDF
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                )
                                : const Icon(Icons.receipt, size: 20),
                        color: Colors.green,
                        tooltip: 'Mini Facture',
                      ),
                      // Bouton Impression Thermique
                      IconButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) =>
                                      PrinterDiscoveryPage(invoice: invoice),
                            ),
                          );
                        },
                        icon: const Icon(Icons.print, size: 20),
                        color: Colors.blue[600],
                        tooltip: 'Impression Thermique',
                      ),
                      // Bouton PDF existant
                      IconButton(
                        onPressed: () async {
                          try {
                            // Générer le PDF pour prévisualisation
                            final pdfDocument =
                                await PDFService.generateInvoicePDF(invoice);

                            if (mounted) {
                              // Naviguer vers la page de prévisualisation
                              final prefix =
                                  invoice.type == InvoiceType.proforma
                                      ? 'Proforma'
                                      : 'Facture';
                              final fileName =
                                  '${prefix}_${invoice.invoiceNumber}_${invoice.clientName.replaceAll(' ', '_')}.pdf';

                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder:
                                      (context) => PDFPreviewPage(
                                        pdfDocument: pdfDocument,
                                        title: 'Prévisualisation $prefix',
                                        fileName: fileName,
                                      ),
                                ),
                              );
                            }
                          } catch (e) {
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('Erreur PDF: $e')),
                              );
                            }
                          }
                        },
                        icon: const Icon(Icons.picture_as_pdf, size: 20),
                        color: Colors.red,
                        tooltip: 'PDF',
                      ),
                    ],
                  ),
                  // Deuxième ligne avec le bouton dupliquer
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        onPressed: () => _duplicateInvoice(invoice),
                        icon: const Icon(Icons.copy, size: 20),
                        color: Colors.blue,
                        tooltip: 'Dupliquer',
                      ),
                    ],
                  ),

                  // Bouton principal pour créer une commande
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    child: ElevatedButton.icon(
                      onPressed: () => _createOrderFromInvoice(invoice),
                      icon: const Icon(Icons.shopping_cart_outlined, size: 18),
                      label: const Text(
                        'Créer une commande',
                        style: TextStyle(fontSize: 13),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                FilterChip(
                  label: const Text('Toutes'),
                  selected: _selectedStatus == null,
                  onSelected: (selected) {
                    setState(() {
                      _selectedStatus = null;
                    });
                    _filterInvoices();
                  },
                ),
                const SizedBox(width: 8),
                ...InvoiceStatus.values.map(
                  (status) => Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(status.displayName),
                      selected: _selectedStatus == status,
                      onSelected: (selected) {
                        setState(() {
                          _selectedStatus = selected ? status : null;
                        });
                        _filterInvoices();
                      },
                      selectedColor: _getStatusColor(
                        status,
                      ).withValues(alpha: 0.2),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (_selectedStatus != null || _searchController.text.isNotEmpty)
            IconButton(
              onPressed: _clearFilters,
              icon: const Icon(Icons.clear),
              tooltip: 'Effacer les filtres',
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Plus besoin de calculer l'espace réservé car la barre est maintenant en overlay

    // Obtenir le thème dynamique pour les factures
    final themeService = ThemeService.instance;
    final pageTheme = themeService.getThemeForPage('invoices');

    // Appliquer le thème système
    WidgetsBinding.instance.addPostFrameCallback((_) {
      themeService.applySystemUITheme(pageTheme);
    });

    return Scaffold(
      appBar: AppBar(
        title: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icône des factures avec animation
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Icon(
                Icons.receipt_long,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
            // Titre avec animation
            AnimatedDefaultTextStyle(
              duration: const Duration(milliseconds: 300),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              child: Text(
                _selectionMode
                    ? '${_selectedInvoices.length} facture(s) sélectionnée(s)'
                    : 'HCP-DESIGN - Factures',
              ),
            ),
          ],
        ),
        backgroundColor: pageTheme.primary,
        foregroundColor: Colors.white,
        elevation: 4,
        centerTitle: true,
        shadowColor: pageTheme.secondary.withValues(alpha: 0.5),

        // Gradient en arrière-plan
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [pageTheme.primary, pageTheme.secondary],
            ),
          ),
        ),
        actions: [
          if (!_selectionMode)
            IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const CommandesJourPage(),
                  ),
                );
              },
              icon: const Icon(Icons.local_shipping),
              tooltip: 'Commandes / Réglages',
            ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Rechercher par nom, téléphone ou numéro...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(horizontal: 20),
              ),
              onChanged: (value) => _filterInvoices(),
            ),
          ),
        ),
      ),
      body: Column(
        children: [
          _buildFilterChips(),
          Expanded(
            child:
                _filteredInvoices.isEmpty
                    ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.receipt_long,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _invoices.isEmpty
                                ? 'Aucune facture créée'
                                : 'Aucune facture trouvée',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _invoices.isEmpty
                                ? 'Créez votre première facture'
                                : 'Essayez de modifier vos filtres',
                            style: TextStyle(color: Colors.grey[500]),
                          ),
                        ],
                      ),
                    )
                    : RefreshIndicator(
                      onRefresh: _loadInvoices,
                      child: _buildInvoiceListWithDateSeparators(),
                    ),
          ),
        ],
      ),
      floatingActionButton:
          _selectionMode
              ? Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    child: FloatingActionButton(
                      heroTag: 'cancel',
                      onPressed: () {
                        setState(() {
                          _selectionMode = false;
                          _selectedInvoices.clear();
                        });
                      },
                      backgroundColor: Colors.red[600],
                      foregroundColor: Colors.white,
                      mini: true,
                      child: const Icon(Icons.close),
                    ),
                  ),
                  const SizedBox(height: 10),
                  FloatingActionButton(
                    heroTag: 'print',
                    onPressed: () async {
                      if (_selectedInvoices.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Aucune facture sélectionnée'),
                          ),
                        );
                        return;
                      }
                      // Capturer le contexte avant l'opération async
                      final navigator = Navigator.of(context);
                      final scaffoldMessenger = ScaffoldMessenger.of(context);

                      try {
                        // Vérifier que les factures sélectionnées ont des données valides
                        if (_selectedInvoices.any(
                          (invoice) =>
                              invoice.clientName.trim().isEmpty ||
                              invoice.clientNumber.trim().isEmpty ||
                              invoice.deliveryLocation.trim().isEmpty,
                        )) {
                          scaffoldMessenger.showSnackBar(
                            const SnackBar(
                              content: Text(
                                'Attention: Certaines factures ont des informations manquantes',
                              ),
                              backgroundColor: Colors.orange,
                            ),
                          );
                        }

                        // Générer le PDF groupé pour prévisualisation
                        final pdfDocument =
                            await PDFService.generateGroupedReceiptDocument(
                              _selectedInvoices,
                            );

                        if (mounted) {
                          // Naviguer vers la page de prévisualisation
                          final fileName =
                              'Recu_Groupe_${DateFormat('yyyyMMdd_HHmmss').format(DateTime.now())}.pdf';

                          navigator.push(
                            MaterialPageRoute(
                              builder:
                                  (context) => PDFPreviewPage(
                                    pdfDocument: pdfDocument,
                                    title: 'Prévisualisation Reçu Groupé',
                                    fileName: fileName,
                                  ),
                            ),
                          );

                          setState(() {
                            _selectionMode = false;
                            _selectedInvoices.clear();
                          });
                        }
                      } catch (e) {
                        if (mounted) {
                          scaffoldMessenger.showSnackBar(
                            SnackBar(content: Text('Erreur: $e')),
                          );
                        }
                      }
                    },
                    backgroundColor: pageTheme.accent,
                    foregroundColor: Colors.white,
                    child: const Icon(Icons.receipt_long),
                  ),
                ],
              )
              : Padding(
                padding: EdgeInsets.only(
                  bottom:
                      70 +
                      16 * 2 +
                      MediaQuery.viewPaddingOf(context).bottom +
                      16,
                ),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  child: FloatingActionButton(
                    onPressed: _showInvoiceTypeMenu,
                    backgroundColor: pageTheme.accent,
                    foregroundColor: Colors.white,
                    elevation: 6,
                    highlightElevation: 12,
                    child: const Icon(Icons.add),
                  ),
                ),
              ),
    );
  }

  void _showInvoiceTypeMenu() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Créer une nouvelle facture',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
              const SizedBox(height: 20),

              // Option 1: Facture normale
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.receipt_long, color: Colors.blue[700]),
                ),
                title: const Text(
                  'Facture Normale',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: const Text('Facture standard pour vente confirmée'),
                onTap: () async {
                  Navigator.pop(context);
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateInvoicePage(),
                    ),
                  );
                  if (result == true) {
                    _loadInvoices();
                  }
                },
              ),

              const Divider(),

              // Option 2: Facture proforma
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.purple[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.description, color: Colors.purple[700]),
                ),
                title: const Text(
                  'Facture Proforma',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                subtitle: const Text('Devis détaillé avec date de validité'),
                onTap: () async {
                  Navigator.pop(context);
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const CreateProformaPage(),
                    ),
                  );
                  if (result == true) {
                    _loadInvoices();
                  }
                },
              ),

              const SizedBox(height: 10),
            ],
          ),
        );
      },
    );
  }

  String _formatCurrency(double value) {
    return '${NumberFormat('#,##0', 'fr_FR').format(value)} FCFA';
  }

  double _resteAPayer(Invoice invoice) {
    double reste = invoice.subtotal + invoice.deliveryPrice;
    if (invoice.advance > 0) reste -= invoice.advance;
    if (invoice.discountAmount > 0) reste -= invoice.discountAmount;
    return reste;
  }

  /// Formate le numéro de téléphone pour WhatsApp
  String _formatPhoneForWhatsApp(String? phoneNumber) {
    if (phoneNumber == null || phoneNumber.isEmpty) {
      return '';
    }

    // Nettoyer le numéro (supprimer espaces, tirets, points, etc. mais garder + et chiffres)
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // Si le numéro commence déjà par +225, le retourner tel quel
    if (cleanNumber.startsWith('+225')) {
      return cleanNumber;
    }

    // Si le numéro commence par 225 sans +, ajouter le +
    if (cleanNumber.startsWith('225')) {
      return '+$cleanNumber';
    }

    // Si le numéro commence par 0, garder le 0 et ajouter +225
    if (cleanNumber.startsWith('0')) {
      return '+225$cleanNumber';
    }

    // Pour les autres cas, ajouter +225 directement
    return '+225$cleanNumber';
  }

  /// Ouvre WhatsApp avec le numéro du client
  Future<void> _openWhatsApp(Invoice invoice) async {
    try {
      debugPrint('📱 === DÉBUT OUVERTURE WHATSAPP ===');
      debugPrint('📱 Numéro original: "${invoice.clientNumber}"');

      final phoneNumber = _formatPhoneForWhatsApp(invoice.clientNumber);
      debugPrint('📱 Numéro formaté: "$phoneNumber"');

      if (phoneNumber.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              '❌ Aucun numéro de téléphone disponible pour ce client',
            ),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Supprimer le + du numéro pour l'URL WhatsApp
      final whatsappNumber =
          phoneNumber.startsWith('+') ? phoneNumber.substring(1) : phoneNumber;
      debugPrint('📱 Numéro WhatsApp final: "$whatsappNumber"');

      // Message personnalisé pour WhatsApp
      final message = Uri.encodeComponent(
        'Bonjour ${invoice.clientName},\n\n'
        'Voici votre facture HCP DESIGN :\n'
        '📄 Facture N° F${invoice.id.toString().padLeft(6, '0')}\n'
        '💰 Montant : ${_formatCurrency(_resteAPayer(invoice))}\n'
        '📅 Date : ${DateFormat('dd/MM/yyyy').format(invoice.createdAt)}\n\n'
        'Merci pour votre confiance !\n'
        '🌐 www.hcp-designci.com',
      );

      // Essayer plusieurs formats d'URL WhatsApp
      final urls = [
        'https://wa.me/$whatsappNumber?text=$message',
        'https://api.whatsapp.com/send?phone=$whatsappNumber&text=$message',
        'whatsapp://send?phone=$whatsappNumber&text=$message',
      ];

      bool success = false;
      String? lastError;

      for (String whatsappUrl in urls) {
        try {
          final uri = Uri.parse(whatsappUrl);
          debugPrint('🔗 Tentative URL: $whatsappUrl');

          if (await canLaunchUrl(uri)) {
            debugPrint('✅ canLaunchUrl = true pour: $whatsappUrl');
            await launchUrl(uri, mode: LaunchMode.externalApplication);
            success = true;
            break;
          } else {
            debugPrint('❌ canLaunchUrl = false pour: $whatsappUrl');
          }
        } catch (e) {
          lastError = e.toString();
          debugPrint('❌ Erreur avec URL $whatsappUrl: $e');
          continue;
        }
      }

      if (success) {
        // Afficher un message de succès
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('📱 WhatsApp ouvert pour ${invoice.clientName}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        debugPrint(
          '❌ Aucune URL WhatsApp n\'a fonctionné. Dernière erreur: $lastError',
        );
        // WhatsApp n'est pas disponible, proposer des alternatives
        if (mounted) {
          _showWhatsAppAlternatives(invoice, whatsappNumber);
        }
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Erreur ouverture WhatsApp: $e');
      debugPrint('❌ Stack trace: $stackTrace');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('❌ Erreur WhatsApp'),
                Text(
                  'Client: ${invoice.clientName}',
                  style: TextStyle(fontSize: 12),
                ),
                Text('Détail: ${e.toString()}', style: TextStyle(fontSize: 10)),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  /// Affiche les alternatives quand WhatsApp n'est pas disponible
  void _showWhatsAppAlternatives(Invoice invoice, String whatsappNumber) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.warning, color: Colors.orange),
                SizedBox(width: 8),
                Text('WhatsApp non disponible'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'WhatsApp n\'est pas installé ou ne peut pas être ouvert.',
                  style: TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Alternatives disponibles :',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text('📱 Numéro : $whatsappNumber'),
                const SizedBox(height: 4),
                Text('👤 Client : ${invoice.clientName}'),
              ],
            ),
            actions: [
              // Bouton pour appeler directement
              TextButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _makePhoneCall(whatsappNumber);
                },
                icon: const Icon(Icons.phone, color: Colors.blue),
                label: const Text('Appeler'),
              ),
              // Bouton pour copier le numéro
              TextButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _copyToClipboard(whatsappNumber, invoice.clientName);
                },
                icon: const Icon(Icons.copy, color: Colors.grey),
                label: const Text('Copier'),
              ),
              // Bouton pour ouvrir WhatsApp Web
              TextButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  _openWhatsAppWeb(whatsappNumber, invoice);
                },
                icon: const FaIcon(
                  FontAwesomeIcons.whatsapp,
                  color: Color(0xFF25D366),
                ),
                label: const Text('WhatsApp Web'),
              ),
            ],
          ),
    );
  }

  /// Effectue un appel téléphonique
  Future<void> _makePhoneCall(String phoneNumber) async {
    try {
      final uri = Uri.parse('tel:$phoneNumber');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('📞 Ouverture de l\'application d\'appel...'),
              backgroundColor: Colors.blue,
            ),
          );
        }
      } else {
        throw Exception('Impossible d\'effectuer l\'appel');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur appel : $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Copie le numéro dans le presse-papiers
  Future<void> _copyToClipboard(String phoneNumber, String clientName) async {
    try {
      // Note: Clipboard nécessite l'import de 'package:flutter/services.dart'
      // Pour l'instant, on affiche juste le numéro
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('📋 Numéro de $clientName : $phoneNumber'),
            backgroundColor: Colors.grey[700],
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'OK',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Erreur copie : $e');
    }
  }

  /// Ouvre WhatsApp Web dans le navigateur
  Future<void> _openWhatsAppWeb(String whatsappNumber, Invoice invoice) async {
    try {
      final message = Uri.encodeComponent(
        'Bonjour ${invoice.clientName},\n\n'
        'Voici votre facture HCP DESIGN :\n'
        '📄 Facture N° F${invoice.id.toString().padLeft(6, '0')}\n'
        '💰 Montant : ${_formatCurrency(_resteAPayer(invoice))}\n'
        '📅 Date : ${DateFormat('dd/MM/yyyy').format(invoice.createdAt)}\n\n'
        'Merci pour votre confiance !\n'
        '🌐 www.hcp-designci.com',
      );

      final webUrl =
          'https://web.whatsapp.com/send?phone=$whatsappNumber&text=$message';
      final uri = Uri.parse(webUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('🌐 WhatsApp Web ouvert dans le navigateur'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('Impossible d\'ouvrir WhatsApp Web');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur WhatsApp Web : $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Duplique une facture existante
  Future<void> _duplicateInvoice(Invoice invoice) async {
    try {
      // Naviguer vers la page de création avec les données pré-remplies
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) =>
                  invoice.type == InvoiceType.proforma
                      ? CreateProformaPage(duplicateFrom: invoice)
                      : CreateInvoicePage(duplicateFrom: invoice),
        ),
      );

      if (result == true) {
        _loadInvoices();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Facture dupliquée avec succès'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Erreur lors de la duplication : $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Créer une commande à partir d'une facture
  Future<void> _createOrderFromInvoice(Invoice invoice) async {
    try {
      // Afficher un feedback immédiat
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Text('Préparation de la commande pour ${invoice.clientName}...'),
            ],
          ),
          backgroundColor: Colors.purple[600],
          duration: const Duration(seconds: 2),
        ),
      );

      // Naviguer vers la page de création de commande avec la facture pré-sélectionnée
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CreateOrderPage(linkedInvoice: invoice),
        ),
      );

      if (result == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white, size: 20),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Commande créée avec succès à partir de la facture ${invoice.invoiceNumber}',
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.green[600],
              duration: const Duration(seconds: 3),
              action: SnackBarAction(
                label: 'Voir',
                textColor: Colors.white,
                onPressed: () {
                  // Naviguer vers la page des commandes
                  Navigator.pushNamed(context, '/orders');
                },
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text('Erreur lors de la création de commande : $e'),
                ),
              ],
            ),
            backgroundColor: Colors.red[600],
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }
}
