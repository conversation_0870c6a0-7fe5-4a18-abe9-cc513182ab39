import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:open_file/open_file.dart';
import 'thermal_printer/printer_discovery_page.dart';

class PDFPreviewPage extends StatefulWidget {
  final pw.Document pdfDocument;
  final String title;
  final String fileName;

  const PDFPreviewPage({
    super.key,
    required this.pdfDocument,
    required this.title,
    required this.fileName,
  });

  @override
  State<PDFPreviewPage> createState() => _PDFPreviewPageState();
}

class _PDFPreviewPageState extends State<PDFPreviewPage> {
  String? _pdfPath;
  bool _isLoading = true;
  bool _isSharing = false;
  int _currentPage = 0;
  int _totalPages = 0;

  @override
  void initState() {
    super.initState();
    _generatePDFFile();
  }

  Future<void> _generatePDFFile() async {
    try {
      final bytes = await widget.pdfDocument.save();
      final dir = await getTemporaryDirectory();
      final file = File('${dir.path}/${widget.fileName}');
      await file.writeAsBytes(bytes);

      if (mounted) {
        setState(() {
          _pdfPath = file.path;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la génération: $e')),
        );
      }
    }
  }

  Future<void> _sharePDF() async {
    if (_pdfPath == null) return;

    setState(() => _isSharing = true);

    try {
      await Share.shareXFiles([XFile(_pdfPath!)], text: 'Voici le PDF généré');
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Erreur lors du partage: $e')));
      }
    } finally {
      setState(() => _isSharing = false);
    }
  }

  Future<void> _openPDF() async {
    if (_pdfPath == null) return;

    try {
      await OpenFile.open(_pdfPath!);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'ouverture: $e')),
        );
      }
    }
  }

  Future<void> _printThermal() async {
    if (_pdfPath == null) return;

    try {
      // Naviguer vers la page de découverte d'imprimante avec le PDF
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PrinterDiscoveryPage(
            pdfPath: _pdfPath!,
            documentTitle: widget.title,
          ),
        ),
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'impression: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: Colors.blue[900],
        foregroundColor: Colors.white,
        actions: [
          if (!_isLoading && _pdfPath != null) ...[
            IconButton(
              onPressed: _openPDF,
              icon: const Icon(Icons.open_in_new),
              tooltip: 'Ouvrir dans une autre app',
            ),
            IconButton(
              onPressed: _isSharing ? null : _sharePDF,
              icon:
                  _isSharing
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Icon(Icons.share),
              tooltip: 'Partager',
            ),
            IconButton(
              onPressed: _printThermal,
              icon: const Icon(Icons.print),
              tooltip: 'Impression thermique',
            ),
          ],
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Génération du PDF en cours...'),
                  ],
                ),
              )
              : _pdfPath == null
              ? const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 64, color: Colors.red),
                    SizedBox(height: 16),
                    Text('Erreur lors de la génération du PDF'),
                  ],
                ),
              )
              : Column(
                children: [
                  // Barre d'informations
                  Container(
                    padding: const EdgeInsets.all(16),
                    color: Colors.grey[100],
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Page ${_currentPage + 1} sur $_totalPages',
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Row(
                          children: [
                            IconButton(
                              onPressed: _openPDF,
                              icon: const Icon(Icons.open_in_new),
                              tooltip: 'Ouvrir',
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.blue[600],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.all(12),
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: _isSharing ? null : _sharePDF,
                              icon:
                                  _isSharing
                                      ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                Colors.white,
                                              ),
                                        ),
                                      )
                                      : const Icon(Icons.share),
                              tooltip: 'Partager',
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.green[600],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.all(12),
                              ),
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: _printThermal,
                              icon: const Icon(Icons.print),
                              tooltip: 'Imprimer',
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.orange[600],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.all(12),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  // Visualiseur PDF
                  Expanded(
                    child: PDFView(
                      filePath: _pdfPath!,
                      enableSwipe: true,
                      swipeHorizontal: false,
                      autoSpacing: false,
                      pageFling: false,
                      onRender: (pages) {
                        setState(() {
                          _totalPages = pages ?? 0;
                        });
                      },
                      onPageChanged: (page, total) {
                        setState(() {
                          _currentPage = page ?? 0;
                        });
                      },
                      onError: (error) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text('Erreur PDF: $error')),
                        );
                      },
                    ),
                  ),
                ],
              ),
    );
  }

  @override
  void dispose() {
    // Nettoyer le fichier temporaire
    if (_pdfPath != null) {
      try {
        File(_pdfPath!).deleteSync();
      } catch (e) {
        // Ignorer les erreurs de suppression
      }
    }
    super.dispose();
  }
}
