# Guide de Conversion des Sauvegardes

## Vue d'ensemble

Ce guide explique comment convertir les fichiers de sauvegarde segmentée vers le format de sauvegarde standard de l'application HCP CRM.

## Fichiers créés

### 1. Convertisseur automatique
- **Fichier**: `lib/utils/backup_format_converter.dart`
- **Description**: Classe utilitaire pour convertir les sauvegardes segmentées vers le format standard
- **Utilisation**: Intégré dans l'application Flutter

### 2. Script de conversion standalone
- **Fichier**: `convert_backup.dart`
- **Description**: Script Dart autonome pour convertir un fichier spécifique
- **Utilisation**: Exécution en ligne de commande

### 3. Fichiers de sauvegarde convertis
- **Fichier original**: `hcp_backup_invoices_Juillet_2025_1755625135430.json` (format segmenté)
- **Fichier converti**: `hcp_backup_standard_format_complete.json` (format standard)
- **Contenu**: 49 factures du mois de juillet 2025

## Différences entre les formats

### Format Segmenté (Original)
```json
{
  "version": "2.2.0",
  "timestamp": "2025-08-19T17:38:55.417019",
  "segmented": true,
  "config": {
    "dataTypes": ["invoices"],
    "period": {
      "startDate": "2025-07-01T00:00:00.000",
      "endDate": "2025-07-31T23:59:59.000",
      "displayName": "Juillet 2025"
    },
    "includeImages": false,
    "compressData": true
  },
  "data": {
    "invoices": [...]
  }
}
```

### Format Standard (Converti)
```json
{
  "version": "2.2.0",
  "timestamp": "2025-08-19T17:38:55.417019",
  "data": {
    "products": [],
    "categories": [],
    "invoices": [...],
    "tasks": [],
    "colis": []
  },
  "images": {
    "invoices": {},
    "colis": {}
  }
}
```

## Comment utiliser

### Méthode 1: Script autonome
```bash
# Placer le fichier à convertir dans le répertoire du projet
# Exécuter le script
dart convert_backup.dart
```

### Méthode 2: Intégration dans l'application
```dart
import 'package:general_hcp_crm/utils/backup_format_converter.dart';

// Convertir un fichier
final convertedPath = await BackupFormatConverter.convertFileToStandard(
  'chemin/vers/sauvegarde_segmentee.json',
  'chemin/vers/sauvegarde_standard.json',
);

// Convertir des données en mémoire
final standardBackup = BackupFormatConverter.convertSegmentedToStandard(
  segmentedBackupData,
);
```

## Fonctionnalités du convertisseur

### Validation des formats
- `isSegmentedBackup()`: Vérifie si une sauvegarde est au format segmenté
- `isStandardBackup()`: Vérifie si une sauvegarde est au format standard
- `getBackupInfo()`: Obtient des informations détaillées sur une sauvegarde

### Conversion des données
- Préserve toutes les données des factures
- Maintient la compatibilité avec les versions
- Ajoute les sections manquantes (products, categories, tasks, colis)
- Gère les images (structure vide si non présentes)

## Résultats de la conversion

### Statistiques du fichier converti
- **49 factures** converties avec succès
- **Période**: Juillet 2025
- **Montant total**: 310,000 FCFA
- **Format**: Standard compatible avec l'application

### Structure des données préservées
- Identifiants uniques des factures
- Informations clients (noms, numéros)
- Détails des produits et services
- Montants, avances, et totaux
- Statuts de paiement
- Lieux de livraison
- Dates de création

## Utilisation dans l'application

Le fichier converti `hcp_backup_standard_format_complete.json` peut maintenant être:
1. Importé via l'interface de restauration de l'application
2. Utilisé avec `BackupService.restoreFromFile()`
3. Traité par les fonctions de sauvegarde standard

## Notes importantes

- La conversion préserve l'intégrité des données
- Les images ne sont pas incluses dans ce fichier (config: includeImages = false)
- Le format standard est compatible avec toutes les fonctionnalités de l'application
- Les sections vides (products, categories, etc.) permettent une restauration sans erreur

## Dépannage

### Erreurs communes
1. **Fichier non trouvé**: Vérifier le chemin du fichier d'entrée
2. **JSON invalide**: Vérifier la syntaxe du fichier original
3. **Permissions**: S'assurer d'avoir les droits d'écriture dans le répertoire

### Validation
Pour vérifier la conversion, utiliser:
```dart
final info = BackupFormatConverter.getBackupInfo(backupData);
print('Type: ${info['type']}');
print('Factures: ${info['invoicesCount']}');
```