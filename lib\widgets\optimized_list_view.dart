import 'package:flutter/material.dart';

/// Widget de liste optimisé avec pagination et lazy loading
class OptimizedListView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int pageSize;
  final bool enablePagination;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  
  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.pageSize = 20,
    this.enablePagination = true,
    this.loadingWidget,
    this.emptyWidget,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.onLoadMore,
    this.hasMore = false,
  });

  @override
  State<OptimizedListView<T>> createState() => _OptimizedListViewState<T>();
}

class _OptimizedListViewState<T> extends State<OptimizedListView<T>> {
  late ScrollController _scrollController;
  int _currentPage = 1;
  bool _isLoadingMore = false;
  
  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    
    if (widget.enablePagination && widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }
  
  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }
  
  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }
  
  Future<void> _loadMore() async {
    if (_isLoadingMore || !widget.hasMore) return;
    
    setState(() => _isLoadingMore = true);
    
    try {
      widget.onLoadMore?.call();
      _currentPage++;
    } finally {
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return widget.emptyWidget ?? 
        const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inbox, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'Aucun élément trouvé',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        );
    }
    
    final itemsToShow = widget.enablePagination 
        ? widget.items.take(_currentPage * widget.pageSize).toList()
        : widget.items;
    
    return ListView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      itemCount: itemsToShow.length + (widget.hasMore && _isLoadingMore ? 1 : 0),
      cacheExtent: 500, // Cache plus d'éléments pour de meilleures performances
      itemBuilder: (context, index) {
        // Afficher l'indicateur de chargement à la fin
        if (index >= itemsToShow.length) {
          return widget.loadingWidget ?? 
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            );
        }
        
        final item = itemsToShow[index];
        return widget.itemBuilder(context, item, index);
      },
    );
  }
}

/// Widget de grille optimisé avec pagination
class OptimizedGridView<T> extends StatefulWidget {
  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final int pageSize;
  final bool enablePagination;
  final Widget? loadingWidget;
  final Widget? emptyWidget;
  final ScrollController? controller;
  final EdgeInsets? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final VoidCallback? onLoadMore;
  final bool hasMore;
  
  const OptimizedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.crossAxisCount = 2,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 8.0,
    this.mainAxisSpacing = 8.0,
    this.pageSize = 20,
    this.enablePagination = true,
    this.loadingWidget,
    this.emptyWidget,
    this.controller,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.onLoadMore,
    this.hasMore = false,
  });

  @override
  State<OptimizedGridView<T>> createState() => _OptimizedGridViewState<T>();
}

class _OptimizedGridViewState<T> extends State<OptimizedGridView<T>> {
  late ScrollController _scrollController;
  int _currentPage = 1;
  bool _isLoadingMore = false;
  
  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    
    if (widget.enablePagination && widget.onLoadMore != null) {
      _scrollController.addListener(_onScroll);
    }
  }
  
  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }
  
  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }
  
  Future<void> _loadMore() async {
    if (_isLoadingMore || !widget.hasMore) return;
    
    setState(() => _isLoadingMore = true);
    
    try {
      widget.onLoadMore?.call();
      _currentPage++;
    } finally {
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (widget.items.isEmpty) {
      return widget.emptyWidget ?? 
        const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.grid_view, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'Aucun élément trouvé',
                style: TextStyle(fontSize: 16, color: Colors.grey),
              ),
            ],
          ),
        );
    }
    
    final itemsToShow = widget.enablePagination 
        ? widget.items.take(_currentPage * widget.pageSize).toList()
        : widget.items;
    
    return GridView.builder(
      controller: _scrollController,
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.crossAxisCount,
        childAspectRatio: widget.childAspectRatio,
        crossAxisSpacing: widget.crossAxisSpacing,
        mainAxisSpacing: widget.mainAxisSpacing,
      ),
      itemCount: itemsToShow.length + (widget.hasMore && _isLoadingMore ? 1 : 0),
      cacheExtent: 500,
      itemBuilder: (context, index) {
        // Afficher l'indicateur de chargement à la fin
        if (index >= itemsToShow.length) {
          return widget.loadingWidget ?? 
            const Center(child: CircularProgressIndicator());
        }
        
        final item = itemsToShow[index];
        return widget.itemBuilder(context, item, index);
      },
    );
  }
}

/// Mixin pour optimiser les listes avec recherche
mixin OptimizedSearchMixin<T> {
  List<T> _originalItems = [];
  List<T> _filteredItems = [];
  String _searchTerm = '';
  
  List<T> get filteredItems => _filteredItems;
  String get searchTerm => _searchTerm;
  
  void initializeItems(List<T> items) {
    _originalItems = items;
    _filteredItems = items;
  }
  
  void updateSearchTerm(String term, bool Function(T item, String term) matcher) {
    _searchTerm = term;
    if (term.isEmpty) {
      _filteredItems = _originalItems;
    } else {
      _filteredItems = _originalItems.where((item) => matcher(item, term)).toList();
    }
  }
  
  void updateItems(List<T> items) {
    _originalItems = items;
    if (_searchTerm.isEmpty) {
      _filteredItems = items;
    } else {
      // Réappliquer le filtre de recherche
      _filteredItems = items.where((item) => 
        _searchTerm.isEmpty || _matchesSearch(item, _searchTerm)).toList();
    }
  }
  
  bool _matchesSearch(T item, String term) {
    // À implémenter dans la classe qui utilise le mixin
    return true;
  }
}
