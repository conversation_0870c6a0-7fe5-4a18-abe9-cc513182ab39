import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/employee_performance_service.dart';

class EmployeePerformancePage extends StatefulWidget {
  const EmployeePerformancePage({super.key});

  @override
  State<EmployeePerformancePage> createState() =>
      _EmployeePerformancePageState();
}

class _EmployeePerformancePageState extends State<EmployeePerformancePage> {
  EmployeePerformanceStats? _performanceStats;
  bool _isLoading = true;
  final NumberFormat _currencyFormat = NumberFormat('#,##0', 'fr_FR');
  final EmployeePerformanceService _performanceService =
      EmployeePerformanceService.instance;

  @override
  void initState() {
    super.initState();
    _loadPerformanceStats();
  }

  Future<void> _loadPerformanceStats() async {
    setState(() => _isLoading = true);
    try {
      final stats = await _performanceService.getCurrentMonthPerformance();
      setState(() {
        _performanceStats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des performances: $e'),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Performance Employé'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Color(0xFF2196F3), // Bleu
                Color(0xFF3F51B5), // Indigo
                Color(0xFF9C27B0), // Violet
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadPerformanceStats,
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _performanceStats == null
              ? const Center(
                child: Text('Aucune donnée de performance disponible'),
              )
              : RefreshIndicator(
                onRefresh: _loadPerformanceStats,
                child: ListView(
                  padding: const EdgeInsets.all(16),
                  children: [
                    _buildSalaryCard(),
                    const SizedBox(height: 16),
                    _buildObjectiveCard(),
                    const SizedBox(height: 16),
                    _buildStatsCard(),
                    const SizedBox(height: 16),
                    _buildClientDistributionCard(),
                    const SizedBox(height: 16),
                    _buildTopClientsCard(),
                  ],
                ),
              ),
    );
  }

  Widget _buildSalaryCard() {
    final stats = _performanceStats!;
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.green[400]!, Colors.green[600]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: Colors.white,
                  size: 28,
                ),
                SizedBox(width: 12),
                Text(
                  'Salaire Mensuel',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              '${_currencyFormat.format(stats.totalSalary)} FCFA',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 32,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Salaire fixe',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '${_currencyFormat.format(stats.fixedSalary)} FCFA',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Bonus gagné',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.8),
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '${_currencyFormat.format(stats.bonus)} FCFA',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Bonus possible: ${_currencyFormat.format(stats.maxBonus)} FCFA (${stats.bonusPercentage.toStringAsFixed(1)}% atteint)',
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.9),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildObjectiveCard() {
    final stats = _performanceStats!;
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.flag, color: Colors.orange, size: 28),
                SizedBox(width: 12),
                Text(
                  'Objectif Mensuel',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${stats.validClients} / ${stats.clientObjective}',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[600],
                  ),
                ),
                Text(
                  '${stats.objectiveProgress.toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color:
                        stats.objectiveProgress >= 100
                            ? Colors.green
                            : Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: stats.objectiveProgress / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                stats.objectiveProgress >= 100 ? Colors.green : Colors.orange,
              ),
              minHeight: 8,
            ),
            const SizedBox(height: 12),
            Text(
              'Clients avec commande ≥ 3 000 FCFA',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            if (stats.objectiveProgress >= 100)
              Container(
                margin: const EdgeInsets.only(top: 12),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.celebration, color: Colors.green[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '🎉 Félicitations ! Objectif atteint !',
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard() {
    final stats = _performanceStats!;
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue, size: 28),
                SizedBox(width: 12),
                Text(
                  'Statistiques du Mois',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 20),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              childAspectRatio: 1.8,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                _buildStatItem(
                  'Total Clients',
                  '${stats.totalClients}',
                  Icons.group,
                  Colors.purple,
                ),
                _buildStatItem(
                  'Factures Payées',
                  '${stats.totalInvoices}',
                  Icons.receipt,
                  Colors.orange,
                ),
                _buildStatItem(
                  'CA Mensuel',
                  _currencyFormat.format(stats.totalRevenue),
                  Icons.attach_money,
                  Colors.green,
                ),
                _buildStatItem(
                  'Panier Moyen',
                  _currencyFormat.format(stats.averageOrderValue),
                  Icons.shopping_cart,
                  Colors.blue,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildClientDistributionCard() {
    final stats = _performanceStats!;
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.pie_chart, color: Colors.indigo, size: 28),
                SizedBox(width: 12),
                Text(
                  'Répartition des Clients',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildDistributionItem(
              '< 1 000 FCFA',
              stats.clientsByOrderRange['under1000'] ?? 0,
              Colors.red,
            ),
            _buildDistributionItem(
              '1 000 - 3 000 FCFA',
              stats.clientsByOrderRange['between1000And3000'] ?? 0,
              Colors.orange,
            ),
            _buildDistributionItem(
              '3 000 - 5 000 FCFA',
              stats.clientsByOrderRange['between3000And5000'] ?? 0,
              Colors.green,
            ),
            _buildDistributionItem(
              '5 000 - 10 000 FCFA',
              stats.clientsByOrderRange['between5000And10000'] ?? 0,
              Colors.blue,
            ),
            _buildDistributionItem(
              '> 10 000 FCFA',
              stats.clientsByOrderRange['above10000'] ?? 0,
              Colors.purple,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionItem(String range, int count, Color color) {
    final total = _performanceStats!.totalClients;
    final percentage = total > 0 ? (count / total * 100) : 0.0;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(child: Text(range, style: const TextStyle(fontSize: 14))),
          Text(
            '$count (${percentage.toStringAsFixed(1)}%)',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopClientsCard() {
    final stats = _performanceStats!;
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.star, color: Colors.amber, size: 28),
                SizedBox(width: 12),
                Text(
                  'Top 5 Clients',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (stats.topClients.isEmpty)
              const Center(
                child: Text(
                  'Aucun client ce mois-ci',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              )
            else
              ...stats.topClients.asMap().entries.map((entry) {
                final index = entry.key;
                final client = entry.value;
                final colors = [
                  Colors.amber,
                  Colors.grey,
                  Colors.brown,
                  Colors.blue,
                  Colors.purple,
                ];

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: colors[index % colors.length],
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              client.clientName,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              client.clientNumber,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '${_currencyFormat.format(client.totalAmount)} FCFA',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${client.orderCount} commande${client.orderCount > 1 ? 's' : ''}',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }),
          ],
        ),
      ),
    );
  }
}
