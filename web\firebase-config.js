// Configuration Firebase pour le web
// Configuration mise à jour avec les valeurs de firebase_options.dart
const firebaseConfig = {
  apiKey: "AIzaSyCNUNgGq-veTvbCeiLVU538IhhIK6oUmR8",
  authDomain: "general-hcp-crm.firebaseapp.com",
  databaseURL: "https://general-hcp-crm-default-rtdb.firebaseio.com",
  projectId: "general-hcp-crm",
  storageBucket: "general-hcp-crm.firebasestorage.app",
  messagingSenderId: "540211492967",
  appId: "1:540211492967:web:ced243fbffeaad2f877cc2",
  measurementId: "G-35C7PNV6KX"
};

// Configuration pour le développement local (optionnel)
if (window.location.hostname === 'localhost') {
  console.log('Mode développement détecté');
  // Vous pouvez utiliser l'émulateur Firebase ici si nécessaire
}

// Initialiser Firebase
if (typeof firebase !== 'undefined') {
  firebase.initializeApp(firebaseConfig);
  console.log('Firebase initialisé pour le web');
} else {
  console.warn('Firebase SDK non chargé');
}