import 'package:flutter/foundation.dart';

/// Service de logging centralisé pour remplacer les print()
class LoggingService {
  static const String _tag = 'HCP_CRM';

  /// Log d'information
  static void info(String message, [String? tag]) {
    _log('INFO', message, tag);
  }

  /// Log d'erreur
  static void error(String message, [String? tag, dynamic error]) {
    _log('ERROR', message, tag);
    if (error != null) {
      _log('ERROR', 'Details: $error', tag);
    }
  }

  /// Log de debug (seulement en mode debug)
  static void debug(String message, [String? tag]) {
    if (kDebugMode) {
      _log('DEBUG', message, tag);
    }
  }

  /// Log d'avertissement
  static void warning(String message, [String? tag]) {
    _log('WARNING', message, tag);
  }

  /// Log de succès
  static void success(String message, [String? tag]) {
    _log('SUCCESS', message, tag);
  }

  /// Méthode privée pour formater et afficher les logs
  static void _log(String level, String message, String? tag) {
    final timestamp = DateTime.now().toIso8601String();
    final logTag = tag ?? _tag;
    final formattedMessage = '[$timestamp] [$level] [$logTag] $message';
    
    if (kDebugMode) {
      debugPrint(formattedMessage);
    }
  }

  /// Log pour les opérations de base de données
  static void database(String operation, String table, [String? details]) {
    info('$operation on $table${details != null ? ': $details' : ''}', 'DATABASE');
  }

  /// Log pour les opérations de synchronisation
  static void sync(String message) {
    info(message, 'SYNC');
  }

  /// Log pour les opérations d'authentification
  static void auth(String message) {
    info(message, 'AUTH');
  }

  /// Log pour les opérations réseau
  static void network(String message) {
    info(message, 'NETWORK');
  }

  /// Log pour les migrations
  static void migration(String message) {
    info(message, 'MIGRATION');
  }
}
