import 'package:flutter/material.dart';

/// Widget de grille réorganisable qui permet de glisser-déposer les éléments
/// pour les réorganiser
class ReorderableGridView extends StatefulWidget {
  final List<Widget> children;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final EdgeInsets? padding;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final double? childAspectRatio;
  final int? forcedCrossAxisCount;
  final Function(int oldIndex, int newIndex) onReorder;

  const ReorderableGridView({
    super.key,
    required this.children,
    required this.onReorder,
    this.shrinkWrap = false,
    this.physics,
    this.padding,
    this.mainAxisSpacing,
    this.crossAxisSpacing,
    this.childAspectRatio,
    this.forcedCrossAxisCount,
  });

  @override
  State<ReorderableGridView> createState() => _ReorderableGridViewState();
}

class _ReorderableGridViewState extends State<ReorderableGridView> {
  late List<Widget> _items;
  late int _crossAxisCount;

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.children);
  }

  @override
  void didUpdateWidget(ReorderableGridView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.children != oldWidget.children) {
      _items = List.from(widget.children);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Déterminer le nombre de colonnes
    _crossAxisCount = widget.forcedCrossAxisCount ?? 2;

    // Utiliser un GridView.count pour organiser les éléments en grille
    return GridView.count(
      crossAxisCount: _crossAxisCount,
      childAspectRatio: widget.childAspectRatio ?? 1.0,
      crossAxisSpacing: widget.crossAxisSpacing ?? 8.0,
      mainAxisSpacing: widget.mainAxisSpacing ?? 8.0,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      padding: widget.padding,
      children: List.generate(_items.length, (index) {
        return Draggable<int>(
          data: index,
          feedback: Material(
            elevation: 4.0,
            child: Container(
              width: 200,
              height: 200 / (widget.childAspectRatio ?? 1.0),
              color: Colors.white.withValues(alpha: 0.8),
              child: _items[index],
            ),
          ),
          childWhenDragging: Opacity(opacity: 0.2, child: _items[index]),
          onDragEnd: (details) {
            if (details.wasAccepted) {
              // La réorganisation est gérée par DragTarget
            }
          },
          child: DragTarget<int>(
            builder: (context, candidateData, rejectedData) {
              return _items[index];
            },
            onAcceptWithDetails: (details) {
              final draggedIndex = details.data;
              if (draggedIndex != index) {
                // Appeler onReorder avec les indices appropriés
                int newIndex = index;
                if (draggedIndex < newIndex) {
                  newIndex -= 1;
                }
                widget.onReorder(draggedIndex, newIndex);
              }
            },
          ),
        );
      }),
    );
  }
}
