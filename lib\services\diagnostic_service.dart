import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/product.dart';

import '../models/invoice.dart';
import '../models/task.dart';
import '../models/colis.dart';

/// Service de diagnostic pour identifier les problèmes de performance
class DiagnosticService {
  /// Teste la sauvegarde d'un produit simple
  static Future<Map<String, dynamic>> testProductSave() async {
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint('🔍 Test de sauvegarde produit...');

      // Créer un produit de test simple
      final testProduct = Product(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        name: 'Produit Test',
        price: 10.0,
        quantity: 1,
        categoryId: 'test_category',
        description: 'Test',
      );

      // Tester la sauvegarde directe dans SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final existingProducts = prefs.getString('inventory_products');

      List<Product> products = [];
      if (existingProducts != null) {
        final List<dynamic> productsList = jsonDecode(existingProducts);
        products = productsList.map((json) => Product.fromJson(json)).toList();
      }

      products.add(testProduct);

      final productsJson = jsonEncode(products.map((p) => p.toJson()).toList());
      await prefs.setString('inventory_products', productsJson);

      stopwatch.stop();

      debugPrint('✅ Test produit réussi en ${stopwatch.elapsedMilliseconds}ms');

      return {
        'success': true,
        'duration': stopwatch.elapsedMilliseconds,
        'productId': testProduct.id,
        'message': 'Sauvegarde produit réussie',
      };
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ Test produit échoué: $e');

      return {
        'success': false,
        'duration': stopwatch.elapsedMilliseconds,
        'error': e.toString(),
        'message': 'Erreur lors de la sauvegarde produit',
      };
    }
  }

  /// Teste la sauvegarde d'une facture simple
  static Future<Map<String, dynamic>> testInvoiceSave() async {
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint('🔍 Test de sauvegarde facture...');

      // Créer une facture de test simple
      final testInvoice = Invoice(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        clientName: 'Client Test',
        clientNumber: '123456',
        products: 'Produit Test',
        items: [],
        deliveryLocation: 'Test Location',
        deliveryPrice: 10.0,
        advance: 0.0,
        subtotal: 100.0,
        total: 110.0,
        status: InvoiceStatus.enAttente,
        createdAt: DateTime.now(),
        type: InvoiceType.normale,
      );

      // Tester la sauvegarde directe dans SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final existingInvoices = prefs.getString('invoices');

      List<Invoice> invoices = [];
      if (existingInvoices != null) {
        final List<dynamic> invoicesList = jsonDecode(existingInvoices);
        invoices = invoicesList.map((json) => Invoice.fromJson(json)).toList();
      }

      invoices.add(testInvoice);

      final invoicesJson = jsonEncode(invoices.map((i) => i.toJson()).toList());
      await prefs.setString('invoices', invoicesJson);

      stopwatch.stop();

      debugPrint('✅ Test facture réussi en ${stopwatch.elapsedMilliseconds}ms');

      return {
        'success': true,
        'duration': stopwatch.elapsedMilliseconds,
        'invoiceId': testInvoice.id,
        'message': 'Sauvegarde facture réussie',
      };
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ Test facture échoué: $e');

      return {
        'success': false,
        'duration': stopwatch.elapsedMilliseconds,
        'error': e.toString(),
        'message': 'Erreur lors de la sauvegarde facture',
      };
    }
  }

  /// Teste la sauvegarde d'une tâche simple
  static Future<Map<String, dynamic>> testTaskSave() async {
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint('🔍 Test de sauvegarde tâche...');

      // Créer une tâche de test simple
      final testTask = Task(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        title: 'Tâche Test',
        description: 'Description test',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: TaskPriority.medium,
        isCompleted: false,
        createdAt: DateTime.now(),
      );

      // Tester la sauvegarde directe dans SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final existingTasks = prefs.getString('tasks');

      List<Task> tasks = [];
      if (existingTasks != null) {
        final List<dynamic> tasksList = jsonDecode(existingTasks);
        tasks = tasksList.map((json) => Task.fromJson(json)).toList();
      }

      tasks.add(testTask);

      final tasksJson = jsonEncode(tasks.map((t) => t.toJson()).toList());
      await prefs.setString('tasks', tasksJson);

      stopwatch.stop();

      debugPrint('✅ Test tâche réussi en ${stopwatch.elapsedMilliseconds}ms');

      return {
        'success': true,
        'duration': stopwatch.elapsedMilliseconds,
        'taskId': testTask.id,
        'message': 'Sauvegarde tâche réussie',
      };
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ Test tâche échoué: $e');

      return {
        'success': false,
        'duration': stopwatch.elapsedMilliseconds,
        'error': e.toString(),
        'message': 'Erreur lors de la sauvegarde tâche',
      };
    }
  }

  /// Teste la sauvegarde d'un colis simple
  static Future<Map<String, dynamic>> testColisSave() async {
    final stopwatch = Stopwatch()..start();

    try {
      debugPrint('🔍 Test de sauvegarde colis...');

      // Créer un colis de test simple
      final testColis = Colis(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        libelle: 'Colis Test',
        photoPath: '',
        zoneLivraison: 'Zone Test',
        numeroClient: '123456',
        resteAPayer: 100.0,
        fraisLivraison: 10.0,
        dateAjout: DateTime.now(),
        statut: StatutLivraison.enCours,
      );

      // Tester la sauvegarde directe dans SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final existingColis = prefs.getStringList('hcp_colis') ?? [];

      List<Colis> colisList = [];
      for (final colisJson in existingColis) {
        try {
          colisList.add(Colis.fromJson(jsonDecode(colisJson)));
        } catch (e) {
          debugPrint('Erreur parsing colis: $e');
        }
      }

      colisList.add(testColis);

      final colisJsonList =
          colisList.map((c) => jsonEncode(c.toJson())).toList();
      await prefs.setStringList('hcp_colis', colisJsonList);

      stopwatch.stop();

      debugPrint('✅ Test colis réussi en ${stopwatch.elapsedMilliseconds}ms');

      return {
        'success': true,
        'duration': stopwatch.elapsedMilliseconds,
        'colisId': testColis.id,
        'message': 'Sauvegarde colis réussie',
      };
    } catch (e) {
      stopwatch.stop();
      debugPrint('❌ Test colis échoué: $e');

      return {
        'success': false,
        'duration': stopwatch.elapsedMilliseconds,
        'error': e.toString(),
        'message': 'Erreur lors de la sauvegarde colis',
      };
    }
  }

  /// Lance tous les tests de diagnostic avec timeout
  static Future<Map<String, dynamic>> runAllTests() async {
    debugPrint('🚀 Lancement des tests de diagnostic...');

    final results = <String, dynamic>{};
    const timeout = Duration(seconds: 10); // Timeout de 10 secondes par test

    try {
      // Test produit avec timeout
      results['product'] = await testProductSave().timeout(
        timeout,
        onTimeout: () => {
          'success': false,
          'duration': 10000,
          'error': 'Timeout après 10 secondes',
          'message': 'Test produit timeout',
        },
      );

      // Test facture avec timeout
      results['invoice'] = await testInvoiceSave().timeout(
        timeout,
        onTimeout: () => {
          'success': false,
          'duration': 10000,
          'error': 'Timeout après 10 secondes',
          'message': 'Test facture timeout',
        },
      );

      // Test tâche avec timeout
      results['task'] = await testTaskSave().timeout(
        timeout,
        onTimeout: () => {
          'success': false,
          'duration': 10000,
          'error': 'Timeout après 10 secondes',
          'message': 'Test tâche timeout',
        },
      );

      // Test colis avec timeout
      results['colis'] = await testColisSave().timeout(
        timeout,
        onTimeout: () => {
          'success': false,
          'duration': 10000,
          'error': 'Timeout après 10 secondes',
          'message': 'Test colis timeout',
        },
      );

      // Résumé
      final successCount =
          results.values.where((r) => r['success'] == true).length;
      final totalTests = results.length;

      results['summary'] = {
        'totalTests': totalTests,
        'successCount': successCount,
        'failureCount': totalTests - successCount,
        'allPassed': successCount == totalTests,
      };

      debugPrint('📊 Tests terminés: $successCount/$totalTests réussis');

      return results;
    } catch (e) {
      debugPrint('❌ Erreur lors des tests de diagnostic: $e');
      return {
        'error': e.toString(),
        'summary': {
          'totalTests': 0,
          'successCount': 0,
          'failureCount': 1,
          'allPassed': false,
        },
      };
    }
  }

  /// Nettoie les données de test
  static Future<void> cleanupTestData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Nettoyer les produits de test
      final productsJson = prefs.getString('inventory_products');
      if (productsJson != null) {
        final List<dynamic> productsList = jsonDecode(productsJson);
        final products =
            productsList.map((json) => Product.fromJson(json)).toList();
        products.removeWhere((p) => p.id.startsWith('test_'));

        final cleanProductsJson = jsonEncode(
          products.map((p) => p.toJson()).toList(),
        );
        await prefs.setString('inventory_products', cleanProductsJson);
      }

      // Nettoyer les factures de test
      final invoicesJson = prefs.getString('invoices');
      if (invoicesJson != null) {
        final List<dynamic> invoicesList = jsonDecode(invoicesJson);
        final invoices =
            invoicesList.map((json) => Invoice.fromJson(json)).toList();
        invoices.removeWhere((i) => i.id.startsWith('test_'));

        final cleanInvoicesJson = jsonEncode(
          invoices.map((i) => i.toJson()).toList(),
        );
        await prefs.setString('invoices', cleanInvoicesJson);
      }

      // Nettoyer les tâches de test
      final tasksJson = prefs.getString('tasks');
      if (tasksJson != null) {
        final List<dynamic> tasksList = jsonDecode(tasksJson);
        final tasks = tasksList.map((json) => Task.fromJson(json)).toList();
        tasks.removeWhere((t) => t.id.startsWith('test_'));

        final cleanTasksJson = jsonEncode(
          tasks.map((t) => t.toJson()).toList(),
        );
        await prefs.setString('tasks', cleanTasksJson);
      }

      // Nettoyer les colis de test
      final colisJsonList = prefs.getStringList('hcp_colis') ?? [];
      final cleanColisList = <String>[];

      for (final colisJson in colisJsonList) {
        try {
          final colis = Colis.fromJson(jsonDecode(colisJson));
          if (!colis.id.startsWith('test_')) {
            cleanColisList.add(colisJson);
          }
        } catch (e) {
          // Garder les données valides même si on ne peut pas les parser
          cleanColisList.add(colisJson);
        }
      }

      await prefs.setStringList('hcp_colis', cleanColisList);

      debugPrint('🧹 Nettoyage des données de test terminé');
    } catch (e) {
      debugPrint('❌ Erreur lors du nettoyage: $e');
    }
  }
}
