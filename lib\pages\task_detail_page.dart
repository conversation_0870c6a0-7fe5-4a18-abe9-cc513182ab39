import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/advanced_task.dart';
import '../models/task_activity.dart';
import '../services/advanced_task_service.dart';
import '../widgets/task_dialogs.dart';
import '../widgets/advanced_task_widgets.dart';

class TaskDetailPage extends StatefulWidget {
  final AdvancedTask task;

  const TaskDetailPage({super.key, required this.task});

  @override
  State<TaskDetailPage> createState() => _TaskDetailPageState();
}

class _TaskDetailPageState extends State<TaskDetailPage>
    with TickerProviderStateMixin {
  final AdvancedTaskService _taskService = AdvancedTaskService();
  final _commentController = TextEditingController();

  late TabController _tabController;
  late AdvancedTask _task;

  List<AdvancedTask> _subtasks = [];
  List<TaskComment> _comments = [];
  List<TaskActivity> _activities = [];

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _task = widget.task;
    _tabController = TabController(length: 4, vsync: this);
    _loadTaskData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  Future<void> _loadTaskData() async {
    setState(() => _isLoading = true);

    try {
      await _taskService.initialize();

      final subtasks = await _taskService.getSubtasks(_task.id);
      final comments = await _taskService.getCommentsByTask(_task.id);
      final activities = await _taskService.getActivitiesByTask(_task.id);

      setState(() {
        _subtasks = subtasks;
        _comments = comments;
        _activities = activities;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors du chargement: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          _task.title,
          style: TextStyle(fontSize: isMobile ? 16 : 20),
          overflow: TextOverflow.ellipsis,
        ),
        backgroundColor: _task.priority.color,
        foregroundColor: Colors.white,
        actions: _buildAppBarActions(isMobile),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  _buildTaskHeader(),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildOverviewTab(),
                        _buildSubtasksTab(),
                        _buildCommentsTab(),
                        _buildActivityTab(),
                      ],
                    ),
                  ),
                ],
              ),
      bottomNavigationBar: TabBar(
        controller: _tabController,
        isScrollable: isMobile,
        tabs:
            isMobile
                ? const [
                  Tab(icon: Icon(Icons.info, size: 20), text: 'Aperçu'),
                  Tab(icon: Icon(Icons.list, size: 20), text: 'Sous-tâches'),
                  Tab(
                    icon: Icon(Icons.comment, size: 20),
                    text: 'Commentaires',
                  ),
                  Tab(icon: Icon(Icons.history, size: 20), text: 'Activité'),
                ]
                : const [
                  Tab(icon: Icon(Icons.info), text: 'Aperçu'),
                  Tab(icon: Icon(Icons.list), text: 'Sous-tâches'),
                  Tab(icon: Icon(Icons.comment), text: 'Commentaires'),
                  Tab(icon: Icon(Icons.history), text: 'Activité'),
                ],
      ),
    );
  }

  Widget _buildTaskHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _task.priority.color.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildStatusChip(),
              const SizedBox(width: 12),
              _buildPriorityChip(),
              const Spacer(),
              if (_task.dueDate != null) _buildDueDateChip(),
            ],
          ),
          const SizedBox(height: 12),
          if (_task.description.isNotEmpty) ...[
            Text(
              _task.description,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 12),
          ],
          if (_task.tags.isNotEmpty) TaskTagsWidget(tags: _task.tags),
        ],
      ),
    );
  }

  Widget _buildStatusChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getStatusColor(_task.status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getStatusColor(_task.status)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(_task.status),
            size: 16,
            color: _getStatusColor(_task.status),
          ),
          const SizedBox(width: 6),
          Text(
            _task.status.displayName,
            style: TextStyle(
              color: _getStatusColor(_task.status),
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _task.priority.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _task.priority.color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.flag, size: 16, color: _task.priority.color),
          const SizedBox(width: 6),
          Text(
            _task.priority.displayName,
            style: TextStyle(
              color: _task.priority.color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDueDateChip() {
    final isOverdue = _task.isOverdue;
    final color = isOverdue ? Colors.red : Colors.blue;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isOverdue ? Icons.warning : Icons.schedule,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 6),
          Text(
            DateFormat('dd/MM/yyyy').format(_task.dueDate!),
            style: TextStyle(color: color, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoCard('Informations générales', [
            _buildInfoRow(
              'Créée le',
              DateFormat('dd/MM/yyyy à HH:mm').format(_task.createdAt),
            ),
            if (_task.startDate != null)
              _buildInfoRow(
                'Date de début',
                DateFormat('dd/MM/yyyy').format(_task.startDate!),
              ),
            if (_task.dueDate != null)
              _buildInfoRow(
                'Date d\'échéance',
                DateFormat('dd/MM/yyyy').format(_task.dueDate!),
              ),
            if (_task.assigneeId != null)
              _buildInfoRow('Responsable', _task.assigneeId!),
            if (_task.estimatedHours != null)
              _buildInfoRow('Estimation', '${_task.estimatedHours}h'),
          ]),

          const SizedBox(height: 16),

          _buildInfoCard('Progression', [_buildProgressRow()]),

          if (_subtasks.isNotEmpty) ...[
            const SizedBox(height: 16),
            _buildInfoCard('Sous-tâches (${_subtasks.length})', [
              ..._subtasks
                  .take(3)
                  .map(
                    (subtask) => ListTile(
                      contentPadding: EdgeInsets.zero,
                      leading: Icon(
                        _getStatusIcon(subtask.status),
                        color: _getStatusColor(subtask.status),
                      ),
                      title: Text(subtask.title),
                      subtitle: Text(subtask.status.displayName),
                    ),
                  ),
              if (_subtasks.length > 3)
                TextButton(
                  onPressed: () => _tabController.animateTo(1),
                  child: Text(
                    'Voir toutes les sous-tâches (${_subtasks.length})',
                  ),
                ),
            ]),
          ],
        ],
      ),
    );
  }

  Widget _buildSubtasksTab() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                'Sous-tâches (${_subtasks.length})',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: _addSubtask,
                icon: const Icon(Icons.add),
                label: const Text('Ajouter'),
              ),
            ],
          ),
        ),
        Expanded(
          child:
              _subtasks.isEmpty
                  ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.list_alt, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'Aucune sous-tâche',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _subtasks.length,
                    itemBuilder: (context, index) {
                      final subtask = _subtasks[index];
                      return TaskListTile(
                        task: subtask,
                        onTap: () => _openSubtask(subtask),
                        onStatusChanged:
                            (status) => _updateSubtaskStatus(subtask, status),
                      );
                    },
                  ),
        ),
      ],
    );
  }

  Widget _buildCommentsTab() {
    return Column(
      children: [
        Expanded(
          child:
              _comments.isEmpty
                  ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.comment, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          'Aucun commentaire',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                        ),
                      ],
                    ),
                  )
                  : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _comments.length,
                    itemBuilder: (context, index) {
                      final comment = _comments[index];
                      return _buildCommentItem(comment);
                    },
                  ),
        ),
        _buildCommentInput(),
      ],
    );
  }

  Widget _buildActivityTab() {
    return _activities.isEmpty
        ? const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.history, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'Aucune activité',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            ],
          ),
        )
        : ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _activities.length,
          itemBuilder: (context, index) {
            final activity = _activities[index];
            return _buildActivityItem(activity);
          },
        );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildProgressRow() {
    final completedSubtasks = _subtasks.where((t) => t.isCompleted).length;
    final totalSubtasks = _subtasks.length;
    final progress =
        totalSubtasks > 0 ? completedSubtasks / totalSubtasks : 0.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Sous-tâches terminées'),
            Text('$completedSubtasks/$totalSubtasks'),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey.withValues(alpha: 0.2),
          valueColor: AlwaysStoppedAnimation<Color>(_task.priority.color),
        ),
      ],
    );
  }

  Widget _buildCommentItem(TaskComment comment) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.blue.withValues(alpha: 0.1),
                  child: const Icon(Icons.person, size: 16, color: Colors.blue),
                ),
                const SizedBox(width: 8),
                Text(
                  comment.authorId,
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                const Spacer(),
                Text(
                  DateFormat('dd/MM/yyyy à HH:mm').format(comment.createdAt),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(comment.content),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(TaskActivity activity) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getActivityIcon(activity.type),
          color: _getActivityColor(activity.type),
        ),
        title: Text(activity.description),
        subtitle: Text(
          DateFormat('dd/MM/yyyy à HH:mm').format(activity.createdAt),
        ),
        trailing: Text(
          activity.userId,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(color: Colors.grey.withValues(alpha: 0.2)),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: const InputDecoration(
                hintText: 'Ajouter un commentaire...',
                border: OutlineInputBorder(),
              ),
              maxLines: null,
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(onPressed: _addComment, child: const Text('Envoyer')),
        ],
      ),
    );
  }

  Color _getStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Colors.grey;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.inReview:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.blocked:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(TaskStatus status) {
    switch (status) {
      case TaskStatus.todo:
        return Icons.radio_button_unchecked;
      case TaskStatus.inProgress:
        return Icons.play_circle;
      case TaskStatus.inReview:
        return Icons.rate_review;
      case TaskStatus.completed:
        return Icons.check_circle;
      case TaskStatus.blocked:
        return Icons.block;
    }
  }

  IconData _getActivityIcon(ActivityType type) {
    switch (type) {
      case ActivityType.created:
        return Icons.add_circle;
      case ActivityType.updated:
        return Icons.edit;
      case ActivityType.statusChanged:
        return Icons.flag;
      case ActivityType.commentAdded:
        return Icons.comment;
      case ActivityType.completed:
        return Icons.check_circle;
      default:
        return Icons.info;
    }
  }

  Color _getActivityColor(ActivityType type) {
    switch (type) {
      case ActivityType.created:
        return Colors.green;
      case ActivityType.updated:
        return Colors.blue;
      case ActivityType.statusChanged:
        return Colors.orange;
      case ActivityType.commentAdded:
        return Colors.purple;
      case ActivityType.completed:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  void _editTask() {
    showDialog(
      context: context,
      builder:
          (context) => TaskDialog(
            task: _task,
            onTaskSaved: (updatedTask) async {
              final messenger = ScaffoldMessenger.of(context);
              try {
                await _taskService.updateTask(updatedTask);
                setState(() {
                  _task = updatedTask;
                });
                if (mounted) {
                  messenger.showSnackBar(
                    const SnackBar(
                      content: Text('Tâche mise à jour avec succès'),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la mise à jour: $e'),
                    ),
                  );
                }
              }
            },
          ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        _editTask();
        break;
      case 'duplicate':
        _duplicateTask();
        break;
      case 'delete':
        _deleteTask();
        break;
    }
  }

  void _duplicateTask() {
    final duplicatedTask = _task.copyWith(
      id: '',
      title: '${_task.title} (copie)',
      createdAt: DateTime.now(),
      status: TaskStatus.todo,
    );

    showDialog(
      context: context,
      builder:
          (context) => TaskDialog(
            task: duplicatedTask,
            onTaskSaved: (task) async {
              final messenger = ScaffoldMessenger.of(context);
              try {
                await _taskService.addTask(task);
                if (mounted) {
                  messenger.showSnackBar(
                    const SnackBar(
                      content: Text('Tâche dupliquée avec succès'),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(
                      content: Text('Erreur lors de la duplication: $e'),
                    ),
                  );
                }
              }
            },
          ),
    );
  }

  void _deleteTask() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Supprimer la tâche'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer "${_task.title}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  final messenger = ScaffoldMessenger.of(context);
                  try {
                    await _taskService.deleteTask(_task.id);
                    navigator.pop(); // Fermer le dialogue
                    navigator.pop(); // Retourner à la liste
                    if (mounted) {
                      messenger.showSnackBar(
                        const SnackBar(
                          content: Text('Tâche supprimée avec succès'),
                        ),
                      );
                    }
                  } catch (e) {
                    navigator.pop();
                    if (mounted) {
                      messenger.showSnackBar(
                        SnackBar(
                          content: Text('Erreur lors de la suppression: $e'),
                        ),
                      );
                    }
                  }
                },
                child: const Text(
                  'Supprimer',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
    );
  }

  void _addSubtask() {
    showDialog(
      context: context,
      builder:
          (context) => TaskDialog(
            projectId: _task.projectId,
            sectionId: _task.sectionId,
            onTaskSaved: (subtask) async {
              final messenger = ScaffoldMessenger.of(context);
              try {
                final subtaskWithParent = subtask.copyWith(
                  parentTaskId: _task.id,
                );
                await _taskService.addTask(subtaskWithParent);
                _loadTaskData();
                if (mounted) {
                  messenger.showSnackBar(
                    const SnackBar(
                      content: Text('Sous-tâche créée avec succès'),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  messenger.showSnackBar(
                    SnackBar(content: Text('Erreur lors de la création: $e')),
                  );
                }
              }
            },
          ),
    );
  }

  void _openSubtask(AdvancedTask subtask) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => TaskDetailPage(task: subtask)),
    ).then((_) => _loadTaskData());
  }

  void _updateSubtaskStatus(AdvancedTask subtask, TaskStatus newStatus) async {
    try {
      final updatedSubtask = subtask.copyWith(status: newStatus);
      await _taskService.updateTask(updatedSubtask);
      _loadTaskData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la mise à jour: $e')),
        );
      }
    }
  }

  void _addComment() async {
    final content = _commentController.text.trim();
    if (content.isEmpty) return;

    try {
      final comment = TaskComment(
        id: '',
        taskId: _task.id,
        authorId: 'user_${DateTime.now().millisecondsSinceEpoch}',
        content: content,
        createdAt: DateTime.now(),
      );

      await _taskService.addComment(comment);
      _commentController.clear();
      _loadTaskData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de l\'ajout du commentaire: $e')),
        );
      }
    }
  }

  List<Widget> _buildAppBarActions(bool isMobile) {
    if (isMobile) {
      return [
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit),
                      SizedBox(width: 8),
                      Text('Modifier'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'duplicate',
                  child: Row(
                    children: [
                      Icon(Icons.copy),
                      SizedBox(width: 8),
                      Text('Dupliquer'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Supprimer', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
        ),
      ];
    } else {
      return [
        IconButton(
          icon: const Icon(Icons.edit),
          onPressed: _editTask,
          tooltip: 'Modifier',
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'duplicate',
                  child: Row(
                    children: [
                      Icon(Icons.copy),
                      SizedBox(width: 8),
                      Text('Dupliquer'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red),
                      SizedBox(width: 8),
                      Text('Supprimer', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
        ),
      ];
    }
  }
}
