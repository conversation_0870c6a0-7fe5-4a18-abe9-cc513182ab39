@echo off
echo ========================================
echo    TEST DE SYNCHRONISATION COMPLÈTE
echo ========================================
echo Ce script vérifie que vos données mobiles
echo sont bien synchronisées avec la version web
echo ========================================
echo.

REM Vérifier que Flutter est installé
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter n'est pas installé ou pas dans le PATH
    pause
    exit /b 1
)

echo ✅ Flutter détecté
echo.

REM Étape 1: Analyse du code
echo ℹ️ [1/6] Analyse du code...
flutter analyze
if %errorlevel% neq 0 (
    echo ❌ Problèmes d'analyse détectés - Veuillez corriger avant de continuer
    pause
    exit /b 1
)
echo ✅ Analyse réussie
echo.

REM Étape 2: Nettoyage
echo ℹ️ [2/6] Nettoyage des builds précédents...
flutter clean
flutter pub get
echo ✅ Nettoyage terminé
echo.

REM Étape 3: Test de compilation mobile
echo ℹ️ [3/6] Test de compilation mobile...
flutter build apk --debug --target-platform android-arm64
if %errorlevel% neq 0 (
    echo ❌ Échec de la compilation mobile
    pause
    exit /b 1
)
echo ✅ Compilation mobile réussie
echo.

REM Étape 4: Test de compilation web
echo ℹ️ [4/6] Test de compilation web...
flutter build web --release
if %errorlevel% neq 0 (
    echo ❌ Échec de la compilation web
    pause
    exit /b 1
)
echo ✅ Compilation web réussie
echo.

REM Étape 5: Vérification des fichiers de synchronisation
echo ℹ️ [5/6] Vérification des services de synchronisation...

if not exist "lib\services\unified_sync_service.dart" (
    echo ❌ Service de synchronisation unifié manquant
    pause
    exit /b 1
)

if not exist "lib\services\sync_verification_service.dart" (
    echo ❌ Service de vérification manquant
    pause
    exit /b 1
)

if not exist "lib\pages\sync_diagnostic_page.dart" (
    echo ❌ Page de diagnostic manquant
    pause
    exit /b 1
)

echo ✅ Tous les services de synchronisation sont présents
echo.

REM Étape 6: Instructions et démarrage du serveur
echo ℹ️ [6/6] Démarrage du serveur web de test...
echo.
echo ℹ️ 🌐 INSTRUCTIONS POUR TESTER LA SYNCHRONISATION:
echo.
echo ⚠️ 1. MOBILE:
echo    • Installez l'APK: build\app\outputs\flutter-apk\app-debug.apk
echo    • Ouvrez l'application mobile
echo    • Vérifiez que vos données existantes sont présentes
echo    • Appuyez sur l'icône 🔄 dans le dashboard pour voir le diagnostic
echo.
echo ⚠️ 2. WEB:
echo    • Ouvrez votre navigateur sur: http://localhost:8000
echo    • Vérifiez que les mêmes données apparaissent
echo    • Appuyez sur l'icône 🔄 dans le dashboard pour voir le diagnostic
echo.
echo ⚠️ 3. TEST DE SYNCHRONISATION:
echo    • Ajoutez une facture sur mobile
echo    • Vérifiez qu'elle apparaît sur web (peut prendre quelques secondes)
echo    • Ajoutez un produit sur web
echo    • Vérifiez qu'il apparaît sur mobile
echo.
echo ✅ IMPORTANT: Vos données mobiles existantes seront automatiquement
echo ✅ synchronisées vers le web lors du premier lancement!
echo.
echo ℹ️ Serveur web démarré sur http://localhost:8000
echo ℹ️ Appuyez sur Ctrl+C pour arrêter le serveur
echo.

REM Démarrer le serveur web
cd build\web
python -m http.server 8000
