import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/advanced_task.dart';
import '../models/task_user.dart';
import '../services/advanced_task_service.dart';

/// Dialogue pour les filtres avancés
class FilterDialog extends StatefulWidget {
  final TaskFilter currentFilter;
  final Function(TaskFilter) onFilterChanged;

  const FilterDialog({
    super.key,
    required this.currentFilter,
    required this.onFilterChanged,
  });

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  final AdvancedTaskService _taskService = AdvancedTaskService();

  late TaskFilter _filter;
  List<Project> _projects = [];
  List<TaskSection> _sections = [];
  List<String> _availableTags = [];

  @override
  void initState() {
    super.initState();
    _filter = widget.currentFilter;
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      await _taskService.initialize();
      final projects = await _taskService.getProjects();
      final tasks = await _taskService.getTasks();

      // Extraire tous les tags uniques
      final tags = <String>{};
      for (final task in tasks) {
        tags.addAll(task.tags);
      }

      setState(() {
        _projects = projects;
        _availableTags = tags.toList()..sort();
      });

      if (_filter.projectId != null) {
        await _loadSections(_filter.projectId!);
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des données: $e');
    }
  }

  Future<void> _loadSections(String projectId) async {
    try {
      final sections = await _taskService.getSectionsByProject(projectId);
      setState(() {
        _sections = sections;
      });
    } catch (e) {
      debugPrint('Erreur lors du chargement des sections: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête
            Row(
              children: [
                const Icon(Icons.filter_list, size: 28),
                const SizedBox(width: 12),
                Text(
                  'Filtres avancés',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Contenu des filtres
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildProjectFilter(),
                    const SizedBox(height: 24),
                    _buildStatusAndPriorityFilters(),
                    const SizedBox(height: 24),
                    _buildDateFilters(),
                    const SizedBox(height: 24),
                    _buildTagsFilter(),
                    const SizedBox(height: 24),
                    _buildOtherFilters(),
                  ],
                ),
              ),
            ),

            // Boutons d'action
            const SizedBox(height: 24),
            Row(
              children: [
                TextButton(
                  onPressed: _clearFilters,
                  child: const Text('Effacer tout'),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Annuler'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _applyFilters,
                  child: const Text('Appliquer'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProjectFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Projet et Section',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _filter.projectId,
                decoration: const InputDecoration(
                  labelText: 'Projet',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('Tous les projets'),
                  ),
                  ..._projects.map(
                    (project) => DropdownMenuItem(
                      value: project.id,
                      child: Row(
                        children: [
                          Container(
                            width: 16,
                            height: 16,
                            decoration: BoxDecoration(
                              color: project.color,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(child: Text(project.name)),
                        ],
                      ),
                    ),
                  ),
                ],
                onChanged: (projectId) {
                  setState(() {
                    _filter = _filter.copyWith(
                      projectId: projectId,
                      sectionId: null,
                    );
                    _sections.clear();
                  });
                  if (projectId != null) {
                    _loadSections(projectId);
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _filter.sectionId,
                decoration: const InputDecoration(
                  labelText: 'Section',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('Toutes les sections'),
                  ),
                  ..._sections.map(
                    (section) => DropdownMenuItem(
                      value: section.id,
                      child: Text(section.name),
                    ),
                  ),
                ],
                onChanged: (sectionId) {
                  setState(() {
                    _filter = _filter.copyWith(sectionId: sectionId);
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatusAndPriorityFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Statut et Priorité',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // Statuts
        Text('Statuts', style: Theme.of(context).textTheme.titleSmall),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              TaskStatus.values.map((status) {
                final isSelected = _filter.statuses.contains(status);
                return FilterChip(
                  label: Text(status.displayName),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      final statuses = List<TaskStatus>.from(_filter.statuses);
                      if (selected) {
                        statuses.add(status);
                      } else {
                        statuses.remove(status);
                      }
                      _filter = _filter.copyWith(statuses: statuses);
                    });
                  },
                );
              }).toList(),
        ),

        const SizedBox(height: 16),

        // Priorités
        Text('Priorités', style: Theme.of(context).textTheme.titleSmall),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children:
              TaskPriority.values.map((priority) {
                final isSelected = _filter.priorities.contains(priority);
                return FilterChip(
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.flag, color: priority.color, size: 16),
                      const SizedBox(width: 4),
                      Text(priority.displayName),
                    ],
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      final priorities = List<TaskPriority>.from(
                        _filter.priorities,
                      );
                      if (selected) {
                        priorities.add(priority);
                      } else {
                        priorities.remove(priority);
                      }
                      _filter = _filter.copyWith(priorities: priorities);
                    });
                  },
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildDateFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dates',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        // Dates d'échéance
        Text('Échéance', style: Theme.of(context).textTheme.titleSmall),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectFilterDate(context, 'dueDateFrom'),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Du',
                    border: OutlineInputBorder(),
                  ),
                  child: Text(
                    _filter.dueDateFrom != null
                        ? DateFormat('dd/MM/yyyy').format(_filter.dueDateFrom!)
                        : 'Aucune date',
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: () => _selectFilterDate(context, 'dueDateTo'),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Au',
                    border: OutlineInputBorder(),
                  ),
                  child: Text(
                    _filter.dueDateTo != null
                        ? DateFormat('dd/MM/yyyy').format(_filter.dueDateTo!)
                        : 'Aucune date',
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTagsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        if (_availableTags.isNotEmpty)
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                _availableTags.map((tag) {
                  final isSelected = _filter.tags.contains(tag);
                  return FilterChip(
                    label: Text(tag),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        final tags = List<String>.from(_filter.tags);
                        if (selected) {
                          tags.add(tag);
                        } else {
                          tags.remove(tag);
                        }
                        _filter = _filter.copyWith(tags: tags);
                      });
                    },
                  );
                }).toList(),
          )
        else
          const Text('Aucun tag disponible'),
      ],
    );
  }

  Widget _buildOtherFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Autres filtres',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        CheckboxListTile(
          title: const Text('Tâches en retard uniquement'),
          value: _filter.isOverdue ?? false,
          onChanged: (value) {
            setState(() {
              _filter = _filter.copyWith(isOverdue: value);
            });
          },
        ),

        CheckboxListTile(
          title: const Text('Avec pièces jointes'),
          value: _filter.hasAttachments ?? false,
          onChanged: (value) {
            setState(() {
              _filter = _filter.copyWith(hasAttachments: value);
            });
          },
        ),

        CheckboxListTile(
          title: const Text('Avec commentaires'),
          value: _filter.hasComments ?? false,
          onChanged: (value) {
            setState(() {
              _filter = _filter.copyWith(hasComments: value);
            });
          },
        ),
      ],
    );
  }

  Future<void> _selectFilterDate(BuildContext context, String dateType) async {
    DateTime? initialDate;
    switch (dateType) {
      case 'dueDateFrom':
        initialDate = _filter.dueDateFrom;
        break;
      case 'dueDateTo':
        initialDate = _filter.dueDateTo;
        break;
    }

    final date = await showDatePicker(
      context: context,
      initialDate: initialDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (date != null) {
      setState(() {
        switch (dateType) {
          case 'dueDateFrom':
            _filter = _filter.copyWith(dueDateFrom: date);
            break;
          case 'dueDateTo':
            _filter = _filter.copyWith(dueDateTo: date);
            break;
        }
      });
    }
  }

  void _clearFilters() {
    setState(() {
      _filter = TaskFilter();
    });
  }

  void _applyFilters() {
    widget.onFilterChanged(_filter);
    Navigator.pop(context);
  }
}

/// Dialogue pour ajouter ou modifier une tâche
class TaskDialog extends StatefulWidget {
  final AdvancedTask? task;
  final String? projectId;
  final String? sectionId;
  final Function(AdvancedTask) onTaskSaved;

  const TaskDialog({
    super.key,
    this.task,
    this.projectId,
    this.sectionId,
    required this.onTaskSaved,
  });

  @override
  State<TaskDialog> createState() => _TaskDialogState();
}

class _TaskDialogState extends State<TaskDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final AdvancedTaskService _taskService = AdvancedTaskService();

  TaskPriority _selectedPriority = TaskPriority.medium;
  TaskStatus _selectedStatus = TaskStatus.todo;
  DateTime? _startDate;
  DateTime? _dueDate;
  String? _selectedProjectId;
  String? _selectedSectionId;
  String? _selectedAssigneeId;
  List<String> _tags = [];
  double? _estimatedHours;

  List<Project> _projects = [];
  List<TaskSection> _sections = [];

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedProjectId = widget.projectId;
    _selectedSectionId = widget.sectionId;
    _loadData();
    _initializeFields();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      await _taskService.initialize();
      final projects = await _taskService.getProjects();

      setState(() {
        _projects = projects;
      });

      if (_selectedProjectId != null) {
        await _loadSections(_selectedProjectId!);
      }
    } catch (e) {
      debugPrint('Erreur lors du chargement des données: $e');
    }
  }

  Future<void> _loadSections(String projectId) async {
    try {
      final sections = await _taskService.getSectionsByProject(projectId);
      setState(() {
        _sections = sections;
      });
    } catch (e) {
      debugPrint('Erreur lors du chargement des sections: $e');
    }
  }

  void _initializeFields() {
    if (widget.task != null) {
      final task = widget.task!;
      _titleController.text = task.title;
      _descriptionController.text = task.description;
      _selectedPriority = task.priority;
      _selectedStatus = task.status;
      _startDate = task.startDate;
      _dueDate = task.dueDate;
      _selectedProjectId = task.projectId;
      _selectedSectionId = task.sectionId;
      _selectedAssigneeId = task.assigneeId;
      _tags = List.from(task.tags);
      _estimatedHours = task.estimatedHours;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 600;

    if (isMobile) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            widget.task == null ? 'Nouvelle tâche' : 'Modifier la tâche',
          ),
          actions: [
            TextButton(
              onPressed: _isLoading ? null : _saveTask,
              child:
                  _isLoading
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Text(
                        'Sauvegarder',
                        style: TextStyle(color: Colors.white),
                      ),
            ),
          ],
        ),
        body: _buildFormContent(isMobile),
      );
    }

    return Dialog(
      child: Container(
        width: screenWidth * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête
            Row(
              children: [
                Icon(
                  widget.task == null ? Icons.add_task : Icons.edit,
                  size: 28,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  widget.task == null ? 'Nouvelle tâche' : 'Modifier la tâche',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Contenu du formulaire
            Expanded(child: _buildFormContent(false)),

            // Boutons d'action (desktop seulement)
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Annuler'),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: _isLoading ? null : _saveTask,
                  child:
                      _isLoading
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : Text(widget.task == null ? 'Créer' : 'Modifier'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormContent(bool isMobile) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(isMobile ? 16 : 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBasicFields(),
            const SizedBox(height: 24),
            _buildProjectAndSection(),
            const SizedBox(height: 24),
            _buildPriorityAndStatus(),
            const SizedBox(height: 24),
            _buildDates(),
            const SizedBox(height: 24),
            _buildAssignmentFields(),
            const SizedBox(height: 24),
            _buildTagsField(),
            const SizedBox(height: 24),
            _buildEstimationField(),
            if (isMobile) const SizedBox(height: 80), // Espace pour le FAB
          ],
        ),
      ),
    );
  }

  Widget _buildBasicFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'Titre de la tâche *',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.title),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Le titre est requis';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: 'Description',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.description),
          ),
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildProjectAndSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Organisation',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedProjectId,
                decoration: const InputDecoration(
                  labelText: 'Projet',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.folder),
                ),
                items:
                    _projects
                        .map(
                          (project) => DropdownMenuItem(
                            value: project.id,
                            child: Row(
                              children: [
                                Container(
                                  width: 16,
                                  height: 16,
                                  decoration: BoxDecoration(
                                    color: project.color,
                                    shape: BoxShape.circle,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(child: Text(project.name)),
                              ],
                            ),
                          ),
                        )
                        .toList(),
                onChanged: (projectId) {
                  setState(() {
                    _selectedProjectId = projectId;
                    _selectedSectionId = null;
                    _sections.clear();
                  });
                  if (projectId != null) {
                    _loadSections(projectId);
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedSectionId,
                decoration: const InputDecoration(
                  labelText: 'Section',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.view_list),
                ),
                items: [
                  const DropdownMenuItem(
                    value: null,
                    child: Text('Aucune section'),
                  ),
                  ..._sections.map(
                    (section) => DropdownMenuItem(
                      value: section.id,
                      child: Text(section.name),
                    ),
                  ),
                ],
                onChanged: (sectionId) {
                  setState(() {
                    _selectedSectionId = sectionId;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriorityAndStatus() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Priorité et Statut',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<TaskPriority>(
                value: _selectedPriority,
                decoration: const InputDecoration(
                  labelText: 'Priorité',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.flag),
                ),
                items:
                    TaskPriority.values
                        .map(
                          (priority) => DropdownMenuItem(
                            value: priority,
                            child: Row(
                              children: [
                                Icon(
                                  Icons.flag,
                                  color: priority.color,
                                  size: 16,
                                ),
                                const SizedBox(width: 8),
                                Text(priority.displayName),
                              ],
                            ),
                          ),
                        )
                        .toList(),
                onChanged: (priority) {
                  if (priority != null) {
                    setState(() {
                      _selectedPriority = priority;
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<TaskStatus>(
                value: _selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Statut',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.radio_button_checked),
                ),
                items:
                    TaskStatus.values
                        .map(
                          (status) => DropdownMenuItem(
                            value: status,
                            child: Text(status.displayName),
                          ),
                        )
                        .toList(),
                onChanged: (status) {
                  if (status != null) {
                    setState(() {
                      _selectedStatus = status;
                    });
                  }
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDates() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dates',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(context, true),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Date de début',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.play_arrow),
                  ),
                  child: Text(
                    _startDate != null
                        ? DateFormat('dd/MM/yyyy').format(_startDate!)
                        : 'Aucune date',
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(context, false),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Date d\'échéance',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.schedule),
                  ),
                  child: Text(
                    _dueDate != null
                        ? DateFormat('dd/MM/yyyy').format(_dueDate!)
                        : 'Aucune date',
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAssignmentFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Assignation',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        TextFormField(
          initialValue: _selectedAssigneeId,
          decoration: const InputDecoration(
            labelText: 'Responsable (ID)',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
            hintText: 'Laissez vide pour non assigné',
          ),
          onChanged: (value) {
            _selectedAssigneeId = value.trim().isEmpty ? null : value.trim();
          },
        ),
      ],
    );
  }

  Widget _buildTagsField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tags',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_tags.isNotEmpty) ...[
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      _tags
                          .map(
                            (tag) => Chip(
                              label: Text(tag),
                              deleteIcon: const Icon(Icons.close, size: 16),
                              onDeleted: () {
                                setState(() {
                                  _tags.remove(tag);
                                });
                              },
                            ),
                          )
                          .toList(),
                ),
                const SizedBox(height: 8),
              ],
              TextButton.icon(
                onPressed: _showAddTagDialog,
                icon: const Icon(Icons.add),
                label: const Text('Ajouter un tag'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEstimationField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Estimation',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        TextFormField(
          initialValue: _estimatedHours?.toString(),
          decoration: const InputDecoration(
            labelText: 'Heures estimées',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.timer),
            suffixText: 'h',
          ),
          keyboardType: TextInputType.number,
          onChanged: (value) {
            _estimatedHours = double.tryParse(value);
          },
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final date = await showDatePicker(
      context: context,
      initialDate:
          isStartDate
              ? (_startDate ?? DateTime.now())
              : (_dueDate ?? DateTime.now()),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (date != null) {
      setState(() {
        if (isStartDate) {
          _startDate = date;
        } else {
          _dueDate = date;
        }
      });
    }
  }

  void _showAddTagDialog() {
    final controller = TextEditingController();
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Ajouter un tag'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Nom du tag',
                border: OutlineInputBorder(),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              ElevatedButton(
                onPressed: () {
                  final tag = controller.text.trim();
                  if (tag.isNotEmpty && !_tags.contains(tag)) {
                    setState(() {
                      _tags.add(tag);
                    });
                  }
                  Navigator.pop(context);
                },
                child: const Text('Ajouter'),
              ),
            ],
          ),
    );
  }

  Future<void> _saveTask() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final task = AdvancedTask(
        id: widget.task?.id ?? '',
        projectId: _selectedProjectId ?? '',
        sectionId: _selectedSectionId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority,
        status: _selectedStatus,
        createdAt: widget.task?.createdAt ?? DateTime.now(),
        startDate: _startDate,
        dueDate: _dueDate,
        assigneeId: _selectedAssigneeId,
        tags: _tags,
        estimatedHours: _estimatedHours,
        order: widget.task?.order ?? 0,
      );

      widget.onTaskSaved(task);
      Navigator.pop(context);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur lors de la sauvegarde: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
